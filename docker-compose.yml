services:
  mongodb:
    image: mongo:7.0
    container_name: supermarket_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-supermarket_data}
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - supermarket_network

  api:
    build:
      context: .
      dockerfile: docker/api/Dockerfile
    container_name: supermarket_api
    restart: unless-stopped
    environment:
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/${MONGO_DATABASE:-supermarket_data}?authSource=admin
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - API_HOST=${API_HOST:-0.0.0.0}
      - API_PORT=${API_PORT:-8000}
    ports:
      - "8500:8000"
    depends_on:
      - mongodb
    volumes:
      - ./api:/app/api
      - ./config:/app/config
    networks:
      - supermarket_network

  scraper:
    build:
      context: .
      dockerfile: docker/scraper/Dockerfile
    container_name: supermarket_scraper
    restart: unless-stopped
    environment:
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/${MONGO_DATABASE:-supermarket_data}?authSource=admin
      - SCRAPING_INTERVAL=${SCRAPING_INTERVAL:-3600}
      - RATE_LIMIT_DELAY=${RATE_LIMIT_DELAY:-2}
      - USER_AGENT_ROTATION=${USER_AGENT_ROTATION:-true}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - HEADLESS_BROWSER=${HEADLESS_BROWSER:-true}
    depends_on:
      - mongodb
    volumes:
      - ./scrapers:/app/scrapers
      - ./config:/app/config
      - ./data:/app/data
    networks:
      - supermarket_network

  redis:
    image: redis:7.2-alpine
    container_name: supermarket_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - supermarket_network

  scheduler:
    build:
      context: .
      dockerfile: docker/scheduler/Dockerfile
    container_name: supermarket_scheduler
    restart: unless-stopped
    environment:
      - MONGODB_URL=mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/${MONGO_DATABASE:-supermarket_data}?authSource=admin
      - REDIS_URL=redis://redis:6379
      - SCRAPING_SCHEDULE=${SCRAPING_SCHEDULE:-0 */6 * * *}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./scheduler:/app/scheduler
      - ./config:/app/config
    networks:
      - supermarket_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: supermarket_frontend
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://localhost:8500
    ports:
      - "3500:3000"
    depends_on:
      - api
    networks:
      - supermarket_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3500/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  supermarket_network:
    driver: bridge
