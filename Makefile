# Makefile for Argentine Supermarket Scraper

.PHONY: help build up down logs clean test setup-db run-scraper

# Default target
help:
	@echo "Available commands:"
	@echo "  build        - Build Docker images"
	@echo "  up           - Start all services"
	@echo "  down         - Stop all services"
	@echo "  logs         - Show logs from all services"
	@echo "  clean        - Clean up Docker resources"
	@echo "  test         - Run tests"
	@echo "  setup-db     - Setup database"
	@echo "  run-scraper  - Run scraper manually"
	@echo "  install      - Install Python dependencies"

# Docker commands
build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

logs:
	docker-compose logs -f

clean:
	docker-compose down -v
	docker system prune -f

# Development commands
install:
	pip install -r requirements.txt

setup-db:
	python scripts/setup_database.py

run-scraper:
	python scripts/run_scraper.py

test:
	python -m pytest tests/ -v

# API commands
run-api:
	uvicorn api.main:app --reload --host 0.0.0.0 --port 8000

# <PERSON>raper commands
run-coto:
	python scripts/run_scraper.py --market coto

run-carrefour:
	python scripts/run_scraper.py --market carrefour

run-jumbo:
	python scripts/run_scraper.py --market jumbo

run-disco:
	python scripts/run_scraper.py --market disco

run-vea:
	python scripts/run_scraper.py --market vea

# Utility commands
check-env:
	@echo "Checking environment configuration..."
	@python -c "from config import settings; print('✓ Configuration loaded successfully')"

format:
	black .
	flake8 .

# Production commands
deploy:
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

backup-db:
	docker exec supermarket_mongodb mongodump --out /data/backup/$(shell date +%Y%m%d_%H%M%S)

restore-db:
	@echo "Usage: make restore-db BACKUP_DIR=<backup_directory>"
	docker exec supermarket_mongodb mongorestore /data/backup/$(BACKUP_DIR)
