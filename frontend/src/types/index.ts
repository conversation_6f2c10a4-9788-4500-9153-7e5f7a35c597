// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// Health Check Types
export interface HealthStatus {
  status: string;
  timestamp: string;
  database: {
    status: string;
    total_products: number;
    products_by_market: Record<string, number>;
    last_update: string | null;
  };
  api: {
    version: string;
    endpoints_available: boolean;
  };
}

// Market Types
export interface Market {
  name: string;
  display_name: string;
  product_count: number;
  average_price: number;
  last_scraped?: string;
  status?: 'active' | 'inactive' | 'scraping';
}

// Product Types
export interface Product {
  id: string;
  name: string;
  brand?: string;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  market: string;
  category: string;
  subcategory?: string;
  image_url?: string;
  product_url?: string;
  availability: boolean;
  promotion?: string;
  unit?: string;
  scraped_at: string;
  updated_at: string;
}

// Search and Filter Types
export interface ProductFilters {
  market?: string[];
  category?: string[];
  min_price?: number;
  max_price?: number;
  availability?: boolean;
  on_promotion?: boolean;
  brand?: string[];
}

export interface SearchParams extends ProductFilters {
  query?: string;
  page?: number;
  limit?: number;
  sort_by?: 'price' | 'name' | 'discount' | 'updated_at';
  sort_order?: 'asc' | 'desc';
}

export interface SearchResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
  filters_applied: ProductFilters;
}

// Category Types
export interface Category {
  name: string;
  display_name: string;
  product_count: number;
  subcategories?: string[];
}

// Price Comparison Types
export interface PriceComparison {
  product_name: string;
  prices: Array<{
    market: string;
    price: number;
    original_price?: number;
    discount_percentage?: number;
    availability: boolean;
    promotion?: string;
    product_url?: string;
    updated_at: string;
  }>;
  best_price: {
    market: string;
    price: number;
    savings?: number;
  };
  average_price: number;
  price_range: {
    min: number;
    max: number;
  };
}

// Statistics Types
export interface MarketStats {
  market: string;
  total_products: number;
  categories: Category[];
  average_price: number;
  price_range: {
    min: number;
    max: number;
  };
  last_scraped: string;
  scraping_frequency: string;
  top_categories: Array<{
    name: string;
    product_count: number;
  }>;
  recent_products: Product[];
}

// Scraper Control Types
export interface ScrapingSession {
  id: string;
  market: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at?: string;
  products_scraped: number;
  errors: number;
  categories: string[];
  progress_percentage: number;
  current_category?: string;
  estimated_completion?: string;
}

export interface ScrapingConfig {
  market: string;
  categories?: string[];
  rate_limit_delay?: number;
  max_products?: number;
  headless?: boolean;
}

// UI State Types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Chart Data Types
export interface ChartDataPoint {
  name: string;
  value: number;
  market?: string;
  color?: string;
}

export interface PriceHistoryPoint {
  date: string;
  price: number;
  market: string;
}

// Navigation Types
export interface NavItem {
  name: string;
  href: string;
  icon?: React.ComponentType<any>;
  current?: boolean;
}

// Form Types
export interface SearchFormData {
  query: string;
  markets: string[];
  categories: string[];
  minPrice: string;
  maxPrice: string;
  availability: boolean;
  onPromotion: boolean;
}

// Error Types
export interface ApiError {
  message: string;
  status?: number;
  details?: any;
}

// Utility Types
export type MarketName = 'coto' | 'carrefour' | 'jumbo' | 'disco' | 'vea';

export type SortOption = {
  value: string;
  label: string;
  field: string;
  order: 'asc' | 'desc';
};

export type ViewMode = 'grid' | 'list' | 'table';
