import React, { useState } from 'react';
import {
  PlayIcon,
  StopIcon,
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { ScraperStatus, MarketName } from '../../types';

interface ScraperControlsProps {
  scrapers: ScraperStatus[];
  onStartScraper: (market: MarketName) => void;
  onStopScraper: (market: MarketName) => void;
  onStartAll: () => void;
  onStopAll: () => void;
  onConfigChange: (market: MarketName, config: any) => void;
}

const ScraperControls: React.FC<ScraperControlsProps> = ({
  scrapers,
  onStartScraper,
  onStopScraper,
  onStartAll,
  onStopAll,
  onConfigChange,
}) => {
  const [expandedConfig, setExpandedConfig] = useState<MarketName | null>(null);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <StopIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const marketDisplayNames: Record<string, string> = {
    coto: 'Coto Digital',
    carrefour: 'Carrefour',
    jumbo: 'Jumbo',
    disco: 'Disco',
    vea: 'Vea',
  };

  return (
    <div className="space-y-6">
      {/* Header with Global Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Scraper Controls</h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage individual market scrapers and configurations
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={onStartAll}
            className="btn btn-primary btn-sm"
          >
            <PlayIcon className="h-4 w-4 mr-2" />
            Start All
          </button>
          <button
            onClick={onStopAll}
            className="btn btn-outline btn-sm"
          >
            <StopIcon className="h-4 w-4 mr-2" />
            Stop All
          </button>
        </div>
      </div>

      {/* Individual Scraper Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {scrapers.map((scraper) => (
          <div key={scraper.market} className="card">
            <div className="card-body">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  {getStatusIcon(scraper.status)}
                  <h3 className="ml-2 text-sm font-medium text-gray-900">
                    {marketDisplayNames[scraper.market]}
                  </h3>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(scraper.status)}`}>
                  {scraper.status.charAt(0).toUpperCase() + scraper.status.slice(1)}
                </span>
              </div>

              {/* Stats */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Products Scraped:</span>
                  <span className="font-medium">{scraper.productsScraped.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Errors:</span>
                  <span className="font-medium text-red-600">{scraper.errors}</span>
                </div>
                {scraper.lastRun && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Last Run:</span>
                    <span className="font-medium">{new Date(scraper.lastRun).toLocaleString()}</span>
                  </div>
                )}
              </div>

              {/* Controls */}
              <div className="flex space-x-2 mb-3">
                {scraper.status === 'running' ? (
                  <button
                    onClick={() => onStopScraper(scraper.market)}
                    className="flex-1 btn btn-outline btn-sm"
                  >
                    <StopIcon className="h-4 w-4 mr-1" />
                    Stop
                  </button>
                ) : (
                  <button
                    onClick={() => onStartScraper(scraper.market)}
                    className="flex-1 btn btn-primary btn-sm"
                  >
                    <PlayIcon className="h-4 w-4 mr-1" />
                    Start
                  </button>
                )}
                <button
                  onClick={() => setExpandedConfig(
                    expandedConfig === scraper.market ? null : scraper.market
                  )}
                  className="btn btn-outline btn-sm"
                >
                  <CogIcon className="h-4 w-4" />
                </button>
              </div>

              {/* Configuration Panel */}
              {expandedConfig === scraper.market && (
                <div className="border-t pt-3 space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Frequency
                    </label>
                    <select
                      value={scraper.config.frequency || 'daily'}
                      onChange={(e) => onConfigChange(scraper.market, {
                        ...scraper.config,
                        frequency: e.target.value
                      })}
                      className="w-full text-xs border border-gray-300 rounded-md px-2 py-1"
                    >
                      <option value="hourly">Hourly</option>
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Timeout (seconds)
                    </label>
                    <input
                      type="number"
                      value={scraper.config.timeout || 30}
                      onChange={(e) => onConfigChange(scraper.market, {
                        ...scraper.config,
                        timeout: parseInt(e.target.value)
                      })}
                      className="w-full text-xs border border-gray-300 rounded-md px-2 py-1"
                      min="10"
                      max="300"
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Max Pages
                    </label>
                    <input
                      type="number"
                      value={scraper.config.max_pages || 10}
                      onChange={(e) => onConfigChange(scraper.market, {
                        ...scraper.config,
                        max_pages: parseInt(e.target.value)
                      })}
                      className="w-full text-xs border border-gray-300 rounded-md px-2 py-1"
                      min="1"
                      max="100"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Coming Soon Badge */}
      <div className="text-center">
        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
          <ClockIcon className="h-4 w-4 mr-1" />
          Backend Integration Coming Soon - v2.0
        </span>
      </div>
    </div>
  );
};

export default ScraperControls;
