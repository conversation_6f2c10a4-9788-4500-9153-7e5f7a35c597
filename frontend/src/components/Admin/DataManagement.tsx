import React, { useState } from 'react';
import {
  CircleStackIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  TrashIcon,
  WrenchScrewdriverIcon,
  ClockIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { DatabaseStats, MarketName } from '../../types';

interface DataManagementProps {
  stats: DatabaseStats;
  onExportData: (market?: MarketName) => void;
  onImportData: (file: File) => void;
  onCleanupData: (options: any) => void;
  onOptimizeDatabase: () => void;
}

const DataManagement: React.FC<DataManagementProps> = ({
  stats,
  onExportData,
  onImportData,
  onCleanupData,
  onOptimizeDatabase,
}) => {
  const [showCleanupModal, setShowCleanupModal] = useState(false);
  const [cleanupOptions, setCleanupOptions] = useState({
    olderThanDays: 30,
    markets: [] as MarketName[],
    duplicates: true,
    invalidData: true,
  });

  const marketDisplayNames: Record<string, string> = {
    coto: 'Coto Digital',
    carrefour: 'Carrefour',
    jumbo: 'Jumbo',
    disco: 'Disco',
    vea: 'Vea',
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onImportData(file);
    }
  };

  const handleCleanup = () => {
    onCleanupData(cleanupOptions);
    setShowCleanupModal(false);
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-lg font-medium text-gray-900">Data Management</h2>
        <p className="mt-1 text-sm text-gray-500">
          Manage product database, exports, imports, and cleanup operations
        </p>
      </div>

      {/* Database Statistics */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center">
            <CircleStackIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Database Statistics</h3>
          </div>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Total Products */}
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {stats.totalProducts.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500">Total Products</div>
            </div>

            {/* Database Size */}
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">
                {stats.databaseSize}
              </div>
              <div className="text-sm text-gray-500">Database Size</div>
            </div>

            {/* Last Updated */}
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">
                {new Date(stats.lastUpdated).toLocaleDateString()}
              </div>
              <div className="text-sm text-gray-500">Last Updated</div>
            </div>
          </div>

          {/* Products by Market */}
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Products by Market</h4>
            <div className="space-y-3">
              {Object.entries(stats.productsByMarket).map(([market, count]) => (
                <div key={market} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">
                    {marketDisplayNames[market as MarketName]}
                  </span>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-900">
                      {count.toLocaleString()}
                    </span>
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{
                          width: `${(count / Math.max(...Object.values(stats.productsByMarket))) * 100}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Export/Import Operations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Export Data */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center">
              <ArrowDownTrayIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Export Data</h3>
            </div>
          </div>
          <div className="card-body space-y-4">
            <p className="text-sm text-gray-600">
              Export product data in JSON or CSV format for backup or analysis.
            </p>
            
            <div className="space-y-3">
              <button
                onClick={() => onExportData()}
                className="w-full btn btn-outline"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Export All Products
              </button>
              
              <div className="grid grid-cols-2 gap-2">
                {Object.keys(marketDisplayNames).map((market) => (
                  <button
                    key={market}
                    onClick={() => onExportData(market as MarketName)}
                    className="btn btn-outline btn-sm"
                  >
                    {marketDisplayNames[market as MarketName]}
                  </button>
                ))}
              </div>
            </div>

            <div className="text-center">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <ClockIcon className="h-3 w-3 mr-1" />
                Coming Soon
              </span>
            </div>
          </div>
        </div>

        {/* Import Data */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center">
              <ArrowUpTrayIcon className="h-5 w-5 text-gray-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Import Data</h3>
            </div>
          </div>
          <div className="card-body space-y-4">
            <p className="text-sm text-gray-600">
              Import product data from JSON or CSV files. Existing products will be updated.
            </p>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <ArrowUpTrayIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <label className="cursor-pointer">
                <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                  Choose file to upload
                </span>
                <input
                  type="file"
                  className="hidden"
                  accept=".json,.csv"
                  onChange={handleFileImport}
                />
              </label>
              <p className="text-xs text-gray-500 mt-1">
                JSON or CSV files up to 100MB
              </p>
            </div>

            <div className="text-center">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <ClockIcon className="h-3 w-3 mr-1" />
                Coming Soon
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Database Operations */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center">
            <WrenchScrewdriverIcon className="h-5 w-5 text-gray-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Database Operations</h3>
          </div>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Cleanup Operations */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Cleanup Operations</h4>
              <p className="text-sm text-gray-600">
                Remove old or invalid data to optimize database performance.
              </p>
              <button
                onClick={() => setShowCleanupModal(true)}
                className="w-full btn btn-outline text-red-600 hover:text-red-700"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                Configure Cleanup
              </button>
            </div>

            {/* Optimization */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-900">Database Optimization</h4>
              <p className="text-sm text-gray-600">
                Optimize database indexes and performance for faster queries.
              </p>
              <button
                onClick={onOptimizeDatabase}
                className="w-full btn btn-primary"
              >
                <WrenchScrewdriverIcon className="h-4 w-4 mr-2" />
                Optimize Database
              </button>
            </div>
          </div>

          <div className="mt-6 text-center">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              <ClockIcon className="h-4 w-4 mr-1" />
              Database Operations Coming Soon - v2.0
            </span>
          </div>
        </div>
      </div>

      {/* Cleanup Modal */}
      {showCleanupModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center mb-4">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Configure Data Cleanup</h3>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Delete data older than (days)
                  </label>
                  <input
                    type="number"
                    value={cleanupOptions.olderThanDays}
                    onChange={(e) => setCleanupOptions({
                      ...cleanupOptions,
                      olderThanDays: parseInt(e.target.value)
                    })}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    min="1"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Markets to clean
                  </label>
                  <div className="space-y-2">
                    {Object.entries(marketDisplayNames).map(([market, name]) => (
                      <label key={market} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={cleanupOptions.markets.includes(market as MarketName)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setCleanupOptions({
                                ...cleanupOptions,
                                markets: [...cleanupOptions.markets, market as MarketName]
                              });
                            } else {
                              setCleanupOptions({
                                ...cleanupOptions,
                                markets: cleanupOptions.markets.filter(m => m !== market)
                              });
                            }
                          }}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">{name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={cleanupOptions.duplicates}
                      onChange={(e) => setCleanupOptions({
                        ...cleanupOptions,
                        duplicates: e.target.checked
                      })}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Remove duplicate products</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={cleanupOptions.invalidData}
                      onChange={(e) => setCleanupOptions({
                        ...cleanupOptions,
                        invalidData: e.target.checked
                      })}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Remove invalid data</span>
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowCleanupModal(false)}
                  className="btn btn-outline"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCleanup}
                  className="btn btn-primary bg-red-600 hover:bg-red-700"
                >
                  Start Cleanup
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataManagement;
