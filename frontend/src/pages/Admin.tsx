import React, { useState, useEffect } from 'react';
import {
  CogIcon,
  ChartBarIcon,
  ServerIcon,
  CircleStackIcon,
  DocumentTextIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';

// Import the new admin components
import ScraperControls from '../components/Admin/ScraperControls';
import ScrapingLogs from '../components/Admin/ScrapingLogs';
import SystemConfiguration from '../components/Admin/SystemConfiguration';
import PerformanceMonitoring from '../components/Admin/PerformanceMonitoring';
import DataManagement from '../components/Admin/DataManagement';

// Import types
import {
  ScraperStatus,
  LogEntry,
  SystemMetrics,
  DatabaseStats,
  MarketName
} from '../types';

const Admin: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - in real implementation, this would come from API calls
  const [scraperStatuses, setScraperStatuses] = useState<ScraperStatus[]>([
    {
      market: 'coto',
      status: 'running',
      lastRun: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 2).toISOString(), // 2 hours from now
      productsScraped: 1247,
      errors: 3,
      config: {
        market: 'coto',
        frequency: 'daily',
        timeout: 30,
        max_pages: 10,
        rate_limit_delay: 2000,
      }
    },
    {
      market: 'carrefour',
      status: 'stopped',
      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
      productsScraped: 892,
      errors: 1,
      config: {
        market: 'carrefour',
        frequency: 'daily',
        timeout: 45,
        max_pages: 15,
        rate_limit_delay: 1500,
      }
    },
    {
      market: 'jumbo',
      status: 'error',
      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      productsScraped: 634,
      errors: 12,
      config: {
        market: 'jumbo',
        frequency: 'daily',
        timeout: 60,
        max_pages: 12,
        rate_limit_delay: 2000,
      }
    },
    {
      market: 'disco',
      status: 'stopped',
      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
      productsScraped: 445,
      errors: 0,
      config: {
        market: 'disco',
        frequency: 'weekly',
        timeout: 30,
        max_pages: 8,
        rate_limit_delay: 2500,
      }
    },
    {
      market: 'vea',
      status: 'running',
      lastRun: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 1.5).toISOString(), // 1.5 hours from now
      productsScraped: 723,
      errors: 2,
      config: {
        market: 'vea',
        frequency: 'daily',
        timeout: 40,
        max_pages: 10,
        rate_limit_delay: 2000,
      }
    },
  ]);

  const [logs, setLogs] = useState<LogEntry[]>([
    {
      id: '1',
      timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
      market: 'coto',
      level: 'info',
      message: 'Successfully scraped 45 products from category "Lácteos"',
      details: { category: 'Lácteos', products: 45, duration: '2.3s' }
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 1000 * 60 * 12).toISOString(),
      market: 'carrefour',
      level: 'warning',
      message: 'High response time detected (3.2s average)',
      details: { averageResponseTime: '3.2s', threshold: '2.0s' }
    },
    {
      id: '3',
      timestamp: new Date(Date.now() - 1000 * 60 * 18).toISOString(),
      market: 'jumbo',
      level: 'error',
      message: 'Failed to load product page: Connection timeout',
      details: { url: 'https://jumbo.com.ar/products/page/5', error: 'TIMEOUT' }
    },
    {
      id: '4',
      timestamp: new Date(Date.now() - 1000 * 60 * 25).toISOString(),
      market: 'vea',
      level: 'info',
      message: 'Scraping session completed successfully',
      details: { totalProducts: 156, duration: '4m 32s', errors: 0 }
    },
    {
      id: '5',
      timestamp: new Date(Date.now() - 1000 * 60 * 35).toISOString(),
      market: 'disco',
      level: 'warning',
      message: 'Product price format changed, using fallback parser',
      details: { oldFormat: '$XX.XX', newFormat: 'XX,XX $', fallbackUsed: true }
    },
  ]);

  const systemMetrics: SystemMetrics = {
    productsScrapedLast24h: 3941,
    successRate: 94.2,
    averageResponseTime: 1247,
    cpuUsage: 34.5,
    memoryUsage: 67.8,
    databaseSize: '2.4 GB',
  };

  const databaseStats: DatabaseStats = {
    totalProducts: 15847,
    productsByMarket: {
      coto: 4521,
      carrefour: 3892,
      jumbo: 3234,
      disco: 2145,
      vea: 2055,
    },
    lastUpdated: new Date().toISOString(),
    databaseSize: '2.4 GB',
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Admin Panel
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage scrapers, monitor performance, and configure system settings
            </p>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 overflow-x-auto">
            {[
              { id: 'overview', name: 'Overview', icon: ChartBarIcon },
              { id: 'scrapers', name: 'Scrapers', icon: CogIcon },
              { id: 'logs', name: 'Logs', icon: DocumentTextIcon },
              { id: 'performance', name: 'Performance', icon: ChartBarIcon },
              { id: 'config', name: 'Configuration', icon: ServerIcon },
              { id: 'data', name: 'Data Management', icon: CircleStackIcon },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Mock handlers - in real implementation, these would make API calls */}
        {(() => {
          const handleStartScraper = (market: MarketName) => {
            console.log(`Starting scraper for ${market}`);
            setScraperStatuses(prev =>
              prev.map(s => s.market === market ? { ...s, status: 'running' as const } : s)
            );
          };

          const handleStopScraper = (market: MarketName) => {
            console.log(`Stopping scraper for ${market}`);
            setScraperStatuses(prev =>
              prev.map(s => s.market === market ? { ...s, status: 'stopped' as const } : s)
            );
          };

          const handleStartAll = () => {
            console.log('Starting all scrapers');
            setScraperStatuses(prev =>
              prev.map(s => ({ ...s, status: 'running' as const }))
            );
          };

          const handleStopAll = () => {
            console.log('Stopping all scrapers');
            setScraperStatuses(prev =>
              prev.map(s => ({ ...s, status: 'stopped' as const }))
            );
          };

          const handleConfigChange = (market: MarketName, config: any) => {
            console.log(`Updating config for ${market}:`, config);
            setScraperStatuses(prev =>
              prev.map(s => s.market === market ? { ...s, config } : s)
            );
          };

          const handleExportLogs = () => console.log('Exporting logs');
          const handleClearLogs = () => { console.log('Clearing logs'); setLogs([]); };
          const handleRefreshLogs = () => console.log('Refreshing logs');
          const handleSaveConfig = (config: any) => console.log('Saving system configuration:', config);
          const handleExportData = (market?: MarketName) => console.log(`Exporting data${market ? ` for ${market}` : ''}`);
          const handleImportData = (file: File) => console.log('Importing data from file:', file.name);
          const handleCleanupData = (options: any) => console.log('Cleaning up data with options:', options);
          const handleOptimizeDatabase = () => console.log('Optimizing database');

          return null; // This is just to define the handlers in scope
        })()}

        {/* Tab Content */}
        <div className="space-y-8">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Quick Stats Cards */}
              <div className="card">
                <div className="card-body">
                  <div className="flex items-center mb-4">
                    <CogIcon className="h-8 w-8 text-blue-500 mr-3" />
                    <h2 className="text-xl font-semibold text-gray-900">
                      Active Scrapers
                    </h2>
                  </div>
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {scraperStatuses.filter(s => s.status === 'running').length}
                  </div>
                  <p className="text-sm text-gray-600">
                    of {scraperStatuses.length} total scrapers
                  </p>
                </div>
              </div>

              <div className="card">
                <div className="card-body">
                  <div className="flex items-center mb-4">
                    <ChartBarIcon className="h-8 w-8 text-green-500 mr-3" />
                    <h2 className="text-xl font-semibold text-gray-900">
                      Products Today
                    </h2>
                  </div>
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {systemMetrics.productsScrapedLast24h.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">
                    {systemMetrics.successRate}% success rate
                  </p>
                </div>
              </div>

              <div className="card">
                <div className="card-body">
                  <div className="flex items-center mb-4">
                    <CircleStackIcon className="h-8 w-8 text-purple-500 mr-3" />
                    <h2 className="text-xl font-semibold text-gray-900">
                      Total Products
                    </h2>
                  </div>
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    {databaseStats.totalProducts.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">
                    {databaseStats.databaseSize} database size
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'scrapers' && (
            <ScraperControls
              scrapers={scraperStatuses}
              onStartScraper={(market: MarketName) => {
                console.log(`Starting scraper for ${market}`);
                setScraperStatuses(prev =>
                  prev.map(s => s.market === market ? { ...s, status: 'running' as const } : s)
                );
              }}
              onStopScraper={(market: MarketName) => {
                console.log(`Stopping scraper for ${market}`);
                setScraperStatuses(prev =>
                  prev.map(s => s.market === market ? { ...s, status: 'stopped' as const } : s)
                );
              }}
              onStartAll={() => {
                console.log('Starting all scrapers');
                setScraperStatuses(prev =>
                  prev.map(s => ({ ...s, status: 'running' as const }))
                );
              }}
              onStopAll={() => {
                console.log('Stopping all scrapers');
                setScraperStatuses(prev =>
                  prev.map(s => ({ ...s, status: 'stopped' as const }))
                );
              }}
              onConfigChange={(market: MarketName, config: any) => {
                console.log(`Updating config for ${market}:`, config);
                setScraperStatuses(prev =>
                  prev.map(s => s.market === market ? { ...s, config } : s)
                );
              }}
            />
          )}

          {activeTab === 'logs' && (
            <ScrapingLogs
              logs={logs}
              onExportLogs={() => console.log('Exporting logs')}
              onClearLogs={() => { console.log('Clearing logs'); setLogs([]); }}
              onRefresh={() => console.log('Refreshing logs')}
            />
          )}

          {activeTab === 'performance' && (
            <PerformanceMonitoring metrics={systemMetrics} />
          )}

          {activeTab === 'config' && (
            <SystemConfiguration onSaveConfig={(config: any) => console.log('Saving system configuration:', config)} />
          )}

          {activeTab === 'data' && (
            <DataManagement
              stats={databaseStats}
              onExportData={(market?: MarketName) => console.log(`Exporting data${market ? ` for ${market}` : ''}`)}
              onImportData={(file: File) => console.log('Importing data from file:', file.name)}
              onCleanupData={(options: any) => console.log('Cleaning up data with options:', options)}
              onOptimizeDatabase={() => console.log('Optimizing database')}
            />
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Admin;
