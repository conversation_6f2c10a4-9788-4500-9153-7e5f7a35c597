{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @param {Date|Number} date - the date to round\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.nearestTo=1] - nearest number of minutes to round to. E.g. `15` to round to quarter hours.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Date} the new date rounded to the closest minute\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.nearestTo` must be between 1 and 30\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:07:30 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * // rounds up because given date is exactly between 12:00:00 and 12:15:00\n * //=> Thu Jul 10 2014 12:15:00\n */\nexport default function roundToNearestMinutes(dirtyDate, options) {\n  var _options$nearestTo;\n  if (arguments.length < 1) {\n    throw new TypeError('1 argument required, but only none provided present');\n  }\n  var nearestTo = toInteger((_options$nearestTo = options === null || options === void 0 ? void 0 : options.nearestTo) !== null && _options$nearestTo !== void 0 ? _options$nearestTo : 1);\n  if (nearestTo < 1 || nearestTo > 30) {\n    throw new RangeError('`options.nearestTo` must be between 1 and 30');\n  }\n  var date = toDate(dirtyDate);\n  var seconds = date.getSeconds(); // relevant if nearestTo is 1, which is the default case\n  var minutes = date.getMinutes() + seconds / 60;\n  var roundingMethod = getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod);\n  var roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n  var remainderMinutes = minutes % nearestTo;\n  var addedMinutes = Math.round(remainderMinutes / nearestTo) * nearestTo;\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), roundedMinutes + addedMinutes);\n}", "map": {"version": 3, "names": ["toDate", "getRoundingMethod", "toInteger", "roundToNearestMinutes", "dirtyDate", "options", "_options$nearestTo", "arguments", "length", "TypeError", "nearestTo", "RangeError", "date", "seconds", "getSeconds", "minutes", "getMinutes", "roundingMethod", "roundedMinutes", "remainderMinutes", "addedMinutes", "Math", "round", "Date", "getFullYear", "getMonth", "getDate", "getHours"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/roundToNearestMinutes/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @param {Date|Number} date - the date to round\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.nearestTo=1] - nearest number of minutes to round to. E.g. `15` to round to quarter hours.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Date} the new date rounded to the closest minute\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.nearestTo` must be between 1 and 30\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:07:30 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * // rounds up because given date is exactly between 12:00:00 and 12:15:00\n * //=> Thu Jul 10 2014 12:15:00\n */\nexport default function roundToNearestMinutes(dirtyDate, options) {\n  var _options$nearestTo;\n  if (arguments.length < 1) {\n    throw new TypeError('1 argument required, but only none provided present');\n  }\n  var nearestTo = toInteger((_options$nearestTo = options === null || options === void 0 ? void 0 : options.nearestTo) !== null && _options$nearestTo !== void 0 ? _options$nearestTo : 1);\n  if (nearestTo < 1 || nearestTo > 30) {\n    throw new RangeError('`options.nearestTo` must be between 1 and 30');\n  }\n  var date = toDate(dirtyDate);\n  var seconds = date.getSeconds(); // relevant if nearestTo is 1, which is the default case\n  var minutes = date.getMinutes() + seconds / 60;\n  var roundingMethod = getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod);\n  var roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n  var remainderMinutes = minutes % nearestTo;\n  var addedMinutes = Math.round(remainderMinutes / nearestTo) * nearestTo;\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), roundedMinutes + addedMinutes);\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,qBAAqBA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAChE,IAAIC,kBAAkB;EACtB,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,qDAAqD,CAAC;EAC5E;EACA,IAAIC,SAAS,GAAGR,SAAS,CAAC,CAACI,kBAAkB,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,SAAS,MAAM,IAAI,IAAIJ,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC,CAAC;EACxL,IAAII,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,EAAE,EAAE;IACnC,MAAM,IAAIC,UAAU,CAAC,8CAA8C,CAAC;EACtE;EACA,IAAIC,IAAI,GAAGZ,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAIS,OAAO,GAAGD,IAAI,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC;EACjC,IAAIC,OAAO,GAAGH,IAAI,CAACI,UAAU,CAAC,CAAC,GAAGH,OAAO,GAAG,EAAE;EAC9C,IAAII,cAAc,GAAGhB,iBAAiB,CAACI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACY,cAAc,CAAC;EAChH,IAAIC,cAAc,GAAGD,cAAc,CAACF,OAAO,GAAGL,SAAS,CAAC,GAAGA,SAAS;EACpE,IAAIS,gBAAgB,GAAGJ,OAAO,GAAGL,SAAS;EAC1C,IAAIU,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACH,gBAAgB,GAAGT,SAAS,CAAC,GAAGA,SAAS;EACvE,OAAO,IAAIa,IAAI,CAACX,IAAI,CAACY,WAAW,CAAC,CAAC,EAAEZ,IAAI,CAACa,QAAQ,CAAC,CAAC,EAAEb,IAAI,CAACc,OAAO,CAAC,CAAC,EAAEd,IAAI,CAACe,QAAQ,CAAC,CAAC,EAAET,cAAc,GAAGE,YAAY,CAAC;AACtH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}