{"ast": null, "code": "var _excluded = [\"x1\", \"y1\", \"x2\", \"y2\", \"key\"],\n  _excluded2 = [\"offset\"];\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\n/**\n * @fileOverview Cartesian Grid\n */\nimport React from 'react';\nimport isFunction from 'lodash/isFunction';\nimport { warn } from '../util/LogUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { getCoordinatesOfGrid, getTicksOfAxis } from '../util/ChartUtils';\nimport { getTicks } from './getTicks';\nimport { CartesianAxis } from './CartesianAxis';\nimport { useArbitraryXAxis, useChartHeight, useChartWidth, useOffset, useYAxisWithFiniteDomainOrRandom } from '../context/chartLayoutContext';\n\n/**\n * The <CartesianGrid horizontal\n */\n\nvar Background = function Background(props) {\n  var fill = props.fill;\n  if (!fill || fill === 'none') {\n    return null;\n  }\n  var fillOpacity = props.fillOpacity,\n    x = props.x,\n    y = props.y,\n    width = props.width,\n    height = props.height,\n    ry = props.ry;\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    ry: ry,\n    width: width,\n    height: height,\n    stroke: \"none\",\n    fill: fill,\n    fillOpacity: fillOpacity,\n    className: \"recharts-cartesian-grid-bg\"\n  });\n};\nfunction renderLineItem(option, props) {\n  var lineItem;\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    // @ts-expect-error typescript does not see the props type when cloning an element\n    lineItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (isFunction(option)) {\n    lineItem = option(props);\n  } else {\n    var x1 = props.x1,\n      y1 = props.y1,\n      x2 = props.x2,\n      y2 = props.y2,\n      key = props.key,\n      others = _objectWithoutProperties(props, _excluded);\n    var _filterProps = filterProps(others, false),\n      __ = _filterProps.offset,\n      restOfFilteredProps = _objectWithoutProperties(_filterProps, _excluded2);\n    lineItem = /*#__PURE__*/React.createElement(\"line\", _extends({}, restOfFilteredProps, {\n      x1: x1,\n      y1: y1,\n      x2: x2,\n      y2: y2,\n      fill: \"none\",\n      key: key\n    }));\n  }\n  return lineItem;\n}\nfunction HorizontalGridLines(props) {\n  var x = props.x,\n    width = props.width,\n    _props$horizontal = props.horizontal,\n    horizontal = _props$horizontal === void 0 ? true : _props$horizontal,\n    horizontalPoints = props.horizontalPoints;\n  if (!horizontal || !horizontalPoints || !horizontalPoints.length) {\n    return null;\n  }\n  var items = horizontalPoints.map(function (entry, i) {\n    var lineItemProps = _objectSpread(_objectSpread({}, props), {}, {\n      x1: x,\n      y1: entry,\n      x2: x + width,\n      y2: entry,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(horizontal, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-horizontal\"\n  }, items);\n}\nfunction VerticalGridLines(props) {\n  var y = props.y,\n    height = props.height,\n    _props$vertical = props.vertical,\n    vertical = _props$vertical === void 0 ? true : _props$vertical,\n    verticalPoints = props.verticalPoints;\n  if (!vertical || !verticalPoints || !verticalPoints.length) {\n    return null;\n  }\n  var items = verticalPoints.map(function (entry, i) {\n    var lineItemProps = _objectSpread(_objectSpread({}, props), {}, {\n      x1: entry,\n      y1: y,\n      x2: entry,\n      y2: y + height,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(vertical, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-vertical\"\n  }, items);\n}\nfunction HorizontalStripes(props) {\n  var horizontalFill = props.horizontalFill,\n    fillOpacity = props.fillOpacity,\n    x = props.x,\n    y = props.y,\n    width = props.width,\n    height = props.height,\n    horizontalPoints = props.horizontalPoints,\n    _props$horizontal2 = props.horizontal,\n    horizontal = _props$horizontal2 === void 0 ? true : _props$horizontal2;\n  if (!horizontal || !horizontalFill || !horizontalFill.length) {\n    return null;\n  }\n\n  // Why =y -y? I was trying to find any difference that this makes, with floating point numbers and edge cases but ... nothing.\n  var roundedSortedHorizontalPoints = horizontalPoints.map(function (e) {\n    return Math.round(e + y - y);\n  }).sort(function (a, b) {\n    return a - b;\n  });\n  // Why is this condition `!==` instead of `<=` ?\n  if (y !== roundedSortedHorizontalPoints[0]) {\n    roundedSortedHorizontalPoints.unshift(0);\n  }\n  var items = roundedSortedHorizontalPoints.map(function (entry, i) {\n    // Why do we strip only the last stripe if it is invisible, and not all invisible stripes?\n    var lastStripe = !roundedSortedHorizontalPoints[i + 1];\n    var lineHeight = lastStripe ? y + height - entry : roundedSortedHorizontalPoints[i + 1] - entry;\n    if (lineHeight <= 0) {\n      return null;\n    }\n    var colorIndex = i % horizontalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n\n      y: entry,\n      x: x,\n      height: lineHeight,\n      width: width,\n      stroke: \"none\",\n      fill: horizontalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-horizontal\"\n  }, items);\n}\nfunction VerticalStripes(props) {\n  var _props$vertical2 = props.vertical,\n    vertical = _props$vertical2 === void 0 ? true : _props$vertical2,\n    verticalFill = props.verticalFill,\n    fillOpacity = props.fillOpacity,\n    x = props.x,\n    y = props.y,\n    width = props.width,\n    height = props.height,\n    verticalPoints = props.verticalPoints;\n  if (!vertical || !verticalFill || !verticalFill.length) {\n    return null;\n  }\n  var roundedSortedVerticalPoints = verticalPoints.map(function (e) {\n    return Math.round(e + x - x);\n  }).sort(function (a, b) {\n    return a - b;\n  });\n  if (x !== roundedSortedVerticalPoints[0]) {\n    roundedSortedVerticalPoints.unshift(0);\n  }\n  var items = roundedSortedVerticalPoints.map(function (entry, i) {\n    var lastStripe = !roundedSortedVerticalPoints[i + 1];\n    var lineWidth = lastStripe ? x + width - entry : roundedSortedVerticalPoints[i + 1] - entry;\n    if (lineWidth <= 0) {\n      return null;\n    }\n    var colorIndex = i % verticalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n\n      x: entry,\n      y: y,\n      width: lineWidth,\n      height: height,\n      stroke: \"none\",\n      fill: verticalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-vertical\"\n  }, items);\n}\nvar defaultVerticalCoordinatesGenerator = function defaultVerticalCoordinatesGenerator(_ref, syncWithTicks) {\n  var xAxis = _ref.xAxis,\n    width = _ref.width,\n    height = _ref.height,\n    offset = _ref.offset;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), xAxis), {}, {\n    ticks: getTicksOfAxis(xAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width: width,\n      height: height\n    }\n  })), offset.left, offset.left + offset.width, syncWithTicks);\n};\nvar defaultHorizontalCoordinatesGenerator = function defaultHorizontalCoordinatesGenerator(_ref2, syncWithTicks) {\n  var yAxis = _ref2.yAxis,\n    width = _ref2.width,\n    height = _ref2.height,\n    offset = _ref2.offset;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), yAxis), {}, {\n    ticks: getTicksOfAxis(yAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width: width,\n      height: height\n    }\n  })), offset.top, offset.top + offset.height, syncWithTicks);\n};\nvar defaultProps = {\n  horizontal: true,\n  vertical: true,\n  // The ordinates of horizontal grid lines\n  horizontalPoints: [],\n  // The abscissas of vertical grid lines\n  verticalPoints: [],\n  stroke: '#ccc',\n  fill: 'none',\n  // The fill of colors of grid lines\n  verticalFill: [],\n  horizontalFill: []\n};\nexport function CartesianGrid(props) {\n  var _props$stroke, _props$fill, _props$horizontal3, _props$horizontalFill, _props$vertical3, _props$verticalFill;\n  var chartWidth = useChartWidth();\n  var chartHeight = useChartHeight();\n  var offset = useOffset();\n  var propsIncludingDefaults = _objectSpread(_objectSpread({}, props), {}, {\n    stroke: (_props$stroke = props.stroke) !== null && _props$stroke !== void 0 ? _props$stroke : defaultProps.stroke,\n    fill: (_props$fill = props.fill) !== null && _props$fill !== void 0 ? _props$fill : defaultProps.fill,\n    horizontal: (_props$horizontal3 = props.horizontal) !== null && _props$horizontal3 !== void 0 ? _props$horizontal3 : defaultProps.horizontal,\n    horizontalFill: (_props$horizontalFill = props.horizontalFill) !== null && _props$horizontalFill !== void 0 ? _props$horizontalFill : defaultProps.horizontalFill,\n    vertical: (_props$vertical3 = props.vertical) !== null && _props$vertical3 !== void 0 ? _props$vertical3 : defaultProps.vertical,\n    verticalFill: (_props$verticalFill = props.verticalFill) !== null && _props$verticalFill !== void 0 ? _props$verticalFill : defaultProps.verticalFill,\n    x: isNumber(props.x) ? props.x : offset.left,\n    y: isNumber(props.y) ? props.y : offset.top,\n    width: isNumber(props.width) ? props.width : offset.width,\n    height: isNumber(props.height) ? props.height : offset.height\n  });\n  var x = propsIncludingDefaults.x,\n    y = propsIncludingDefaults.y,\n    width = propsIncludingDefaults.width,\n    height = propsIncludingDefaults.height,\n    syncWithTicks = propsIncludingDefaults.syncWithTicks,\n    horizontalValues = propsIncludingDefaults.horizontalValues,\n    verticalValues = propsIncludingDefaults.verticalValues;\n\n  // @ts-expect-error the scale prop is mixed up - we need to untagle this at some point\n  var xAxis = useArbitraryXAxis();\n  // @ts-expect-error the scale prop is mixed up - we need to untagle this at some point\n  var yAxis = useYAxisWithFiniteDomainOrRandom();\n  if (!isNumber(width) || width <= 0 || !isNumber(height) || height <= 0 || !isNumber(x) || x !== +x || !isNumber(y) || y !== +y) {\n    return null;\n  }\n\n  /*\n   * verticalCoordinatesGenerator and horizontalCoordinatesGenerator are defined\n   * outside of the propsIncludingDefaults because they were never part of the original props\n   * and they were never passed as a prop down to horizontal/vertical custom elements.\n   * If we add these two to propsIncludingDefaults then we are changing public API.\n   * Not a bad thing per se but also not necessary.\n   */\n  var verticalCoordinatesGenerator = propsIncludingDefaults.verticalCoordinatesGenerator || defaultVerticalCoordinatesGenerator;\n  var horizontalCoordinatesGenerator = propsIncludingDefaults.horizontalCoordinatesGenerator || defaultHorizontalCoordinatesGenerator;\n  var horizontalPoints = propsIncludingDefaults.horizontalPoints,\n    verticalPoints = propsIncludingDefaults.verticalPoints;\n\n  // No horizontal points are specified\n  if ((!horizontalPoints || !horizontalPoints.length) && isFunction(horizontalCoordinatesGenerator)) {\n    var isHorizontalValues = horizontalValues && horizontalValues.length;\n    var generatorResult = horizontalCoordinatesGenerator({\n      yAxis: yAxis ? _objectSpread(_objectSpread({}, yAxis), {}, {\n        ticks: isHorizontalValues ? horizontalValues : yAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset: offset\n    }, isHorizontalValues ? true : syncWithTicks);\n    warn(Array.isArray(generatorResult), \"horizontalCoordinatesGenerator should return Array but instead it returned [\".concat(_typeof(generatorResult), \"]\"));\n    if (Array.isArray(generatorResult)) {\n      horizontalPoints = generatorResult;\n    }\n  }\n\n  // No vertical points are specified\n  if ((!verticalPoints || !verticalPoints.length) && isFunction(verticalCoordinatesGenerator)) {\n    var isVerticalValues = verticalValues && verticalValues.length;\n    var _generatorResult = verticalCoordinatesGenerator({\n      xAxis: xAxis ? _objectSpread(_objectSpread({}, xAxis), {}, {\n        ticks: isVerticalValues ? verticalValues : xAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset: offset\n    }, isVerticalValues ? true : syncWithTicks);\n    warn(Array.isArray(_generatorResult), \"verticalCoordinatesGenerator should return Array but instead it returned [\".concat(_typeof(_generatorResult), \"]\"));\n    if (Array.isArray(_generatorResult)) {\n      verticalPoints = _generatorResult;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid\"\n  }, /*#__PURE__*/React.createElement(Background, {\n    fill: propsIncludingDefaults.fill,\n    fillOpacity: propsIncludingDefaults.fillOpacity,\n    x: propsIncludingDefaults.x,\n    y: propsIncludingDefaults.y,\n    width: propsIncludingDefaults.width,\n    height: propsIncludingDefaults.height,\n    ry: propsIncludingDefaults.ry\n  }), /*#__PURE__*/React.createElement(HorizontalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    horizontalPoints: horizontalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })), /*#__PURE__*/React.createElement(VerticalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    verticalPoints: verticalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })), /*#__PURE__*/React.createElement(HorizontalStripes, _extends({}, propsIncludingDefaults, {\n    horizontalPoints: horizontalPoints\n  })), /*#__PURE__*/React.createElement(VerticalStripes, _extends({}, propsIncludingDefaults, {\n    verticalPoints: verticalPoints\n  })));\n}\nCartesianGrid.displayName = 'CartesianGrid';", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "_extends", "assign", "bind", "target", "source", "hasOwnProperty", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "React", "isFunction", "warn", "isNumber", "filterProps", "getCoordinatesOfGrid", "getTicksOfAxis", "getTicks", "<PERSON><PERSON>ian<PERSON><PERSON><PERSON>", "useArbitraryXAxis", "useChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>th", "useOffset", "useYAxisWithFiniteDomainOrRandom", "Background", "props", "fill", "fillOpacity", "x", "y", "width", "height", "ry", "createElement", "stroke", "className", "renderLineItem", "option", "lineItem", "isValidElement", "cloneElement", "x1", "y1", "x2", "y2", "others", "_filterProps", "__", "offset", "restOfFilteredProps", "HorizontalGridLines", "_props$horizontal", "horizontal", "horizontalPoints", "items", "map", "entry", "lineItemProps", "concat", "index", "VerticalGridLines", "_props$vertical", "vertical", "verticalPoints", "HorizontalStripes", "horizontalFill", "_props$horizontal2", "roundedSortedHorizontalPoints", "Math", "round", "sort", "a", "b", "unshift", "lastStripe", "lineHeight", "colorIndex", "VerticalStripes", "_props$vertical2", "verticalFill", "roundedSortedVerticalPoints", "lineWidth", "defaultVerticalCoordinatesGenerator", "_ref", "syncWithTicks", "xAxis", "defaultProps", "ticks", "viewBox", "left", "defaultHorizontalCoordinatesGenerator", "_ref2", "yAxis", "top", "Cartesian<PERSON><PERSON>", "_props$stroke", "_props$fill", "_props$horizontal3", "_props$horizontalFill", "_props$vertical3", "_props$verticalFill", "chartWidth", "chartHeight", "propsIncludingDefaults", "horizontalValues", "verticalValues", "verticalCoordinatesGenerator", "horizontalCoordinatesGenerator", "isHorizontalValues", "generatorResult", "undefined", "Array", "isArray", "isVerticalValues", "_generatorResult", "displayName"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/cartesian/CartesianGrid.js"], "sourcesContent": ["var _excluded = [\"x1\", \"y1\", \"x2\", \"y2\", \"key\"],\n  _excluded2 = [\"offset\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\n/**\n * @fileOverview Cartesian Grid\n */\nimport React from 'react';\nimport isFunction from 'lodash/isFunction';\nimport { warn } from '../util/LogUtils';\nimport { isNumber } from '../util/DataUtils';\nimport { filterProps } from '../util/ReactUtils';\nimport { getCoordinatesOfGrid, getTicksOfAxis } from '../util/ChartUtils';\nimport { getTicks } from './getTicks';\nimport { CartesianAxis } from './CartesianAxis';\nimport { useArbitraryXAxis, useChartHeight, useChartWidth, useOffset, useYAxisWithFiniteDomainOrRandom } from '../context/chartLayoutContext';\n\n/**\n * The <CartesianGrid horizontal\n */\n\nvar Background = function Background(props) {\n  var fill = props.fill;\n  if (!fill || fill === 'none') {\n    return null;\n  }\n  var fillOpacity = props.fillOpacity,\n    x = props.x,\n    y = props.y,\n    width = props.width,\n    height = props.height,\n    ry = props.ry;\n  return /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    ry: ry,\n    width: width,\n    height: height,\n    stroke: \"none\",\n    fill: fill,\n    fillOpacity: fillOpacity,\n    className: \"recharts-cartesian-grid-bg\"\n  });\n};\nfunction renderLineItem(option, props) {\n  var lineItem;\n  if ( /*#__PURE__*/React.isValidElement(option)) {\n    // @ts-expect-error typescript does not see the props type when cloning an element\n    lineItem = /*#__PURE__*/React.cloneElement(option, props);\n  } else if (isFunction(option)) {\n    lineItem = option(props);\n  } else {\n    var x1 = props.x1,\n      y1 = props.y1,\n      x2 = props.x2,\n      y2 = props.y2,\n      key = props.key,\n      others = _objectWithoutProperties(props, _excluded);\n    var _filterProps = filterProps(others, false),\n      __ = _filterProps.offset,\n      restOfFilteredProps = _objectWithoutProperties(_filterProps, _excluded2);\n    lineItem = /*#__PURE__*/React.createElement(\"line\", _extends({}, restOfFilteredProps, {\n      x1: x1,\n      y1: y1,\n      x2: x2,\n      y2: y2,\n      fill: \"none\",\n      key: key\n    }));\n  }\n  return lineItem;\n}\nfunction HorizontalGridLines(props) {\n  var x = props.x,\n    width = props.width,\n    _props$horizontal = props.horizontal,\n    horizontal = _props$horizontal === void 0 ? true : _props$horizontal,\n    horizontalPoints = props.horizontalPoints;\n  if (!horizontal || !horizontalPoints || !horizontalPoints.length) {\n    return null;\n  }\n  var items = horizontalPoints.map(function (entry, i) {\n    var lineItemProps = _objectSpread(_objectSpread({}, props), {}, {\n      x1: x,\n      y1: entry,\n      x2: x + width,\n      y2: entry,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(horizontal, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-horizontal\"\n  }, items);\n}\nfunction VerticalGridLines(props) {\n  var y = props.y,\n    height = props.height,\n    _props$vertical = props.vertical,\n    vertical = _props$vertical === void 0 ? true : _props$vertical,\n    verticalPoints = props.verticalPoints;\n  if (!vertical || !verticalPoints || !verticalPoints.length) {\n    return null;\n  }\n  var items = verticalPoints.map(function (entry, i) {\n    var lineItemProps = _objectSpread(_objectSpread({}, props), {}, {\n      x1: entry,\n      y1: y,\n      x2: entry,\n      y2: y + height,\n      key: \"line-\".concat(i),\n      index: i\n    });\n    return renderLineItem(vertical, lineItemProps);\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid-vertical\"\n  }, items);\n}\nfunction HorizontalStripes(props) {\n  var horizontalFill = props.horizontalFill,\n    fillOpacity = props.fillOpacity,\n    x = props.x,\n    y = props.y,\n    width = props.width,\n    height = props.height,\n    horizontalPoints = props.horizontalPoints,\n    _props$horizontal2 = props.horizontal,\n    horizontal = _props$horizontal2 === void 0 ? true : _props$horizontal2;\n  if (!horizontal || !horizontalFill || !horizontalFill.length) {\n    return null;\n  }\n\n  // Why =y -y? I was trying to find any difference that this makes, with floating point numbers and edge cases but ... nothing.\n  var roundedSortedHorizontalPoints = horizontalPoints.map(function (e) {\n    return Math.round(e + y - y);\n  }).sort(function (a, b) {\n    return a - b;\n  });\n  // Why is this condition `!==` instead of `<=` ?\n  if (y !== roundedSortedHorizontalPoints[0]) {\n    roundedSortedHorizontalPoints.unshift(0);\n  }\n  var items = roundedSortedHorizontalPoints.map(function (entry, i) {\n    // Why do we strip only the last stripe if it is invisible, and not all invisible stripes?\n    var lastStripe = !roundedSortedHorizontalPoints[i + 1];\n    var lineHeight = lastStripe ? y + height - entry : roundedSortedHorizontalPoints[i + 1] - entry;\n    if (lineHeight <= 0) {\n      return null;\n    }\n    var colorIndex = i % horizontalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n      y: entry,\n      x: x,\n      height: lineHeight,\n      width: width,\n      stroke: \"none\",\n      fill: horizontalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-horizontal\"\n  }, items);\n}\nfunction VerticalStripes(props) {\n  var _props$vertical2 = props.vertical,\n    vertical = _props$vertical2 === void 0 ? true : _props$vertical2,\n    verticalFill = props.verticalFill,\n    fillOpacity = props.fillOpacity,\n    x = props.x,\n    y = props.y,\n    width = props.width,\n    height = props.height,\n    verticalPoints = props.verticalPoints;\n  if (!vertical || !verticalFill || !verticalFill.length) {\n    return null;\n  }\n  var roundedSortedVerticalPoints = verticalPoints.map(function (e) {\n    return Math.round(e + x - x);\n  }).sort(function (a, b) {\n    return a - b;\n  });\n  if (x !== roundedSortedVerticalPoints[0]) {\n    roundedSortedVerticalPoints.unshift(0);\n  }\n  var items = roundedSortedVerticalPoints.map(function (entry, i) {\n    var lastStripe = !roundedSortedVerticalPoints[i + 1];\n    var lineWidth = lastStripe ? x + width - entry : roundedSortedVerticalPoints[i + 1] - entry;\n    if (lineWidth <= 0) {\n      return null;\n    }\n    var colorIndex = i % verticalFill.length;\n    return /*#__PURE__*/React.createElement(\"rect\", {\n      key: \"react-\".concat(i) // eslint-disable-line react/no-array-index-key\n      ,\n      x: entry,\n      y: y,\n      width: lineWidth,\n      height: height,\n      stroke: \"none\",\n      fill: verticalFill[colorIndex],\n      fillOpacity: fillOpacity,\n      className: \"recharts-cartesian-grid-bg\"\n    });\n  });\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-gridstripes-vertical\"\n  }, items);\n}\nvar defaultVerticalCoordinatesGenerator = function defaultVerticalCoordinatesGenerator(_ref, syncWithTicks) {\n  var xAxis = _ref.xAxis,\n    width = _ref.width,\n    height = _ref.height,\n    offset = _ref.offset;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), xAxis), {}, {\n    ticks: getTicksOfAxis(xAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width: width,\n      height: height\n    }\n  })), offset.left, offset.left + offset.width, syncWithTicks);\n};\nvar defaultHorizontalCoordinatesGenerator = function defaultHorizontalCoordinatesGenerator(_ref2, syncWithTicks) {\n  var yAxis = _ref2.yAxis,\n    width = _ref2.width,\n    height = _ref2.height,\n    offset = _ref2.offset;\n  return getCoordinatesOfGrid(getTicks(_objectSpread(_objectSpread(_objectSpread({}, CartesianAxis.defaultProps), yAxis), {}, {\n    ticks: getTicksOfAxis(yAxis, true),\n    viewBox: {\n      x: 0,\n      y: 0,\n      width: width,\n      height: height\n    }\n  })), offset.top, offset.top + offset.height, syncWithTicks);\n};\nvar defaultProps = {\n  horizontal: true,\n  vertical: true,\n  // The ordinates of horizontal grid lines\n  horizontalPoints: [],\n  // The abscissas of vertical grid lines\n  verticalPoints: [],\n  stroke: '#ccc',\n  fill: 'none',\n  // The fill of colors of grid lines\n  verticalFill: [],\n  horizontalFill: []\n};\nexport function CartesianGrid(props) {\n  var _props$stroke, _props$fill, _props$horizontal3, _props$horizontalFill, _props$vertical3, _props$verticalFill;\n  var chartWidth = useChartWidth();\n  var chartHeight = useChartHeight();\n  var offset = useOffset();\n  var propsIncludingDefaults = _objectSpread(_objectSpread({}, props), {}, {\n    stroke: (_props$stroke = props.stroke) !== null && _props$stroke !== void 0 ? _props$stroke : defaultProps.stroke,\n    fill: (_props$fill = props.fill) !== null && _props$fill !== void 0 ? _props$fill : defaultProps.fill,\n    horizontal: (_props$horizontal3 = props.horizontal) !== null && _props$horizontal3 !== void 0 ? _props$horizontal3 : defaultProps.horizontal,\n    horizontalFill: (_props$horizontalFill = props.horizontalFill) !== null && _props$horizontalFill !== void 0 ? _props$horizontalFill : defaultProps.horizontalFill,\n    vertical: (_props$vertical3 = props.vertical) !== null && _props$vertical3 !== void 0 ? _props$vertical3 : defaultProps.vertical,\n    verticalFill: (_props$verticalFill = props.verticalFill) !== null && _props$verticalFill !== void 0 ? _props$verticalFill : defaultProps.verticalFill,\n    x: isNumber(props.x) ? props.x : offset.left,\n    y: isNumber(props.y) ? props.y : offset.top,\n    width: isNumber(props.width) ? props.width : offset.width,\n    height: isNumber(props.height) ? props.height : offset.height\n  });\n  var x = propsIncludingDefaults.x,\n    y = propsIncludingDefaults.y,\n    width = propsIncludingDefaults.width,\n    height = propsIncludingDefaults.height,\n    syncWithTicks = propsIncludingDefaults.syncWithTicks,\n    horizontalValues = propsIncludingDefaults.horizontalValues,\n    verticalValues = propsIncludingDefaults.verticalValues;\n\n  // @ts-expect-error the scale prop is mixed up - we need to untagle this at some point\n  var xAxis = useArbitraryXAxis();\n  // @ts-expect-error the scale prop is mixed up - we need to untagle this at some point\n  var yAxis = useYAxisWithFiniteDomainOrRandom();\n  if (!isNumber(width) || width <= 0 || !isNumber(height) || height <= 0 || !isNumber(x) || x !== +x || !isNumber(y) || y !== +y) {\n    return null;\n  }\n\n  /*\n   * verticalCoordinatesGenerator and horizontalCoordinatesGenerator are defined\n   * outside of the propsIncludingDefaults because they were never part of the original props\n   * and they were never passed as a prop down to horizontal/vertical custom elements.\n   * If we add these two to propsIncludingDefaults then we are changing public API.\n   * Not a bad thing per se but also not necessary.\n   */\n  var verticalCoordinatesGenerator = propsIncludingDefaults.verticalCoordinatesGenerator || defaultVerticalCoordinatesGenerator;\n  var horizontalCoordinatesGenerator = propsIncludingDefaults.horizontalCoordinatesGenerator || defaultHorizontalCoordinatesGenerator;\n  var horizontalPoints = propsIncludingDefaults.horizontalPoints,\n    verticalPoints = propsIncludingDefaults.verticalPoints;\n\n  // No horizontal points are specified\n  if ((!horizontalPoints || !horizontalPoints.length) && isFunction(horizontalCoordinatesGenerator)) {\n    var isHorizontalValues = horizontalValues && horizontalValues.length;\n    var generatorResult = horizontalCoordinatesGenerator({\n      yAxis: yAxis ? _objectSpread(_objectSpread({}, yAxis), {}, {\n        ticks: isHorizontalValues ? horizontalValues : yAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset: offset\n    }, isHorizontalValues ? true : syncWithTicks);\n    warn(Array.isArray(generatorResult), \"horizontalCoordinatesGenerator should return Array but instead it returned [\".concat(_typeof(generatorResult), \"]\"));\n    if (Array.isArray(generatorResult)) {\n      horizontalPoints = generatorResult;\n    }\n  }\n\n  // No vertical points are specified\n  if ((!verticalPoints || !verticalPoints.length) && isFunction(verticalCoordinatesGenerator)) {\n    var isVerticalValues = verticalValues && verticalValues.length;\n    var _generatorResult = verticalCoordinatesGenerator({\n      xAxis: xAxis ? _objectSpread(_objectSpread({}, xAxis), {}, {\n        ticks: isVerticalValues ? verticalValues : xAxis.ticks\n      }) : undefined,\n      width: chartWidth,\n      height: chartHeight,\n      offset: offset\n    }, isVerticalValues ? true : syncWithTicks);\n    warn(Array.isArray(_generatorResult), \"verticalCoordinatesGenerator should return Array but instead it returned [\".concat(_typeof(_generatorResult), \"]\"));\n    if (Array.isArray(_generatorResult)) {\n      verticalPoints = _generatorResult;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"g\", {\n    className: \"recharts-cartesian-grid\"\n  }, /*#__PURE__*/React.createElement(Background, {\n    fill: propsIncludingDefaults.fill,\n    fillOpacity: propsIncludingDefaults.fillOpacity,\n    x: propsIncludingDefaults.x,\n    y: propsIncludingDefaults.y,\n    width: propsIncludingDefaults.width,\n    height: propsIncludingDefaults.height,\n    ry: propsIncludingDefaults.ry\n  }), /*#__PURE__*/React.createElement(HorizontalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    horizontalPoints: horizontalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })), /*#__PURE__*/React.createElement(VerticalGridLines, _extends({}, propsIncludingDefaults, {\n    offset: offset,\n    verticalPoints: verticalPoints,\n    xAxis: xAxis,\n    yAxis: yAxis\n  })), /*#__PURE__*/React.createElement(HorizontalStripes, _extends({}, propsIncludingDefaults, {\n    horizontalPoints: horizontalPoints\n  })), /*#__PURE__*/React.createElement(VerticalStripes, _extends({}, propsIncludingDefaults, {\n    verticalPoints: verticalPoints\n  })));\n}\nCartesianGrid.displayName = 'CartesianGrid';"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;EAC7CC,UAAU,GAAG,CAAC,QAAQ,CAAC;AACzB,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGS,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACY,MAAM,CAAC,UAAUL,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACO,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAER,CAAC,CAAC;EAAE;EAAE,OAAOQ,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACC,MAAM,EAAEZ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACX,CAAC,CAAC,GAAGW,SAAS,CAACX,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACI,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAAED,GAAG,GAAGE,cAAc,CAACF,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAID,GAAG,EAAE;IAAEhB,MAAM,CAACe,cAAc,CAACC,GAAG,EAAEC,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEb,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEL,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAAE;EAAE,OAAOF,GAAG;AAAE;AAC3O,SAASG,cAAcA,CAACpB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIT,OAAO,CAACgC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACxB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIR,OAAO,CAACS,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACP,MAAM,CAACgC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIyB,CAAC,GAAGzB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIR,OAAO,CAACgC,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AAC3T,SAAS8B,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAG7B,MAAM,CAAC8B,MAAM,GAAG9B,MAAM,CAAC8B,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,SAAS,CAACC,MAAM,EAAEY,CAAC,EAAE,EAAE;MAAE,IAAIW,MAAM,GAAGxB,SAAS,CAACa,CAAC,CAAC;MAAE,KAAK,IAAIL,GAAG,IAAIgB,MAAM,EAAE;QAAE,IAAIjC,MAAM,CAACL,SAAS,CAACuC,cAAc,CAACT,IAAI,CAACQ,MAAM,EAAEhB,GAAG,CAAC,EAAE;UAAEe,MAAM,CAACf,GAAG,CAAC,GAAGgB,MAAM,CAAChB,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOe,MAAM;EAAE,CAAC;EAAE,OAAOH,QAAQ,CAACtB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AAClV,SAAS0B,wBAAwBA,CAACF,MAAM,EAAEG,QAAQ,EAAE;EAAE,IAAIH,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,MAAM,GAAGK,6BAA6B,CAACJ,MAAM,EAAEG,QAAQ,CAAC;EAAE,IAAInB,GAAG,EAAEK,CAAC;EAAE,IAAItB,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIoC,gBAAgB,GAAGtC,MAAM,CAACE,qBAAqB,CAAC+B,MAAM,CAAC;IAAE,KAAKX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,gBAAgB,CAAC5B,MAAM,EAAEY,CAAC,EAAE,EAAE;MAAEL,GAAG,GAAGqB,gBAAgB,CAAChB,CAAC,CAAC;MAAE,IAAIc,QAAQ,CAACG,OAAO,CAACtB,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACjB,MAAM,CAACL,SAAS,CAAC6C,oBAAoB,CAACf,IAAI,CAACQ,MAAM,EAAEhB,GAAG,CAAC,EAAE;MAAUe,MAAM,CAACf,GAAG,CAAC,GAAGgB,MAAM,CAAChB,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOe,MAAM;AAAE;AAC3e,SAASK,6BAA6BA,CAACJ,MAAM,EAAEG,QAAQ,EAAE;EAAE,IAAIH,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAID,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIf,GAAG,IAAIgB,MAAM,EAAE;IAAE,IAAIjC,MAAM,CAACL,SAAS,CAACuC,cAAc,CAACT,IAAI,CAACQ,MAAM,EAAEhB,GAAG,CAAC,EAAE;MAAE,IAAImB,QAAQ,CAACG,OAAO,CAACtB,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUe,MAAM,CAACf,GAAG,CAAC,GAAGgB,MAAM,CAAChB,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOe,MAAM;AAAE;AACtR;AACA;AACA;AACA,OAAOS,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,oBAAoB;AACzE,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAEC,gCAAgC,QAAQ,+BAA+B;;AAE7I;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC1C,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;EACrB,IAAI,CAACA,IAAI,IAAIA,IAAI,KAAK,MAAM,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,IAAIC,WAAW,GAAGF,KAAK,CAACE,WAAW;IACjCC,CAAC,GAAGH,KAAK,CAACG,CAAC;IACXC,CAAC,GAAGJ,KAAK,CAACI,CAAC;IACXC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,EAAE,GAAGP,KAAK,CAACO,EAAE;EACf,OAAO,aAAatB,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;IAC9CL,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJG,EAAE,EAAEA,EAAE;IACNF,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdG,MAAM,EAAE,MAAM;IACdR,IAAI,EAAEA,IAAI;IACVC,WAAW,EAAEA,WAAW;IACxBQ,SAAS,EAAE;EACb,CAAC,CAAC;AACJ,CAAC;AACD,SAASC,cAAcA,CAACC,MAAM,EAAEZ,KAAK,EAAE;EACrC,IAAIa,QAAQ;EACZ,IAAK,aAAa5B,KAAK,CAAC6B,cAAc,CAACF,MAAM,CAAC,EAAE;IAC9C;IACAC,QAAQ,GAAG,aAAa5B,KAAK,CAAC8B,YAAY,CAACH,MAAM,EAAEZ,KAAK,CAAC;EAC3D,CAAC,MAAM,IAAId,UAAU,CAAC0B,MAAM,CAAC,EAAE;IAC7BC,QAAQ,GAAGD,MAAM,CAACZ,KAAK,CAAC;EAC1B,CAAC,MAAM;IACL,IAAIgB,EAAE,GAAGhB,KAAK,CAACgB,EAAE;MACfC,EAAE,GAAGjB,KAAK,CAACiB,EAAE;MACbC,EAAE,GAAGlB,KAAK,CAACkB,EAAE;MACbC,EAAE,GAAGnB,KAAK,CAACmB,EAAE;MACb1D,GAAG,GAAGuC,KAAK,CAACvC,GAAG;MACf2D,MAAM,GAAGzC,wBAAwB,CAACqB,KAAK,EAAEpE,SAAS,CAAC;IACrD,IAAIyF,YAAY,GAAGhC,WAAW,CAAC+B,MAAM,EAAE,KAAK,CAAC;MAC3CE,EAAE,GAAGD,YAAY,CAACE,MAAM;MACxBC,mBAAmB,GAAG7C,wBAAwB,CAAC0C,YAAY,EAAExF,UAAU,CAAC;IAC1EgF,QAAQ,GAAG,aAAa5B,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAEmD,mBAAmB,EAAE;MACpFR,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNlB,IAAI,EAAE,MAAM;MACZxC,GAAG,EAAEA;IACP,CAAC,CAAC,CAAC;EACL;EACA,OAAOoD,QAAQ;AACjB;AACA,SAASY,mBAAmBA,CAACzB,KAAK,EAAE;EAClC,IAAIG,CAAC,GAAGH,KAAK,CAACG,CAAC;IACbE,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBqB,iBAAiB,GAAG1B,KAAK,CAAC2B,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,gBAAgB,GAAG5B,KAAK,CAAC4B,gBAAgB;EAC3C,IAAI,CAACD,UAAU,IAAI,CAACC,gBAAgB,IAAI,CAACA,gBAAgB,CAAC1E,MAAM,EAAE;IAChE,OAAO,IAAI;EACb;EACA,IAAI2E,KAAK,GAAGD,gBAAgB,CAACE,GAAG,CAAC,UAAUC,KAAK,EAAEjE,CAAC,EAAE;IACnD,IAAIkE,aAAa,GAAGhF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9DgB,EAAE,EAAEb,CAAC;MACLc,EAAE,EAAEc,KAAK;MACTb,EAAE,EAAEf,CAAC,GAAGE,KAAK;MACbc,EAAE,EAAEY,KAAK;MACTtE,GAAG,EAAE,OAAO,CAACwE,MAAM,CAACnE,CAAC,CAAC;MACtBoE,KAAK,EAAEpE;IACT,CAAC,CAAC;IACF,OAAO6C,cAAc,CAACgB,UAAU,EAAEK,aAAa,CAAC;EAClD,CAAC,CAAC;EACF,OAAO,aAAa/C,KAAK,CAACuB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAEmB,KAAK,CAAC;AACX;AACA,SAASM,iBAAiBA,CAACnC,KAAK,EAAE;EAChC,IAAII,CAAC,GAAGJ,KAAK,CAACI,CAAC;IACbE,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrB8B,eAAe,GAAGpC,KAAK,CAACqC,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC9DE,cAAc,GAAGtC,KAAK,CAACsC,cAAc;EACvC,IAAI,CAACD,QAAQ,IAAI,CAACC,cAAc,IAAI,CAACA,cAAc,CAACpF,MAAM,EAAE;IAC1D,OAAO,IAAI;EACb;EACA,IAAI2E,KAAK,GAAGS,cAAc,CAACR,GAAG,CAAC,UAAUC,KAAK,EAAEjE,CAAC,EAAE;IACjD,IAAIkE,aAAa,GAAGhF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9DgB,EAAE,EAAEe,KAAK;MACTd,EAAE,EAAEb,CAAC;MACLc,EAAE,EAAEa,KAAK;MACTZ,EAAE,EAAEf,CAAC,GAAGE,MAAM;MACd7C,GAAG,EAAE,OAAO,CAACwE,MAAM,CAACnE,CAAC,CAAC;MACtBoE,KAAK,EAAEpE;IACT,CAAC,CAAC;IACF,OAAO6C,cAAc,CAAC0B,QAAQ,EAAEL,aAAa,CAAC;EAChD,CAAC,CAAC;EACF,OAAO,aAAa/C,KAAK,CAACuB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAEmB,KAAK,CAAC;AACX;AACA,SAASU,iBAAiBA,CAACvC,KAAK,EAAE;EAChC,IAAIwC,cAAc,GAAGxC,KAAK,CAACwC,cAAc;IACvCtC,WAAW,GAAGF,KAAK,CAACE,WAAW;IAC/BC,CAAC,GAAGH,KAAK,CAACG,CAAC;IACXC,CAAC,GAAGJ,KAAK,CAACI,CAAC;IACXC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBsB,gBAAgB,GAAG5B,KAAK,CAAC4B,gBAAgB;IACzCa,kBAAkB,GAAGzC,KAAK,CAAC2B,UAAU;IACrCA,UAAU,GAAGc,kBAAkB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,kBAAkB;EACxE,IAAI,CAACd,UAAU,IAAI,CAACa,cAAc,IAAI,CAACA,cAAc,CAACtF,MAAM,EAAE;IAC5D,OAAO,IAAI;EACb;;EAEA;EACA,IAAIwF,6BAA6B,GAAGd,gBAAgB,CAACE,GAAG,CAAC,UAAUzF,CAAC,EAAE;IACpE,OAAOsG,IAAI,CAACC,KAAK,CAACvG,CAAC,GAAG+D,CAAC,GAAGA,CAAC,CAAC;EAC9B,CAAC,CAAC,CAACyC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACtB,OAAOD,CAAC,GAAGC,CAAC;EACd,CAAC,CAAC;EACF;EACA,IAAI3C,CAAC,KAAKsC,6BAA6B,CAAC,CAAC,CAAC,EAAE;IAC1CA,6BAA6B,CAACM,OAAO,CAAC,CAAC,CAAC;EAC1C;EACA,IAAInB,KAAK,GAAGa,6BAA6B,CAACZ,GAAG,CAAC,UAAUC,KAAK,EAAEjE,CAAC,EAAE;IAChE;IACA,IAAImF,UAAU,GAAG,CAACP,6BAA6B,CAAC5E,CAAC,GAAG,CAAC,CAAC;IACtD,IAAIoF,UAAU,GAAGD,UAAU,GAAG7C,CAAC,GAAGE,MAAM,GAAGyB,KAAK,GAAGW,6BAA6B,CAAC5E,CAAC,GAAG,CAAC,CAAC,GAAGiE,KAAK;IAC/F,IAAImB,UAAU,IAAI,CAAC,EAAE;MACnB,OAAO,IAAI;IACb;IACA,IAAIC,UAAU,GAAGrF,CAAC,GAAG0E,cAAc,CAACtF,MAAM;IAC1C,OAAO,aAAa+B,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;MAC9C/C,GAAG,EAAE,QAAQ,CAACwE,MAAM,CAACnE,CAAC,CAAC,CAAC;MAAA;;MAExBsC,CAAC,EAAE2B,KAAK;MACR5B,CAAC,EAAEA,CAAC;MACJG,MAAM,EAAE4C,UAAU;MAClB7C,KAAK,EAAEA,KAAK;MACZI,MAAM,EAAE,MAAM;MACdR,IAAI,EAAEuC,cAAc,CAACW,UAAU,CAAC;MAChCjD,WAAW,EAAEA,WAAW;MACxBQ,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAazB,KAAK,CAACuB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAEmB,KAAK,CAAC;AACX;AACA,SAASuB,eAAeA,CAACpD,KAAK,EAAE;EAC9B,IAAIqD,gBAAgB,GAAGrD,KAAK,CAACqC,QAAQ;IACnCA,QAAQ,GAAGgB,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IAChEC,YAAY,GAAGtD,KAAK,CAACsD,YAAY;IACjCpD,WAAW,GAAGF,KAAK,CAACE,WAAW;IAC/BC,CAAC,GAAGH,KAAK,CAACG,CAAC;IACXC,CAAC,GAAGJ,KAAK,CAACI,CAAC;IACXC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBgC,cAAc,GAAGtC,KAAK,CAACsC,cAAc;EACvC,IAAI,CAACD,QAAQ,IAAI,CAACiB,YAAY,IAAI,CAACA,YAAY,CAACpG,MAAM,EAAE;IACtD,OAAO,IAAI;EACb;EACA,IAAIqG,2BAA2B,GAAGjB,cAAc,CAACR,GAAG,CAAC,UAAUzF,CAAC,EAAE;IAChE,OAAOsG,IAAI,CAACC,KAAK,CAACvG,CAAC,GAAG8D,CAAC,GAAGA,CAAC,CAAC;EAC9B,CAAC,CAAC,CAAC0C,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACtB,OAAOD,CAAC,GAAGC,CAAC;EACd,CAAC,CAAC;EACF,IAAI5C,CAAC,KAAKoD,2BAA2B,CAAC,CAAC,CAAC,EAAE;IACxCA,2BAA2B,CAACP,OAAO,CAAC,CAAC,CAAC;EACxC;EACA,IAAInB,KAAK,GAAG0B,2BAA2B,CAACzB,GAAG,CAAC,UAAUC,KAAK,EAAEjE,CAAC,EAAE;IAC9D,IAAImF,UAAU,GAAG,CAACM,2BAA2B,CAACzF,CAAC,GAAG,CAAC,CAAC;IACpD,IAAI0F,SAAS,GAAGP,UAAU,GAAG9C,CAAC,GAAGE,KAAK,GAAG0B,KAAK,GAAGwB,2BAA2B,CAACzF,CAAC,GAAG,CAAC,CAAC,GAAGiE,KAAK;IAC3F,IAAIyB,SAAS,IAAI,CAAC,EAAE;MAClB,OAAO,IAAI;IACb;IACA,IAAIL,UAAU,GAAGrF,CAAC,GAAGwF,YAAY,CAACpG,MAAM;IACxC,OAAO,aAAa+B,KAAK,CAACuB,aAAa,CAAC,MAAM,EAAE;MAC9C/C,GAAG,EAAE,QAAQ,CAACwE,MAAM,CAACnE,CAAC,CAAC,CAAC;MAAA;;MAExBqC,CAAC,EAAE4B,KAAK;MACR3B,CAAC,EAAEA,CAAC;MACJC,KAAK,EAAEmD,SAAS;MAChBlD,MAAM,EAAEA,MAAM;MACdG,MAAM,EAAE,MAAM;MACdR,IAAI,EAAEqD,YAAY,CAACH,UAAU,CAAC;MAC9BjD,WAAW,EAAEA,WAAW;MACxBQ,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAazB,KAAK,CAACuB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAEmB,KAAK,CAAC;AACX;AACA,IAAI4B,mCAAmC,GAAG,SAASA,mCAAmCA,CAACC,IAAI,EAAEC,aAAa,EAAE;EAC1G,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACpBvD,KAAK,GAAGqD,IAAI,CAACrD,KAAK;IAClBC,MAAM,GAAGoD,IAAI,CAACpD,MAAM;IACpBiB,MAAM,GAAGmC,IAAI,CAACnC,MAAM;EACtB,OAAOjC,oBAAoB,CAACE,QAAQ,CAACxC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,aAAa,CAACoE,YAAY,CAAC,EAAED,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1HE,KAAK,EAAEvE,cAAc,CAACqE,KAAK,EAAE,IAAI,CAAC;IAClCG,OAAO,EAAE;MACP5D,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA;IACV;EACF,CAAC,CAAC,CAAC,EAAEiB,MAAM,CAACyC,IAAI,EAAEzC,MAAM,CAACyC,IAAI,GAAGzC,MAAM,CAAClB,KAAK,EAAEsD,aAAa,CAAC;AAC9D,CAAC;AACD,IAAIM,qCAAqC,GAAG,SAASA,qCAAqCA,CAACC,KAAK,EAAEP,aAAa,EAAE;EAC/G,IAAIQ,KAAK,GAAGD,KAAK,CAACC,KAAK;IACrB9D,KAAK,GAAG6D,KAAK,CAAC7D,KAAK;IACnBC,MAAM,GAAG4D,KAAK,CAAC5D,MAAM;IACrBiB,MAAM,GAAG2C,KAAK,CAAC3C,MAAM;EACvB,OAAOjC,oBAAoB,CAACE,QAAQ,CAACxC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyC,aAAa,CAACoE,YAAY,CAAC,EAAEM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1HL,KAAK,EAAEvE,cAAc,CAAC4E,KAAK,EAAE,IAAI,CAAC;IAClCJ,OAAO,EAAE;MACP5D,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAEA,KAAK;MACZC,MAAM,EAAEA;IACV;EACF,CAAC,CAAC,CAAC,EAAEiB,MAAM,CAAC6C,GAAG,EAAE7C,MAAM,CAAC6C,GAAG,GAAG7C,MAAM,CAACjB,MAAM,EAAEqD,aAAa,CAAC;AAC7D,CAAC;AACD,IAAIE,YAAY,GAAG;EACjBlC,UAAU,EAAE,IAAI;EAChBU,QAAQ,EAAE,IAAI;EACd;EACAT,gBAAgB,EAAE,EAAE;EACpB;EACAU,cAAc,EAAE,EAAE;EAClB7B,MAAM,EAAE,MAAM;EACdR,IAAI,EAAE,MAAM;EACZ;EACAqD,YAAY,EAAE,EAAE;EAChBd,cAAc,EAAE;AAClB,CAAC;AACD,OAAO,SAAS6B,aAAaA,CAACrE,KAAK,EAAE;EACnC,IAAIsE,aAAa,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,mBAAmB;EAChH,IAAIC,UAAU,GAAGhF,aAAa,CAAC,CAAC;EAChC,IAAIiF,WAAW,GAAGlF,cAAc,CAAC,CAAC;EAClC,IAAI4B,MAAM,GAAG1B,SAAS,CAAC,CAAC;EACxB,IAAIiF,sBAAsB,GAAG9H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACvES,MAAM,EAAE,CAAC6D,aAAa,GAAGtE,KAAK,CAACS,MAAM,MAAM,IAAI,IAAI6D,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGT,YAAY,CAACpD,MAAM;IACjHR,IAAI,EAAE,CAACsE,WAAW,GAAGvE,KAAK,CAACC,IAAI,MAAM,IAAI,IAAIsE,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGV,YAAY,CAAC5D,IAAI;IACrG0B,UAAU,EAAE,CAAC6C,kBAAkB,GAAGxE,KAAK,CAAC2B,UAAU,MAAM,IAAI,IAAI6C,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAGX,YAAY,CAAClC,UAAU;IAC5Ia,cAAc,EAAE,CAACiC,qBAAqB,GAAGzE,KAAK,CAACwC,cAAc,MAAM,IAAI,IAAIiC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGZ,YAAY,CAACrB,cAAc;IACjKH,QAAQ,EAAE,CAACqC,gBAAgB,GAAG1E,KAAK,CAACqC,QAAQ,MAAM,IAAI,IAAIqC,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGb,YAAY,CAACxB,QAAQ;IAChIiB,YAAY,EAAE,CAACqB,mBAAmB,GAAG3E,KAAK,CAACsD,YAAY,MAAM,IAAI,IAAIqB,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAGd,YAAY,CAACP,YAAY;IACrJnD,CAAC,EAAEf,QAAQ,CAACY,KAAK,CAACG,CAAC,CAAC,GAAGH,KAAK,CAACG,CAAC,GAAGoB,MAAM,CAACyC,IAAI;IAC5C5D,CAAC,EAAEhB,QAAQ,CAACY,KAAK,CAACI,CAAC,CAAC,GAAGJ,KAAK,CAACI,CAAC,GAAGmB,MAAM,CAAC6C,GAAG;IAC3C/D,KAAK,EAAEjB,QAAQ,CAACY,KAAK,CAACK,KAAK,CAAC,GAAGL,KAAK,CAACK,KAAK,GAAGkB,MAAM,CAAClB,KAAK;IACzDC,MAAM,EAAElB,QAAQ,CAACY,KAAK,CAACM,MAAM,CAAC,GAAGN,KAAK,CAACM,MAAM,GAAGiB,MAAM,CAACjB;EACzD,CAAC,CAAC;EACF,IAAIH,CAAC,GAAG2E,sBAAsB,CAAC3E,CAAC;IAC9BC,CAAC,GAAG0E,sBAAsB,CAAC1E,CAAC;IAC5BC,KAAK,GAAGyE,sBAAsB,CAACzE,KAAK;IACpCC,MAAM,GAAGwE,sBAAsB,CAACxE,MAAM;IACtCqD,aAAa,GAAGmB,sBAAsB,CAACnB,aAAa;IACpDoB,gBAAgB,GAAGD,sBAAsB,CAACC,gBAAgB;IAC1DC,cAAc,GAAGF,sBAAsB,CAACE,cAAc;;EAExD;EACA,IAAIpB,KAAK,GAAGlE,iBAAiB,CAAC,CAAC;EAC/B;EACA,IAAIyE,KAAK,GAAGrE,gCAAgC,CAAC,CAAC;EAC9C,IAAI,CAACV,QAAQ,CAACiB,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAI,CAACjB,QAAQ,CAACkB,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACe,CAAC,CAAC,IAAIA,CAAC,KAAK,CAACA,CAAC,IAAI,CAACf,QAAQ,CAACgB,CAAC,CAAC,IAAIA,CAAC,KAAK,CAACA,CAAC,EAAE;IAC9H,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI6E,4BAA4B,GAAGH,sBAAsB,CAACG,4BAA4B,IAAIxB,mCAAmC;EAC7H,IAAIyB,8BAA8B,GAAGJ,sBAAsB,CAACI,8BAA8B,IAAIjB,qCAAqC;EACnI,IAAIrC,gBAAgB,GAAGkD,sBAAsB,CAAClD,gBAAgB;IAC5DU,cAAc,GAAGwC,sBAAsB,CAACxC,cAAc;;EAExD;EACA,IAAI,CAAC,CAACV,gBAAgB,IAAI,CAACA,gBAAgB,CAAC1E,MAAM,KAAKgC,UAAU,CAACgG,8BAA8B,CAAC,EAAE;IACjG,IAAIC,kBAAkB,GAAGJ,gBAAgB,IAAIA,gBAAgB,CAAC7H,MAAM;IACpE,IAAIkI,eAAe,GAAGF,8BAA8B,CAAC;MACnDf,KAAK,EAAEA,KAAK,GAAGnH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACzDL,KAAK,EAAEqB,kBAAkB,GAAGJ,gBAAgB,GAAGZ,KAAK,CAACL;MACvD,CAAC,CAAC,GAAGuB,SAAS;MACdhF,KAAK,EAAEuE,UAAU;MACjBtE,MAAM,EAAEuE,WAAW;MACnBtD,MAAM,EAAEA;IACV,CAAC,EAAE4D,kBAAkB,GAAG,IAAI,GAAGxB,aAAa,CAAC;IAC7CxE,IAAI,CAACmG,KAAK,CAACC,OAAO,CAACH,eAAe,CAAC,EAAE,8EAA8E,CAACnD,MAAM,CAACnG,OAAO,CAACsJ,eAAe,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1J,IAAIE,KAAK,CAACC,OAAO,CAACH,eAAe,CAAC,EAAE;MAClCxD,gBAAgB,GAAGwD,eAAe;IACpC;EACF;;EAEA;EACA,IAAI,CAAC,CAAC9C,cAAc,IAAI,CAACA,cAAc,CAACpF,MAAM,KAAKgC,UAAU,CAAC+F,4BAA4B,CAAC,EAAE;IAC3F,IAAIO,gBAAgB,GAAGR,cAAc,IAAIA,cAAc,CAAC9H,MAAM;IAC9D,IAAIuI,gBAAgB,GAAGR,4BAA4B,CAAC;MAClDrB,KAAK,EAAEA,KAAK,GAAG5G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4G,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACzDE,KAAK,EAAE0B,gBAAgB,GAAGR,cAAc,GAAGpB,KAAK,CAACE;MACnD,CAAC,CAAC,GAAGuB,SAAS;MACdhF,KAAK,EAAEuE,UAAU;MACjBtE,MAAM,EAAEuE,WAAW;MACnBtD,MAAM,EAAEA;IACV,CAAC,EAAEiE,gBAAgB,GAAG,IAAI,GAAG7B,aAAa,CAAC;IAC3CxE,IAAI,CAACmG,KAAK,CAACC,OAAO,CAACE,gBAAgB,CAAC,EAAE,4EAA4E,CAACxD,MAAM,CAACnG,OAAO,CAAC2J,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1J,IAAIH,KAAK,CAACC,OAAO,CAACE,gBAAgB,CAAC,EAAE;MACnCnD,cAAc,GAAGmD,gBAAgB;IACnC;EACF;EACA,OAAO,aAAaxG,KAAK,CAACuB,aAAa,CAAC,GAAG,EAAE;IAC3CE,SAAS,EAAE;EACb,CAAC,EAAE,aAAazB,KAAK,CAACuB,aAAa,CAACT,UAAU,EAAE;IAC9CE,IAAI,EAAE6E,sBAAsB,CAAC7E,IAAI;IACjCC,WAAW,EAAE4E,sBAAsB,CAAC5E,WAAW;IAC/CC,CAAC,EAAE2E,sBAAsB,CAAC3E,CAAC;IAC3BC,CAAC,EAAE0E,sBAAsB,CAAC1E,CAAC;IAC3BC,KAAK,EAAEyE,sBAAsB,CAACzE,KAAK;IACnCC,MAAM,EAAEwE,sBAAsB,CAACxE,MAAM;IACrCC,EAAE,EAAEuE,sBAAsB,CAACvE;EAC7B,CAAC,CAAC,EAAE,aAAatB,KAAK,CAACuB,aAAa,CAACiB,mBAAmB,EAAEpD,QAAQ,CAAC,CAAC,CAAC,EAAEyG,sBAAsB,EAAE;IAC7FvD,MAAM,EAAEA,MAAM;IACdK,gBAAgB,EAAEA,gBAAgB;IAClCgC,KAAK,EAAEA,KAAK;IACZO,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,EAAE,aAAalF,KAAK,CAACuB,aAAa,CAAC2B,iBAAiB,EAAE9D,QAAQ,CAAC,CAAC,CAAC,EAAEyG,sBAAsB,EAAE;IAC5FvD,MAAM,EAAEA,MAAM;IACde,cAAc,EAAEA,cAAc;IAC9BsB,KAAK,EAAEA,KAAK;IACZO,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,EAAE,aAAalF,KAAK,CAACuB,aAAa,CAAC+B,iBAAiB,EAAElE,QAAQ,CAAC,CAAC,CAAC,EAAEyG,sBAAsB,EAAE;IAC5FlD,gBAAgB,EAAEA;EACpB,CAAC,CAAC,CAAC,EAAE,aAAa3C,KAAK,CAACuB,aAAa,CAAC4C,eAAe,EAAE/E,QAAQ,CAAC,CAAC,CAAC,EAAEyG,sBAAsB,EAAE;IAC1FxC,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC,CAAC;AACN;AACA+B,aAAa,CAACqB,WAAW,GAAG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}