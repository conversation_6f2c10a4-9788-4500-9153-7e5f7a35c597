{"ast": null, "code": "import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\nexport default function thresholdScott(values, min, max) {\n  const c = count(values),\n    d = deviation(values);\n  return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}", "map": {"version": 3, "names": ["count", "deviation", "<PERSON><PERSON><PERSON>", "values", "min", "max", "c", "d", "Math", "ceil", "cbrt"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-array/src/threshold/scott.js"], "sourcesContent": ["import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\n\nexport default function thresholdScott(values, min, max) {\n  const c = count(values), d = deviation(values);\n  return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,SAAS,MAAM,iBAAiB;AAEvC,eAAe,SAASC,cAAcA,CAACC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACvD,MAAMC,CAAC,GAAGN,KAAK,CAACG,MAAM,CAAC;IAAEI,CAAC,GAAGN,SAAS,CAACE,MAAM,CAAC;EAC9C,OAAOG,CAAC,IAAIC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACJ,GAAG,GAAGD,GAAG,IAAII,IAAI,CAACE,IAAI,CAACJ,CAAC,CAAC,IAAI,IAAI,GAAGC,CAAC,CAAC,CAAC,GAAG,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}