{"ast": null, "code": "import platform from '../platform/index.js';\nexport default platform.hasStandardBrowserEnv ? ((origin, isMSIE) => url => {\n  url = new URL(url, platform.origin);\n  return origin.protocol === url.protocol && origin.host === url.host && (isMSIE || origin.port === url.port);\n})(new URL(platform.origin), platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)) : () => true;", "map": {"version": 3, "names": ["platform", "hasStandardBrowserEnv", "origin", "isMSIE", "url", "URL", "protocol", "host", "port", "navigator", "test", "userAgent"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/axios/lib/helpers/isURLSameOrigin.js"], "sourcesContent": ["import platform from '../platform/index.js';\n\nexport default platform.hasStandardBrowserEnv ? ((origin, isMSIE) => (url) => {\n  url = new URL(url, platform.origin);\n\n  return (\n    origin.protocol === url.protocol &&\n    origin.host === url.host &&\n    (isMSIE || origin.port === url.port)\n  );\n})(\n  new URL(platform.origin),\n  platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent)\n) : () => true;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAE3C,eAAeA,QAAQ,CAACC,qBAAqB,GAAG,CAAC,CAACC,MAAM,EAAEC,MAAM,KAAMC,GAAG,IAAK;EAC5EA,GAAG,GAAG,IAAIC,GAAG,CAACD,GAAG,EAAEJ,QAAQ,CAACE,MAAM,CAAC;EAEnC,OACEA,MAAM,CAACI,QAAQ,KAAKF,GAAG,CAACE,QAAQ,IAChCJ,MAAM,CAACK,IAAI,KAAKH,GAAG,CAACG,IAAI,KACvBJ,MAAM,IAAID,MAAM,CAACM,IAAI,KAAKJ,GAAG,CAACI,IAAI,CAAC;AAExC,CAAC,EACC,IAAIH,GAAG,CAACL,QAAQ,CAACE,MAAM,CAAC,EACxBF,QAAQ,CAACS,SAAS,IAAI,iBAAiB,CAACC,IAAI,CAACV,QAAQ,CAACS,SAAS,CAACE,SAAS,CAC3E,CAAC,GAAG,MAAM,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}