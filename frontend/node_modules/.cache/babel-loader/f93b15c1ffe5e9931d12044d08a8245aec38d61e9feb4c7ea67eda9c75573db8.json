{"ast": null, "code": "var memoize = require('./memoize');\n\n/** Used as the maximum memoize cache size. */\nvar MAX_MEMOIZE_SIZE = 500;\n\n/**\n * A specialized version of `_.memoize` which clears the memoized function's\n * cache when it exceeds `MAX_MEMOIZE_SIZE`.\n *\n * @private\n * @param {Function} func The function to have its output memoized.\n * @returns {Function} Returns the new memoized function.\n */\nfunction memoizeCapped(func) {\n  var result = memoize(func, function (key) {\n    if (cache.size === MAX_MEMOIZE_SIZE) {\n      cache.clear();\n    }\n    return key;\n  });\n  var cache = result.cache;\n  return result;\n}\nmodule.exports = memoizeCapped;", "map": {"version": 3, "names": ["memoize", "require", "MAX_MEMOIZE_SIZE", "memoizeCapped", "func", "result", "key", "cache", "size", "clear", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_memoizeCapped.js"], "sourcesContent": ["var memoize = require('./memoize');\n\n/** Used as the maximum memoize cache size. */\nvar MAX_MEMOIZE_SIZE = 500;\n\n/**\n * A specialized version of `_.memoize` which clears the memoized function's\n * cache when it exceeds `MAX_MEMOIZE_SIZE`.\n *\n * @private\n * @param {Function} func The function to have its output memoized.\n * @returns {Function} Returns the new memoized function.\n */\nfunction memoizeCapped(func) {\n  var result = memoize(func, function(key) {\n    if (cache.size === MAX_MEMOIZE_SIZE) {\n      cache.clear();\n    }\n    return key;\n  });\n\n  var cache = result.cache;\n  return result;\n}\n\nmodule.exports = memoizeCapped;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;;AAElC;AACA,IAAIC,gBAAgB,GAAG,GAAG;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,MAAM,GAAGL,OAAO,CAACI,IAAI,EAAE,UAASE,GAAG,EAAE;IACvC,IAAIC,KAAK,CAACC,IAAI,KAAKN,gBAAgB,EAAE;MACnCK,KAAK,CAACE,KAAK,CAAC,CAAC;IACf;IACA,OAAOH,GAAG;EACZ,CAAC,CAAC;EAEF,IAAIC,KAAK,GAAGF,MAAM,CAACE,KAAK;EACxB,OAAOF,MAAM;AACf;AAEAK,MAAM,CAACC,OAAO,GAAGR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}