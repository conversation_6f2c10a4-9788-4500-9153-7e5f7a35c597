{"ast": null, "code": "var Symbol = require('./_Symbol'),\n  Uint8Array = require('./_Uint8Array'),\n  eq = require('./eq'),\n  equalArrays = require('./_equalArrays'),\n  mapToArray = require('./_mapToArray'),\n  setToArray = require('./_setToArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n  COMPARE_UNORDERED_FLAG = 2;\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n  dateTag = '[object Date]',\n  errorTag = '[object Error]',\n  mapTag = '[object Map]',\n  numberTag = '[object Number]',\n  regexpTag = '[object RegExp]',\n  setTag = '[object Set]',\n  stringTag = '[object String]',\n  symbolTag = '[object Symbol]';\nvar arrayBufferTag = '[object ArrayBuffer]',\n  dataViewTag = '[object DataView]';\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n  symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * A specialized version of `baseIsEqualDeep` for comparing objects of\n * the same `toStringTag`.\n *\n * **Note:** This function only supports comparing values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {string} tag The `toStringTag` of the objects to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n  switch (tag) {\n    case dataViewTag:\n      if (object.byteLength != other.byteLength || object.byteOffset != other.byteOffset) {\n        return false;\n      }\n      object = object.buffer;\n      other = other.buffer;\n    case arrayBufferTag:\n      if (object.byteLength != other.byteLength || !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\n        return false;\n      }\n      return true;\n    case boolTag:\n    case dateTag:\n    case numberTag:\n      // Coerce booleans to `1` or `0` and dates to milliseconds.\n      // Invalid dates are coerced to `NaN`.\n      return eq(+object, +other);\n    case errorTag:\n      return object.name == other.name && object.message == other.message;\n    case regexpTag:\n    case stringTag:\n      // Coerce regexes to strings and treat strings, primitives and objects,\n      // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\n      // for more details.\n      return object == other + '';\n    case mapTag:\n      var convert = mapToArray;\n    case setTag:\n      var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\n      convert || (convert = setToArray);\n      if (object.size != other.size && !isPartial) {\n        return false;\n      }\n      // Assume cyclic values are equal.\n      var stacked = stack.get(object);\n      if (stacked) {\n        return stacked == other;\n      }\n      bitmask |= COMPARE_UNORDERED_FLAG;\n\n      // Recursively compare objects (susceptible to call stack limits).\n      stack.set(object, other);\n      var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n      stack['delete'](object);\n      return result;\n    case symbolTag:\n      if (symbolValueOf) {\n        return symbolValueOf.call(object) == symbolValueOf.call(other);\n      }\n  }\n  return false;\n}\nmodule.exports = equalByTag;", "map": {"version": 3, "names": ["Symbol", "require", "Uint8Array", "eq", "equalArrays", "mapToArray", "setToArray", "COMPARE_PARTIAL_FLAG", "COMPARE_UNORDERED_FLAG", "boolTag", "dateTag", "errorTag", "mapTag", "numberTag", "regexpTag", "setTag", "stringTag", "symbolTag", "arrayBufferTag", "dataViewTag", "symbol<PERSON>roto", "prototype", "undefined", "symbolValueOf", "valueOf", "equalByTag", "object", "other", "tag", "bitmask", "customizer", "equalFunc", "stack", "byteLength", "byteOffset", "buffer", "name", "message", "convert", "isPartial", "size", "stacked", "get", "set", "result", "call", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_equalByTag.js"], "sourcesContent": ["var Symbol = require('./_Symbol'),\n    Uint8Array = require('./_Uint8Array'),\n    eq = require('./eq'),\n    equalArrays = require('./_equalArrays'),\n    mapToArray = require('./_mapToArray'),\n    setToArray = require('./_setToArray');\n\n/** Used to compose bitmasks for value comparisons. */\nvar COMPARE_PARTIAL_FLAG = 1,\n    COMPARE_UNORDERED_FLAG = 2;\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    errorTag = '[object Error]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]';\n\n/** Used to convert symbols to primitives and strings. */\nvar symbolProto = Symbol ? Symbol.prototype : undefined,\n    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;\n\n/**\n * A specialized version of `baseIsEqualDeep` for comparing objects of\n * the same `toStringTag`.\n *\n * **Note:** This function only supports comparing values with tags of\n * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {string} tag The `toStringTag` of the objects to compare.\n * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.\n * @param {Function} customizer The function to customize comparisons.\n * @param {Function} equalFunc The function to determine equivalents of values.\n * @param {Object} stack Tracks traversed `object` and `other` objects.\n * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.\n */\nfunction equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {\n  switch (tag) {\n    case dataViewTag:\n      if ((object.byteLength != other.byteLength) ||\n          (object.byteOffset != other.byteOffset)) {\n        return false;\n      }\n      object = object.buffer;\n      other = other.buffer;\n\n    case arrayBufferTag:\n      if ((object.byteLength != other.byteLength) ||\n          !equalFunc(new Uint8Array(object), new Uint8Array(other))) {\n        return false;\n      }\n      return true;\n\n    case boolTag:\n    case dateTag:\n    case numberTag:\n      // Coerce booleans to `1` or `0` and dates to milliseconds.\n      // Invalid dates are coerced to `NaN`.\n      return eq(+object, +other);\n\n    case errorTag:\n      return object.name == other.name && object.message == other.message;\n\n    case regexpTag:\n    case stringTag:\n      // Coerce regexes to strings and treat strings, primitives and objects,\n      // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring\n      // for more details.\n      return object == (other + '');\n\n    case mapTag:\n      var convert = mapToArray;\n\n    case setTag:\n      var isPartial = bitmask & COMPARE_PARTIAL_FLAG;\n      convert || (convert = setToArray);\n\n      if (object.size != other.size && !isPartial) {\n        return false;\n      }\n      // Assume cyclic values are equal.\n      var stacked = stack.get(object);\n      if (stacked) {\n        return stacked == other;\n      }\n      bitmask |= COMPARE_UNORDERED_FLAG;\n\n      // Recursively compare objects (susceptible to call stack limits).\n      stack.set(object, other);\n      var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);\n      stack['delete'](object);\n      return result;\n\n    case symbolTag:\n      if (symbolValueOf) {\n        return symbolValueOf.call(object) == symbolValueOf.call(other);\n      }\n  }\n  return false;\n}\n\nmodule.exports = equalByTag;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;EAC7BC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,EAAE,GAAGF,OAAO,CAAC,MAAM,CAAC;EACpBG,WAAW,GAAGH,OAAO,CAAC,gBAAgB,CAAC;EACvCI,UAAU,GAAGJ,OAAO,CAAC,eAAe,CAAC;EACrCK,UAAU,GAAGL,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA,IAAIM,oBAAoB,GAAG,CAAC;EACxBC,sBAAsB,GAAG,CAAC;;AAE9B;AACA,IAAIC,OAAO,GAAG,kBAAkB;EAC5BC,OAAO,GAAG,eAAe;EACzBC,QAAQ,GAAG,gBAAgB;EAC3BC,MAAM,GAAG,cAAc;EACvBC,SAAS,GAAG,iBAAiB;EAC7BC,SAAS,GAAG,iBAAiB;EAC7BC,MAAM,GAAG,cAAc;EACvBC,SAAS,GAAG,iBAAiB;EAC7BC,SAAS,GAAG,iBAAiB;AAEjC,IAAIC,cAAc,GAAG,sBAAsB;EACvCC,WAAW,GAAG,mBAAmB;;AAErC;AACA,IAAIC,WAAW,GAAGpB,MAAM,GAAGA,MAAM,CAACqB,SAAS,GAAGC,SAAS;EACnDC,aAAa,GAAGH,WAAW,GAAGA,WAAW,CAACI,OAAO,GAAGF,SAAS;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC7E,QAAQJ,GAAG;IACT,KAAKT,WAAW;MACd,IAAKO,MAAM,CAACO,UAAU,IAAIN,KAAK,CAACM,UAAU,IACrCP,MAAM,CAACQ,UAAU,IAAIP,KAAK,CAACO,UAAW,EAAE;QAC3C,OAAO,KAAK;MACd;MACAR,MAAM,GAAGA,MAAM,CAACS,MAAM;MACtBR,KAAK,GAAGA,KAAK,CAACQ,MAAM;IAEtB,KAAKjB,cAAc;MACjB,IAAKQ,MAAM,CAACO,UAAU,IAAIN,KAAK,CAACM,UAAU,IACtC,CAACF,SAAS,CAAC,IAAI7B,UAAU,CAACwB,MAAM,CAAC,EAAE,IAAIxB,UAAU,CAACyB,KAAK,CAAC,CAAC,EAAE;QAC7D,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IAEb,KAAKlB,OAAO;IACZ,KAAKC,OAAO;IACZ,KAAKG,SAAS;MACZ;MACA;MACA,OAAOV,EAAE,CAAC,CAACuB,MAAM,EAAE,CAACC,KAAK,CAAC;IAE5B,KAAKhB,QAAQ;MACX,OAAOe,MAAM,CAACU,IAAI,IAAIT,KAAK,CAACS,IAAI,IAAIV,MAAM,CAACW,OAAO,IAAIV,KAAK,CAACU,OAAO;IAErE,KAAKvB,SAAS;IACd,KAAKE,SAAS;MACZ;MACA;MACA;MACA,OAAOU,MAAM,IAAKC,KAAK,GAAG,EAAG;IAE/B,KAAKf,MAAM;MACT,IAAI0B,OAAO,GAAGjC,UAAU;IAE1B,KAAKU,MAAM;MACT,IAAIwB,SAAS,GAAGV,OAAO,GAAGtB,oBAAoB;MAC9C+B,OAAO,KAAKA,OAAO,GAAGhC,UAAU,CAAC;MAEjC,IAAIoB,MAAM,CAACc,IAAI,IAAIb,KAAK,CAACa,IAAI,IAAI,CAACD,SAAS,EAAE;QAC3C,OAAO,KAAK;MACd;MACA;MACA,IAAIE,OAAO,GAAGT,KAAK,CAACU,GAAG,CAAChB,MAAM,CAAC;MAC/B,IAAIe,OAAO,EAAE;QACX,OAAOA,OAAO,IAAId,KAAK;MACzB;MACAE,OAAO,IAAIrB,sBAAsB;;MAEjC;MACAwB,KAAK,CAACW,GAAG,CAACjB,MAAM,EAAEC,KAAK,CAAC;MACxB,IAAIiB,MAAM,GAAGxC,WAAW,CAACkC,OAAO,CAACZ,MAAM,CAAC,EAAEY,OAAO,CAACX,KAAK,CAAC,EAAEE,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,CAAC;MAChGA,KAAK,CAAC,QAAQ,CAAC,CAACN,MAAM,CAAC;MACvB,OAAOkB,MAAM;IAEf,KAAK3B,SAAS;MACZ,IAAIM,aAAa,EAAE;QACjB,OAAOA,aAAa,CAACsB,IAAI,CAACnB,MAAM,CAAC,IAAIH,aAAa,CAACsB,IAAI,CAAClB,KAAK,CAAC;MAChE;EACJ;EACA,OAAO,KAAK;AACd;AAEAmB,MAAM,CAACC,OAAO,GAAGtB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}