{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport isLeapYear from \"../isLeapYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of days in a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\nexport default function getDaysInYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  if (String(new Date(date)) === 'Invalid Date') {\n    return NaN;\n  }\n  return isLeapYear(date) ? 366 : 365;\n}", "map": {"version": 3, "names": ["toDate", "isLeapYear", "requiredArgs", "getDaysInYear", "dirtyDate", "arguments", "date", "String", "Date", "NaN"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/getDaysInYear/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport isLeapYear from \"../isLeapYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDaysInYear\n * @category Year Helpers\n * @summary Get the number of days in a year of the given date.\n *\n * @description\n * Get the number of days in a year of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of days in a year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many days are in 2012?\n * const result = getDaysInYear(new Date(2012, 0, 1))\n * //=> 366\n */\nexport default function getDaysInYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  if (String(new Date(date)) === 'Invalid Date') {\n    return NaN;\n  }\n  return isLeapYear(date) ? 366 : 365;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAE;EAC/CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAIG,MAAM,CAAC,IAAIC,IAAI,CAACF,IAAI,CAAC,CAAC,KAAK,cAAc,EAAE;IAC7C,OAAOG,GAAG;EACZ;EACA,OAAOR,UAAU,CAACK,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}