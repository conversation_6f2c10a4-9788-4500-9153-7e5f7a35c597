{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getISOWeek from \"../getISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISOWeek\n * @category ISO Week Helpers\n * @summary Set the ISO week to the given date.\n *\n * @description\n * Set the ISO week to the given date, saving the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} isoWeek - the ISO week of the new date\n * @returns {Date} the new date with the ISO week set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 53rd ISO week to 7 August 2004:\n * const result = setISOWeek(new Date(2004, 7, 7), 53)\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport default function setISOWeek(dirtyDate, dirtyISOWeek) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeek = toInteger(dirtyISOWeek);\n  var diff = getISOWeek(date) - isoWeek;\n  date.setDate(date.getDate() - diff * 7);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "getISOWeek", "requiredArgs", "setISOWeek", "dirtyDate", "dirtyISOWeek", "arguments", "date", "isoWeek", "diff", "setDate", "getDate"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/setISOWeek/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getISOWeek from \"../getISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setISOWeek\n * @category ISO Week Helpers\n * @summary Set the ISO week to the given date.\n *\n * @description\n * Set the ISO week to the given date, saving the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} isoWeek - the ISO week of the new date\n * @returns {Date} the new date with the ISO week set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 53rd ISO week to 7 August 2004:\n * const result = setISOWeek(new Date(2004, 7, 7), 53)\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport default function setISOWeek(dirtyDate, dirtyISOWeek) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var isoWeek = toInteger(dirtyISOWeek);\n  var diff = getISOWeek(date) - isoWeek;\n  date.setDate(date.getDate() - diff * 7);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC1DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGP,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAII,OAAO,GAAGT,SAAS,CAACM,YAAY,CAAC;EACrC,IAAII,IAAI,GAAGR,UAAU,CAACM,IAAI,CAAC,GAAGC,OAAO;EACrCD,IAAI,CAACG,OAAO,CAACH,IAAI,CAACI,OAAO,CAAC,CAAC,GAAGF,IAAI,GAAG,CAAC,CAAC;EACvC,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}