{"ast": null, "code": "import ascending from \"./ascending.js\";\nexport default function greatest(values) {\n  let compare = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ascending;\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined ? ascending(value, maxValue) > 0 : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined ? compare(value, max) > 0 : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}", "map": {"version": 3, "names": ["ascending", "greatest", "values", "compare", "arguments", "length", "undefined", "max", "defined", "maxValue", "element", "value"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-array/src/greatest.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\n\nexport default function greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, maxValue) > 0\n          : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AAEtC,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAuB;EAAA,IAArBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGJ,SAAS;EAC1D,IAAIO,GAAG;EACP,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIL,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxB,IAAII,QAAQ;IACZ,KAAK,MAAMC,OAAO,IAAIR,MAAM,EAAE;MAC5B,MAAMS,KAAK,GAAGR,OAAO,CAACO,OAAO,CAAC;MAC9B,IAAIF,OAAO,GACLR,SAAS,CAACW,KAAK,EAAEF,QAAQ,CAAC,GAAG,CAAC,GAC9BT,SAAS,CAACW,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACnCJ,GAAG,GAAGG,OAAO;QACbD,QAAQ,GAAGE,KAAK;QAChBH,OAAO,GAAG,IAAI;MAChB;IACF;EACF,CAAC,MAAM;IACL,KAAK,MAAMG,KAAK,IAAIT,MAAM,EAAE;MAC1B,IAAIM,OAAO,GACLL,OAAO,CAACQ,KAAK,EAAEJ,GAAG,CAAC,GAAG,CAAC,GACvBJ,OAAO,CAACQ,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,EAAE;QACjCJ,GAAG,GAAGI,KAAK;QACXH,OAAO,GAAG,IAAI;MAChB;IACF;EACF;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}