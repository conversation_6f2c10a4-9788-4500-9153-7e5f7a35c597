{"ast": null, "code": "var listCacheClear = require('./_listCacheClear'),\n  listCacheDelete = require('./_listCacheDelete'),\n  listCacheGet = require('./_listCacheGet'),\n  listCacheHas = require('./_listCacheHas'),\n  listCacheSet = require('./_listCacheSet');\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n    length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\nmodule.exports = ListCache;", "map": {"version": 3, "names": ["listCacheClear", "require", "listCacheDelete", "listCacheGet", "listCacheHas", "listCacheSet", "ListCache", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_ListCache.js"], "sourcesContent": ["var listCacheClear = require('./_listCacheClear'),\n    listCacheDelete = require('./_listCacheDelete'),\n    listCacheGet = require('./_listCacheGet'),\n    listCacheHas = require('./_listCacheHas'),\n    listCacheSet = require('./_listCacheSet');\n\n/**\n * Creates an list cache object.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction ListCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `ListCache`.\nListCache.prototype.clear = listCacheClear;\nListCache.prototype['delete'] = listCacheDelete;\nListCache.prototype.get = listCacheGet;\nListCache.prototype.has = listCacheHas;\nListCache.prototype.set = listCacheSet;\n\nmodule.exports = ListCache;\n"], "mappings": "AAAA,IAAIA,cAAc,GAAGC,OAAO,CAAC,mBAAmB,CAAC;EAC7CC,eAAe,GAAGD,OAAO,CAAC,oBAAoB,CAAC;EAC/CE,YAAY,GAAGF,OAAO,CAAC,iBAAiB,CAAC;EACzCG,YAAY,GAAGH,OAAO,CAAC,iBAAiB,CAAC;EACzCI,YAAY,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,SAASA,CAACC,OAAO,EAAE;EAC1B,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO,CAACE,MAAM;EAEjD,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACI,GAAG,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B;AACF;;AAEA;AACAL,SAAS,CAACO,SAAS,CAACH,KAAK,GAAGV,cAAc;AAC1CM,SAAS,CAACO,SAAS,CAAC,QAAQ,CAAC,GAAGX,eAAe;AAC/CI,SAAS,CAACO,SAAS,CAACC,GAAG,GAAGX,YAAY;AACtCG,SAAS,CAACO,SAAS,CAACE,GAAG,GAAGX,YAAY;AACtCE,SAAS,CAACO,SAAS,CAACD,GAAG,GAAGP,YAAY;AAEtCW,MAAM,CAACC,OAAO,GAAGX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}