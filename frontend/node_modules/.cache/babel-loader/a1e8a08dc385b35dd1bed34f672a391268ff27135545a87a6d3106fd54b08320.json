{"ast": null, "code": "/**\n * @name isExists\n * @category Common Helpers\n * @summary Is the given date exists?\n *\n * @description\n * Checks if the given arguments convert to an existing date.\n *\n * @param {Number} year of the date to check\n * @param {Number} month of the date to check\n * @param {Number} day of the date to check\n * @returns {<PERSON><PERSON>an} the date exists\n * @throws {TypeError} 3 arguments required\n *\n * @example\n * // For the valid date:\n * const result = isExists(2018, 0, 31)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isExists(2018, 1, 31)\n * //=> false\n */\nexport default function isExists(year, month, day) {\n  if (arguments.length < 3) {\n    throw new TypeError('3 argument required, but only ' + arguments.length + ' present');\n  }\n  var date = new Date(year, month, day);\n  return date.getFullYear() === year && date.getMonth() === month && date.getDate() === day;\n}", "map": {"version": 3, "names": ["isExists", "year", "month", "day", "arguments", "length", "TypeError", "date", "Date", "getFullYear", "getMonth", "getDate"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/isExists/index.js"], "sourcesContent": ["/**\n * @name isExists\n * @category Common Helpers\n * @summary Is the given date exists?\n *\n * @description\n * Checks if the given arguments convert to an existing date.\n *\n * @param {Number} year of the date to check\n * @param {Number} month of the date to check\n * @param {Number} day of the date to check\n * @returns {<PERSON><PERSON>an} the date exists\n * @throws {TypeError} 3 arguments required\n *\n * @example\n * // For the valid date:\n * const result = isExists(2018, 0, 31)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isExists(2018, 1, 31)\n * //=> false\n */\nexport default function isExists(year, month, day) {\n  if (arguments.length < 3) {\n    throw new TypeError('3 argument required, but only ' + arguments.length + ' present');\n  }\n  var date = new Date(year, month, day);\n  return date.getFullYear() === year && date.getMonth() === month && date.getDate() === day;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACjD,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,gCAAgC,GAAGF,SAAS,CAACC,MAAM,GAAG,UAAU,CAAC;EACvF;EACA,IAAIE,IAAI,GAAG,IAAIC,IAAI,CAACP,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACrC,OAAOI,IAAI,CAACE,WAAW,CAAC,CAAC,KAAKR,IAAI,IAAIM,IAAI,CAACG,QAAQ,CAAC,CAAC,KAAKR,KAAK,IAAIK,IAAI,CAACI,OAAO,CAAC,CAAC,KAAKR,GAAG;AAC3F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}