{"ast": null, "code": "import constant from \"./constant.js\";\nimport { abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau } from \"./math.js\";\nimport { withPath } from \"./path.js\";\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0,\n    y10 = y1 - y0,\n    x32 = x3 - x2,\n    y32 = y3 - y2,\n    t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n    y01 = y0 - y1,\n    lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n    ox = lo * y01,\n    oy = -lo * x01,\n    x11 = x0 + ox,\n    y11 = y0 + oy,\n    x10 = x1 + ox,\n    y10 = y1 + oy,\n    x00 = (x11 + x10) / 2,\n    y00 = (y11 + y10) / 2,\n    dx = x10 - x11,\n    dy = y10 - y11,\n    d2 = dx * dx + dy * dy,\n    r = r1 - rc,\n    D = x11 * y10 - x10 * y11,\n    d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n    cx0 = (D * dy - dx * d) / d2,\n    cy0 = (-D * dx - dy * d) / d2,\n    cx1 = (D * dy + dx * d) / d2,\n    cy1 = (-D * dx + dy * d) / d2,\n    dx0 = cx0 - x00,\n    dy0 = cy0 - y00,\n    dx1 = cx1 - x00,\n    dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\nexport default function () {\n  var innerRadius = arcInnerRadius,\n    outerRadius = arcOuterRadius,\n    cornerRadius = constant(0),\n    padRadius = null,\n    startAngle = arcStartAngle,\n    endAngle = arcEndAngle,\n    padAngle = arcPadAngle,\n    context = null,\n    path = withPath(arc);\n  function arc() {\n    var buffer,\n      r,\n      r0 = +innerRadius.apply(this, arguments),\n      r1 = +outerRadius.apply(this, arguments),\n      a0 = startAngle.apply(this, arguments) - halfPi,\n      a1 = endAngle.apply(this, arguments) - halfPi,\n      da = abs(a1 - a0),\n      cw = a1 > a0;\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n        a11 = a1,\n        a00 = a0,\n        a10 = a1,\n        da0 = da,\n        da1 = da,\n        ap = padAngle.apply(this, arguments) / 2,\n        rp = ap > epsilon && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n        rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n        rc0 = rc,\n        rc1 = rc,\n        t0,\n        t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n          p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= cw ? 1 : -1, a00 += p0, a10 -= p0;else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= cw ? 1 : -1, a01 += p1, a11 -= p1;else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n      var x01 = r1 * cos(a01),\n        y01 = r1 * sin(a01),\n        x10 = r0 * cos(a10),\n        y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n          y11 = r1 * sin(a11),\n          x00 = r0 * cos(a00),\n          y00 = r0 * sin(a00),\n          oc;\n\n        // Restrict the corner radius according to the sector angle. If this\n        // intersection fails, it’s probably because the arc is too small, so\n        // disable the corner radius entirely.\n        if (da < pi) {\n          if (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10)) {\n            var ax = x01 - oc[0],\n              ay = y01 - oc[1],\n              bx = x11 - oc[0],\n              by = y11 - oc[1],\n              kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n              lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n            rc0 = min(rc, (r0 - lc) / (kc - 1));\n            rc1 = min(rc, (r1 - lc) / (kc + 1));\n          } else {\n            rc0 = rc1 = 0;\n          }\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n    context.closePath();\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n  arc.centroid = function () {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n      a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n  arc.innerRadius = function (_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n  arc.outerRadius = function (_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n  arc.cornerRadius = function (_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n  arc.padRadius = function (_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n  arc.startAngle = function (_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n  arc.endAngle = function (_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n  arc.padAngle = function (_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n  arc.context = function (_) {\n    return arguments.length ? (context = _ == null ? null : _, arc) : context;\n  };\n  return arc;\n}", "map": {"version": 3, "names": ["constant", "abs", "acos", "asin", "atan2", "cos", "epsilon", "halfPi", "max", "min", "pi", "sin", "sqrt", "tau", "with<PERSON><PERSON>", "arcInnerRadius", "d", "innerRadius", "arcOuterRadius", "outerRadius", "arcStartAngle", "startAngle", "arcEndAngle", "endAngle", "arcPadAngle", "padAngle", "intersect", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "x10", "y10", "x32", "y32", "t", "cornerTangents", "r1", "rc", "cw", "x01", "y01", "lo", "ox", "oy", "x11", "y11", "x00", "y00", "dx", "dy", "d2", "r", "D", "cx0", "cy0", "cx1", "cy1", "dx0", "dy0", "dx1", "dy1", "cx", "cy", "cornerRadius", "padRadius", "context", "path", "arc", "buffer", "r0", "apply", "arguments", "a0", "a1", "da", "moveTo", "a01", "a11", "a00", "a10", "da0", "da1", "ap", "rp", "rc0", "rc1", "t0", "t1", "p0", "p1", "oc", "ax", "ay", "bx", "by", "kc", "lc", "lineTo", "closePath", "centroid", "a", "_", "length"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-shape/src/arc.js"], "sourcesContent": ["import constant from \"./constant.js\";\nimport {abs, acos, asin, atan2, cos, epsilon, halfPi, max, min, pi, sin, sqrt, tau} from \"./math.js\";\nimport {withPath} from \"./path.js\";\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nexport default function() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null,\n      path = withPath(arc);\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle. If this\n        // intersection fails, it’s probably because the arc is too small, so\n        // disable the corner radius entirely.\n        if (da < pi) {\n          if (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10)) {\n            var ax = x01 - oc[0],\n                ay = y01 - oc[1],\n                bx = x11 - oc[0],\n                by = y11 - oc[1],\n                kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n                lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n            rc0 = min(rc, (r0 - lc) / (kc - 1));\n            rc1 = min(rc, (r1 - lc) / (kc + 1));\n          } else {\n            rc0 = rc1 = 0;\n          }\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,SAAQC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,QAAO,WAAW;AACpG,SAAQC,QAAQ,QAAO,WAAW;AAElC,SAASC,cAAcA,CAACC,CAAC,EAAE;EACzB,OAAOA,CAAC,CAACC,WAAW;AACtB;AAEA,SAASC,cAAcA,CAACF,CAAC,EAAE;EACzB,OAAOA,CAAC,CAACG,WAAW;AACtB;AAEA,SAASC,aAAaA,CAACJ,CAAC,EAAE;EACxB,OAAOA,CAAC,CAACK,UAAU;AACrB;AAEA,SAASC,WAAWA,CAACN,CAAC,EAAE;EACtB,OAAOA,CAAC,CAACO,QAAQ;AACnB;AAEA,SAASC,WAAWA,CAACR,CAAC,EAAE;EACtB,OAAOA,CAAC,IAAIA,CAAC,CAACS,QAAQ,CAAC,CAAC;AAC1B;AAEA,SAASC,SAASA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACjD,IAAIC,GAAG,GAAGN,EAAE,GAAGF,EAAE;IAAES,GAAG,GAAGN,EAAE,GAAGF,EAAE;IAC5BS,GAAG,GAAGJ,EAAE,GAAGF,EAAE;IAAEO,GAAG,GAAGJ,EAAE,GAAGF,EAAE;IAC5BO,CAAC,GAAGD,GAAG,GAAGH,GAAG,GAAGE,GAAG,GAAGD,GAAG;EAC7B,IAAIG,CAAC,GAAGA,CAAC,GAAGjC,OAAO,EAAE;EACrBiC,CAAC,GAAG,CAACF,GAAG,IAAIT,EAAE,GAAGI,EAAE,CAAC,GAAGM,GAAG,IAAIX,EAAE,GAAGI,EAAE,CAAC,IAAIQ,CAAC;EAC3C,OAAO,CAACZ,EAAE,GAAGY,CAAC,GAAGJ,GAAG,EAAEP,EAAE,GAAGW,CAAC,GAAGH,GAAG,CAAC;AACrC;;AAEA;AACA;AACA,SAASI,cAAcA,CAACb,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAClD,IAAIC,GAAG,GAAGjB,EAAE,GAAGE,EAAE;IACbgB,GAAG,GAAGjB,EAAE,GAAGE,EAAE;IACbgB,EAAE,GAAG,CAACH,EAAE,GAAGD,EAAE,GAAG,CAACA,EAAE,IAAI9B,IAAI,CAACgC,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,CAAC;IAClDE,EAAE,GAAGD,EAAE,GAAGD,GAAG;IACbG,EAAE,GAAG,CAACF,EAAE,GAAGF,GAAG;IACdK,GAAG,GAAGtB,EAAE,GAAGoB,EAAE;IACbG,GAAG,GAAGtB,EAAE,GAAGoB,EAAE;IACbb,GAAG,GAAGN,EAAE,GAAGkB,EAAE;IACbX,GAAG,GAAGN,EAAE,GAAGkB,EAAE;IACbG,GAAG,GAAG,CAACF,GAAG,GAAGd,GAAG,IAAI,CAAC;IACrBiB,GAAG,GAAG,CAACF,GAAG,GAAGd,GAAG,IAAI,CAAC;IACrBiB,EAAE,GAAGlB,GAAG,GAAGc,GAAG;IACdK,EAAE,GAAGlB,GAAG,GAAGc,GAAG;IACdK,EAAE,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IACtBE,CAAC,GAAGf,EAAE,GAAGC,EAAE;IACXe,CAAC,GAAGR,GAAG,GAAGb,GAAG,GAAGD,GAAG,GAAGe,GAAG;IACzBlC,CAAC,GAAG,CAACsC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI1C,IAAI,CAACJ,GAAG,CAAC,CAAC,EAAEgD,CAAC,GAAGA,CAAC,GAAGD,EAAE,GAAGE,CAAC,GAAGA,CAAC,CAAC,CAAC;IACxDC,GAAG,GAAG,CAACD,CAAC,GAAGH,EAAE,GAAGD,EAAE,GAAGrC,CAAC,IAAIuC,EAAE;IAC5BI,GAAG,GAAG,CAAC,CAACF,CAAC,GAAGJ,EAAE,GAAGC,EAAE,GAAGtC,CAAC,IAAIuC,EAAE;IAC7BK,GAAG,GAAG,CAACH,CAAC,GAAGH,EAAE,GAAGD,EAAE,GAAGrC,CAAC,IAAIuC,EAAE;IAC5BM,GAAG,GAAG,CAAC,CAACJ,CAAC,GAAGJ,EAAE,GAAGC,EAAE,GAAGtC,CAAC,IAAIuC,EAAE;IAC7BO,GAAG,GAAGJ,GAAG,GAAGP,GAAG;IACfY,GAAG,GAAGJ,GAAG,GAAGP,GAAG;IACfY,GAAG,GAAGJ,GAAG,GAAGT,GAAG;IACfc,GAAG,GAAGJ,GAAG,GAAGT,GAAG;;EAEnB;EACA;EACA,IAAIU,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,EAAEP,GAAG,GAAGE,GAAG,EAAED,GAAG,GAAGE,GAAG;EAEvE,OAAO;IACLK,EAAE,EAAER,GAAG;IACPS,EAAE,EAAER,GAAG;IACPf,GAAG,EAAE,CAACG,EAAE;IACRF,GAAG,EAAE,CAACG,EAAE;IACRC,GAAG,EAAES,GAAG,IAAIjB,EAAE,GAAGe,CAAC,GAAG,CAAC,CAAC;IACvBN,GAAG,EAAES,GAAG,IAAIlB,EAAE,GAAGe,CAAC,GAAG,CAAC;EACxB,CAAC;AACH;AAEA,eAAe,YAAW;EACxB,IAAIvC,WAAW,GAAGF,cAAc;IAC5BI,WAAW,GAAGD,cAAc;IAC5BkD,YAAY,GAAGpE,QAAQ,CAAC,CAAC,CAAC;IAC1BqE,SAAS,GAAG,IAAI;IAChBhD,UAAU,GAAGD,aAAa;IAC1BG,QAAQ,GAAGD,WAAW;IACtBG,QAAQ,GAAGD,WAAW;IACtB8C,OAAO,GAAG,IAAI;IACdC,IAAI,GAAGzD,QAAQ,CAAC0D,GAAG,CAAC;EAExB,SAASA,GAAGA,CAAA,EAAG;IACb,IAAIC,MAAM;MACNjB,CAAC;MACDkB,EAAE,GAAG,CAACzD,WAAW,CAAC0D,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACxCnC,EAAE,GAAG,CAACtB,WAAW,CAACwD,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACxCC,EAAE,GAAGxD,UAAU,CAACsD,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAGrE,MAAM;MAC/CuE,EAAE,GAAGvD,QAAQ,CAACoD,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAGrE,MAAM;MAC7CwE,EAAE,GAAG9E,GAAG,CAAC6E,EAAE,GAAGD,EAAE,CAAC;MACjBlC,EAAE,GAAGmC,EAAE,GAAGD,EAAE;IAEhB,IAAI,CAACP,OAAO,EAAEA,OAAO,GAAGG,MAAM,GAAGF,IAAI,CAAC,CAAC;;IAEvC;IACA,IAAI9B,EAAE,GAAGiC,EAAE,EAAElB,CAAC,GAAGf,EAAE,EAAEA,EAAE,GAAGiC,EAAE,EAAEA,EAAE,GAAGlB,CAAC;;IAEpC;IACA,IAAI,EAAEf,EAAE,GAAGnC,OAAO,CAAC,EAAEgE,OAAO,CAACU,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;IAEzC;IAAA,KACK,IAAID,EAAE,GAAGlE,GAAG,GAAGP,OAAO,EAAE;MAC3BgE,OAAO,CAACU,MAAM,CAACvC,EAAE,GAAGpC,GAAG,CAACwE,EAAE,CAAC,EAAEpC,EAAE,GAAG9B,GAAG,CAACkE,EAAE,CAAC,CAAC;MAC1CP,OAAO,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE/B,EAAE,EAAEoC,EAAE,EAAEC,EAAE,EAAE,CAACnC,EAAE,CAAC;MAClC,IAAI+B,EAAE,GAAGpE,OAAO,EAAE;QAChBgE,OAAO,CAACU,MAAM,CAACN,EAAE,GAAGrE,GAAG,CAACyE,EAAE,CAAC,EAAEJ,EAAE,GAAG/D,GAAG,CAACmE,EAAE,CAAC,CAAC;QAC1CR,OAAO,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEE,EAAE,EAAEI,EAAE,EAAED,EAAE,EAAElC,EAAE,CAAC;MACnC;IACF;;IAEA;IAAA,KACK;MACH,IAAIsC,GAAG,GAAGJ,EAAE;QACRK,GAAG,GAAGJ,EAAE;QACRK,GAAG,GAAGN,EAAE;QACRO,GAAG,GAAGN,EAAE;QACRO,GAAG,GAAGN,EAAE;QACRO,GAAG,GAAGP,EAAE;QACRQ,EAAE,GAAG9D,QAAQ,CAACkD,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAG,CAAC;QACxCY,EAAE,GAAID,EAAE,GAAGjF,OAAO,KAAM+D,SAAS,GAAG,CAACA,SAAS,CAACM,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAGhE,IAAI,CAAC8D,EAAE,GAAGA,EAAE,GAAGjC,EAAE,GAAGA,EAAE,CAAC,CAAC;QAChGC,EAAE,GAAGjC,GAAG,CAACR,GAAG,CAACwC,EAAE,GAAGiC,EAAE,CAAC,GAAG,CAAC,EAAE,CAACN,YAAY,CAACO,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;QAChEa,GAAG,GAAG/C,EAAE;QACRgD,GAAG,GAAGhD,EAAE;QACRiD,EAAE;QACFC,EAAE;;MAEN;MACA,IAAIJ,EAAE,GAAGlF,OAAO,EAAE;QAChB,IAAIuF,EAAE,GAAG1F,IAAI,CAACqF,EAAE,GAAGd,EAAE,GAAG/D,GAAG,CAAC4E,EAAE,CAAC,CAAC;UAC5BO,EAAE,GAAG3F,IAAI,CAACqF,EAAE,GAAG/C,EAAE,GAAG9B,GAAG,CAAC4E,EAAE,CAAC,CAAC;QAChC,IAAI,CAACF,GAAG,IAAIQ,EAAE,GAAG,CAAC,IAAIvF,OAAO,EAAEuF,EAAE,IAAKlD,EAAE,GAAG,CAAC,GAAG,CAAC,CAAE,EAAEwC,GAAG,IAAIU,EAAE,EAAET,GAAG,IAAIS,EAAE,CAAC,KACpER,GAAG,GAAG,CAAC,EAAEF,GAAG,GAAGC,GAAG,GAAG,CAACP,EAAE,GAAGC,EAAE,IAAI,CAAC;QACvC,IAAI,CAACQ,GAAG,IAAIQ,EAAE,GAAG,CAAC,IAAIxF,OAAO,EAAEwF,EAAE,IAAKnD,EAAE,GAAG,CAAC,GAAG,CAAC,CAAE,EAAEsC,GAAG,IAAIa,EAAE,EAAEZ,GAAG,IAAIY,EAAE,CAAC,KACpER,GAAG,GAAG,CAAC,EAAEL,GAAG,GAAGC,GAAG,GAAG,CAACL,EAAE,GAAGC,EAAE,IAAI,CAAC;MACzC;MAEA,IAAIlC,GAAG,GAAGH,EAAE,GAAGpC,GAAG,CAAC4E,GAAG,CAAC;QACnBpC,GAAG,GAAGJ,EAAE,GAAG9B,GAAG,CAACsE,GAAG,CAAC;QACnB9C,GAAG,GAAGuC,EAAE,GAAGrE,GAAG,CAAC+E,GAAG,CAAC;QACnBhD,GAAG,GAAGsC,EAAE,GAAG/D,GAAG,CAACyE,GAAG,CAAC;;MAEvB;MACA,IAAI1C,EAAE,GAAGpC,OAAO,EAAE;QAChB,IAAI2C,GAAG,GAAGR,EAAE,GAAGpC,GAAG,CAAC6E,GAAG,CAAC;UACnBhC,GAAG,GAAGT,EAAE,GAAG9B,GAAG,CAACuE,GAAG,CAAC;UACnB/B,GAAG,GAAGuB,EAAE,GAAGrE,GAAG,CAAC8E,GAAG,CAAC;UACnB/B,GAAG,GAAGsB,EAAE,GAAG/D,GAAG,CAACwE,GAAG,CAAC;UACnBY,EAAE;;QAEN;QACA;QACA;QACA,IAAIhB,EAAE,GAAGrE,EAAE,EAAE;UACX,IAAIqF,EAAE,GAAGrE,SAAS,CAACkB,GAAG,EAAEC,GAAG,EAAEM,GAAG,EAAEC,GAAG,EAAEH,GAAG,EAAEC,GAAG,EAAEf,GAAG,EAAEC,GAAG,CAAC,EAAE;YAC1D,IAAI4D,EAAE,GAAGpD,GAAG,GAAGmD,EAAE,CAAC,CAAC,CAAC;cAChBE,EAAE,GAAGpD,GAAG,GAAGkD,EAAE,CAAC,CAAC,CAAC;cAChBG,EAAE,GAAGjD,GAAG,GAAG8C,EAAE,CAAC,CAAC,CAAC;cAChBI,EAAE,GAAGjD,GAAG,GAAG6C,EAAE,CAAC,CAAC,CAAC;cAChBK,EAAE,GAAG,CAAC,GAAGzF,GAAG,CAACT,IAAI,CAAC,CAAC8F,EAAE,GAAGE,EAAE,GAAGD,EAAE,GAAGE,EAAE,KAAKvF,IAAI,CAACoF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC,GAAGrF,IAAI,CAACsF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cACjGE,EAAE,GAAGzF,IAAI,CAACmF,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC;YAC5CN,GAAG,GAAGhF,GAAG,CAACiC,EAAE,EAAE,CAACgC,EAAE,GAAG2B,EAAE,KAAKD,EAAE,GAAG,CAAC,CAAC,CAAC;YACnCV,GAAG,GAAGjF,GAAG,CAACiC,EAAE,EAAE,CAACD,EAAE,GAAG4D,EAAE,KAAKD,EAAE,GAAG,CAAC,CAAC,CAAC;UACrC,CAAC,MAAM;YACLX,GAAG,GAAGC,GAAG,GAAG,CAAC;UACf;QACF;MACF;;MAEA;MACA,IAAI,EAAEJ,GAAG,GAAGhF,OAAO,CAAC,EAAEgE,OAAO,CAACU,MAAM,CAACpC,GAAG,EAAEC,GAAG,CAAC;;MAE9C;MAAA,KACK,IAAI6C,GAAG,GAAGpF,OAAO,EAAE;QACtBqF,EAAE,GAAGnD,cAAc,CAACW,GAAG,EAAEC,GAAG,EAAER,GAAG,EAAEC,GAAG,EAAEJ,EAAE,EAAEiD,GAAG,EAAE/C,EAAE,CAAC;QACpDiD,EAAE,GAAGpD,cAAc,CAACS,GAAG,EAAEC,GAAG,EAAEf,GAAG,EAAEC,GAAG,EAAEK,EAAE,EAAEiD,GAAG,EAAE/C,EAAE,CAAC;QAEpD2B,OAAO,CAACU,MAAM,CAACW,EAAE,CAACzB,EAAE,GAAGyB,EAAE,CAAC/C,GAAG,EAAE+C,EAAE,CAACxB,EAAE,GAAGwB,EAAE,CAAC9C,GAAG,CAAC;;QAE9C;QACA,IAAI6C,GAAG,GAAGhD,EAAE,EAAE4B,OAAO,CAACE,GAAG,CAACmB,EAAE,CAACzB,EAAE,EAAEyB,EAAE,CAACxB,EAAE,EAAEuB,GAAG,EAAEtF,KAAK,CAACuF,EAAE,CAAC9C,GAAG,EAAE8C,EAAE,CAAC/C,GAAG,CAAC,EAAExC,KAAK,CAACwF,EAAE,CAAC/C,GAAG,EAAE+C,EAAE,CAAChD,GAAG,CAAC,EAAE,CAACD,EAAE,CAAC;;QAE/F;QAAA,KACK;UACH2B,OAAO,CAACE,GAAG,CAACmB,EAAE,CAACzB,EAAE,EAAEyB,EAAE,CAACxB,EAAE,EAAEuB,GAAG,EAAEtF,KAAK,CAACuF,EAAE,CAAC9C,GAAG,EAAE8C,EAAE,CAAC/C,GAAG,CAAC,EAAExC,KAAK,CAACuF,EAAE,CAACzC,GAAG,EAAEyC,EAAE,CAAC1C,GAAG,CAAC,EAAE,CAACN,EAAE,CAAC;UACjF2B,OAAO,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE/B,EAAE,EAAErC,KAAK,CAACuF,EAAE,CAACxB,EAAE,GAAGwB,EAAE,CAACzC,GAAG,EAAEyC,EAAE,CAACzB,EAAE,GAAGyB,EAAE,CAAC1C,GAAG,CAAC,EAAE7C,KAAK,CAACwF,EAAE,CAACzB,EAAE,GAAGyB,EAAE,CAAC1C,GAAG,EAAE0C,EAAE,CAAC1B,EAAE,GAAG0B,EAAE,CAAC3C,GAAG,CAAC,EAAE,CAACN,EAAE,CAAC;UACxG2B,OAAO,CAACE,GAAG,CAACoB,EAAE,CAAC1B,EAAE,EAAE0B,EAAE,CAACzB,EAAE,EAAEuB,GAAG,EAAEtF,KAAK,CAACwF,EAAE,CAAC1C,GAAG,EAAE0C,EAAE,CAAC3C,GAAG,CAAC,EAAE7C,KAAK,CAACwF,EAAE,CAAC/C,GAAG,EAAE+C,EAAE,CAAChD,GAAG,CAAC,EAAE,CAACD,EAAE,CAAC;QACnF;MACF;;MAEA;MAAA,KACK2B,OAAO,CAACU,MAAM,CAACpC,GAAG,EAAEC,GAAG,CAAC,EAAEyB,OAAO,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE/B,EAAE,EAAEwC,GAAG,EAAEC,GAAG,EAAE,CAACvC,EAAE,CAAC;;MAEnE;MACA;MACA,IAAI,EAAE+B,EAAE,GAAGpE,OAAO,CAAC,IAAI,EAAE+E,GAAG,GAAG/E,OAAO,CAAC,EAAEgE,OAAO,CAACgC,MAAM,CAACnE,GAAG,EAAEC,GAAG,CAAC;;MAEjE;MAAA,KACK,IAAIqD,GAAG,GAAGnF,OAAO,EAAE;QACtBqF,EAAE,GAAGnD,cAAc,CAACL,GAAG,EAAEC,GAAG,EAAEa,GAAG,EAAEC,GAAG,EAAEwB,EAAE,EAAE,CAACe,GAAG,EAAE9C,EAAE,CAAC;QACrDiD,EAAE,GAAGpD,cAAc,CAACI,GAAG,EAAEC,GAAG,EAAEM,GAAG,EAAEC,GAAG,EAAEsB,EAAE,EAAE,CAACe,GAAG,EAAE9C,EAAE,CAAC;QAErD2B,OAAO,CAACgC,MAAM,CAACX,EAAE,CAACzB,EAAE,GAAGyB,EAAE,CAAC/C,GAAG,EAAE+C,EAAE,CAACxB,EAAE,GAAGwB,EAAE,CAAC9C,GAAG,CAAC;;QAE9C;QACA,IAAI4C,GAAG,GAAG/C,EAAE,EAAE4B,OAAO,CAACE,GAAG,CAACmB,EAAE,CAACzB,EAAE,EAAEyB,EAAE,CAACxB,EAAE,EAAEsB,GAAG,EAAErF,KAAK,CAACuF,EAAE,CAAC9C,GAAG,EAAE8C,EAAE,CAAC/C,GAAG,CAAC,EAAExC,KAAK,CAACwF,EAAE,CAAC/C,GAAG,EAAE+C,EAAE,CAAChD,GAAG,CAAC,EAAE,CAACD,EAAE,CAAC;;QAE/F;QAAA,KACK;UACH2B,OAAO,CAACE,GAAG,CAACmB,EAAE,CAACzB,EAAE,EAAEyB,EAAE,CAACxB,EAAE,EAAEsB,GAAG,EAAErF,KAAK,CAACuF,EAAE,CAAC9C,GAAG,EAAE8C,EAAE,CAAC/C,GAAG,CAAC,EAAExC,KAAK,CAACuF,EAAE,CAACzC,GAAG,EAAEyC,EAAE,CAAC1C,GAAG,CAAC,EAAE,CAACN,EAAE,CAAC;UACjF2B,OAAO,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEE,EAAE,EAAEtE,KAAK,CAACuF,EAAE,CAACxB,EAAE,GAAGwB,EAAE,CAACzC,GAAG,EAAEyC,EAAE,CAACzB,EAAE,GAAGyB,EAAE,CAAC1C,GAAG,CAAC,EAAE7C,KAAK,CAACwF,EAAE,CAACzB,EAAE,GAAGyB,EAAE,CAAC1C,GAAG,EAAE0C,EAAE,CAAC1B,EAAE,GAAG0B,EAAE,CAAC3C,GAAG,CAAC,EAAEN,EAAE,CAAC;UACvG2B,OAAO,CAACE,GAAG,CAACoB,EAAE,CAAC1B,EAAE,EAAE0B,EAAE,CAACzB,EAAE,EAAEsB,GAAG,EAAErF,KAAK,CAACwF,EAAE,CAAC1C,GAAG,EAAE0C,EAAE,CAAC3C,GAAG,CAAC,EAAE7C,KAAK,CAACwF,EAAE,CAAC/C,GAAG,EAAE+C,EAAE,CAAChD,GAAG,CAAC,EAAE,CAACD,EAAE,CAAC;QACnF;MACF;;MAEA;MAAA,KACK2B,OAAO,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEE,EAAE,EAAEU,GAAG,EAAED,GAAG,EAAExC,EAAE,CAAC;IAC1C;IAEA2B,OAAO,CAACiC,SAAS,CAAC,CAAC;IAEnB,IAAI9B,MAAM,EAAE,OAAOH,OAAO,GAAG,IAAI,EAAEG,MAAM,GAAG,EAAE,IAAI,IAAI;EACxD;EAEAD,GAAG,CAACgC,QAAQ,GAAG,YAAW;IACxB,IAAIhD,CAAC,GAAG,CAAC,CAACvC,WAAW,CAAC0D,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAG,CAACzD,WAAW,CAACwD,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,CAAC;MACnF6B,CAAC,GAAG,CAAC,CAACpF,UAAU,CAACsD,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAG,CAACrD,QAAQ,CAACoD,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,CAAC,GAAGlE,EAAE,GAAG,CAAC;IAC5F,OAAO,CAACL,GAAG,CAACoG,CAAC,CAAC,GAAGjD,CAAC,EAAE7C,GAAG,CAAC8F,CAAC,CAAC,GAAGjD,CAAC,CAAC;EACjC,CAAC;EAEDgB,GAAG,CAACvD,WAAW,GAAG,UAASyF,CAAC,EAAE;IAC5B,OAAO9B,SAAS,CAAC+B,MAAM,IAAI1F,WAAW,GAAG,OAAOyF,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG1G,QAAQ,CAAC,CAAC0G,CAAC,CAAC,EAAElC,GAAG,IAAIvD,WAAW;EACzG,CAAC;EAEDuD,GAAG,CAACrD,WAAW,GAAG,UAASuF,CAAC,EAAE;IAC5B,OAAO9B,SAAS,CAAC+B,MAAM,IAAIxF,WAAW,GAAG,OAAOuF,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG1G,QAAQ,CAAC,CAAC0G,CAAC,CAAC,EAAElC,GAAG,IAAIrD,WAAW;EACzG,CAAC;EAEDqD,GAAG,CAACJ,YAAY,GAAG,UAASsC,CAAC,EAAE;IAC7B,OAAO9B,SAAS,CAAC+B,MAAM,IAAIvC,YAAY,GAAG,OAAOsC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG1G,QAAQ,CAAC,CAAC0G,CAAC,CAAC,EAAElC,GAAG,IAAIJ,YAAY;EAC3G,CAAC;EAEDI,GAAG,CAACH,SAAS,GAAG,UAASqC,CAAC,EAAE;IAC1B,OAAO9B,SAAS,CAAC+B,MAAM,IAAItC,SAAS,GAAGqC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAOA,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG1G,QAAQ,CAAC,CAAC0G,CAAC,CAAC,EAAElC,GAAG,IAAIH,SAAS;EACxH,CAAC;EAEDG,GAAG,CAACnD,UAAU,GAAG,UAASqF,CAAC,EAAE;IAC3B,OAAO9B,SAAS,CAAC+B,MAAM,IAAItF,UAAU,GAAG,OAAOqF,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG1G,QAAQ,CAAC,CAAC0G,CAAC,CAAC,EAAElC,GAAG,IAAInD,UAAU;EACvG,CAAC;EAEDmD,GAAG,CAACjD,QAAQ,GAAG,UAASmF,CAAC,EAAE;IACzB,OAAO9B,SAAS,CAAC+B,MAAM,IAAIpF,QAAQ,GAAG,OAAOmF,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG1G,QAAQ,CAAC,CAAC0G,CAAC,CAAC,EAAElC,GAAG,IAAIjD,QAAQ;EACnG,CAAC;EAEDiD,GAAG,CAAC/C,QAAQ,GAAG,UAASiF,CAAC,EAAE;IACzB,OAAO9B,SAAS,CAAC+B,MAAM,IAAIlF,QAAQ,GAAG,OAAOiF,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG1G,QAAQ,CAAC,CAAC0G,CAAC,CAAC,EAAElC,GAAG,IAAI/C,QAAQ;EACnG,CAAC;EAED+C,GAAG,CAACF,OAAO,GAAG,UAASoC,CAAC,EAAE;IACxB,OAAO9B,SAAS,CAAC+B,MAAM,IAAKrC,OAAO,GAAGoC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGA,CAAC,EAAGlC,GAAG,IAAIF,OAAO;EAC7E,CAAC;EAED,OAAOE,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}