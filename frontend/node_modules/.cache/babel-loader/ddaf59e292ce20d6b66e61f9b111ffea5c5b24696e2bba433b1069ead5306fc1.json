{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nvar _excluded = [\"offset\"];\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport React, { cloneElement, isValidElement, createElement } from 'react';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport isObject from 'lodash/isObject';\nimport clsx from 'clsx';\nimport { Text } from './Text';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { isNumOrStr, isNumber, isPercent, getPercentValue, uniqueId, mathSign } from '../util/DataUtils';\nimport { polarToCartesian } from '../util/PolarUtils';\nvar getLabel = function getLabel(props) {\n  var value = props.value,\n    formatter = props.formatter;\n  var label = isNil(props.children) ? value : props.children;\n  if (isFunction(formatter)) {\n    return formatter(label);\n  }\n  return label;\n};\nvar getDeltaAngle = function getDeltaAngle(startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n};\nvar renderRadialLabel = function renderRadialLabel(labelProps, label, attrs) {\n  var position = labelProps.position,\n    viewBox = labelProps.viewBox,\n    offset = labelProps.offset,\n    className = labelProps.className;\n  var _ref = viewBox,\n    cx = _ref.cx,\n    cy = _ref.cy,\n    innerRadius = _ref.innerRadius,\n    outerRadius = _ref.outerRadius,\n    startAngle = _ref.startAngle,\n    endAngle = _ref.endAngle,\n    clockWise = _ref.clockWise;\n  var radius = (innerRadius + outerRadius) / 2;\n  var deltaAngle = getDeltaAngle(startAngle, endAngle);\n  var sign = deltaAngle >= 0 ? 1 : -1;\n  var labelAngle, direction;\n  if (position === 'insideStart') {\n    labelAngle = startAngle + sign * offset;\n    direction = clockWise;\n  } else if (position === 'insideEnd') {\n    labelAngle = endAngle - sign * offset;\n    direction = !clockWise;\n  } else if (position === 'end') {\n    labelAngle = endAngle + sign * offset;\n    direction = clockWise;\n  }\n  direction = deltaAngle <= 0 ? direction : !direction;\n  var startPoint = polarToCartesian(cx, cy, radius, labelAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, labelAngle + (direction ? 1 : -1) * 359);\n  var path = \"M\".concat(startPoint.x, \",\").concat(startPoint.y, \"\\n    A\").concat(radius, \",\").concat(radius, \",0,1,\").concat(direction ? 0 : 1, \",\\n    \").concat(endPoint.x, \",\").concat(endPoint.y);\n  var id = isNil(labelProps.id) ? uniqueId('recharts-radial-line-') : labelProps.id;\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, attrs, {\n    dominantBaseline: \"central\",\n    className: clsx('recharts-radial-bar-label', className)\n  }), /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"path\", {\n    id: id,\n    d: path\n  })), /*#__PURE__*/React.createElement(\"textPath\", {\n    xlinkHref: \"#\".concat(id)\n  }, label));\n};\nvar getAttrsOfPolarLabel = function getAttrsOfPolarLabel(props) {\n  var viewBox = props.viewBox,\n    offset = props.offset,\n    position = props.position;\n  var _ref2 = viewBox,\n    cx = _ref2.cx,\n    cy = _ref2.cy,\n    innerRadius = _ref2.innerRadius,\n    outerRadius = _ref2.outerRadius,\n    startAngle = _ref2.startAngle,\n    endAngle = _ref2.endAngle;\n  var midAngle = (startAngle + endAngle) / 2;\n  if (position === 'outside') {\n    var _polarToCartesian = polarToCartesian(cx, cy, outerRadius + offset, midAngle),\n      _x = _polarToCartesian.x,\n      _y = _polarToCartesian.y;\n    return {\n      x: _x,\n      y: _y,\n      textAnchor: _x >= cx ? 'start' : 'end',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'center') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'centerTop') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'start'\n    };\n  }\n  if (position === 'centerBottom') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'end'\n    };\n  }\n  var r = (innerRadius + outerRadius) / 2;\n  var _polarToCartesian2 = polarToCartesian(cx, cy, r, midAngle),\n    x = _polarToCartesian2.x,\n    y = _polarToCartesian2.y;\n  return {\n    x: x,\n    y: y,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  };\n};\nvar getAttrsOfCartesianLabel = function getAttrsOfCartesianLabel(props) {\n  var viewBox = props.viewBox,\n    parentViewBox = props.parentViewBox,\n    offset = props.offset,\n    position = props.position;\n  var _ref3 = viewBox,\n    x = _ref3.x,\n    y = _ref3.y,\n    width = _ref3.width,\n    height = _ref3.height;\n\n  // Define vertical offsets and position inverts based on the value being positive or negative\n  var verticalSign = height >= 0 ? 1 : -1;\n  var verticalOffset = verticalSign * offset;\n  var verticalEnd = verticalSign > 0 ? 'end' : 'start';\n  var verticalStart = verticalSign > 0 ? 'start' : 'end';\n\n  // Define horizontal offsets and position inverts based on the value being positive or negative\n  var horizontalSign = width >= 0 ? 1 : -1;\n  var horizontalOffset = horizontalSign * offset;\n  var horizontalEnd = horizontalSign > 0 ? 'end' : 'start';\n  var horizontalStart = horizontalSign > 0 ? 'start' : 'end';\n  if (position === 'top') {\n    var attrs = {\n      x: x + width / 2,\n      y: y - verticalSign * offset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    };\n    return _objectSpread(_objectSpread({}, attrs), parentViewBox ? {\n      height: Math.max(y - parentViewBox.y, 0),\n      width: width\n    } : {});\n  }\n  if (position === 'bottom') {\n    var _attrs = {\n      x: x + width / 2,\n      y: y + height + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    };\n    return _objectSpread(_objectSpread({}, _attrs), parentViewBox ? {\n      height: Math.max(parentViewBox.y + parentViewBox.height - (y + height), 0),\n      width: width\n    } : {});\n  }\n  if (position === 'left') {\n    var _attrs2 = {\n      x: x - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs2), parentViewBox ? {\n      width: Math.max(_attrs2.x - parentViewBox.x, 0),\n      height: height\n    } : {});\n  }\n  if (position === 'right') {\n    var _attrs3 = {\n      x: x + width + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs3), parentViewBox ? {\n      width: Math.max(parentViewBox.x + parentViewBox.width - _attrs3.x, 0),\n      height: height\n    } : {});\n  }\n  var sizeAttrs = parentViewBox ? {\n    width: width,\n    height: height\n  } : {};\n  if (position === 'insideLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideTop') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottom') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + height - verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (isObject(position) && (isNumber(position.x) || isPercent(position.x)) && (isNumber(position.y) || isPercent(position.y))) {\n    return _objectSpread({\n      x: x + getPercentValue(position.x, width),\n      y: y + getPercentValue(position.y, height),\n      textAnchor: 'end',\n      verticalAnchor: 'end'\n    }, sizeAttrs);\n  }\n  return _objectSpread({\n    x: x + width / 2,\n    y: y + height / 2,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  }, sizeAttrs);\n};\nvar isPolar = function isPolar(viewBox) {\n  return 'cx' in viewBox && isNumber(viewBox.cx);\n};\nexport function Label(_ref4) {\n  var _ref4$offset = _ref4.offset,\n    offset = _ref4$offset === void 0 ? 5 : _ref4$offset,\n    restProps = _objectWithoutProperties(_ref4, _excluded);\n  var props = _objectSpread({\n    offset: offset\n  }, restProps);\n  var viewBox = props.viewBox,\n    position = props.position,\n    value = props.value,\n    children = props.children,\n    content = props.content,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    textBreakAll = props.textBreakAll;\n  if (!viewBox || isNil(value) && isNil(children) && ! /*#__PURE__*/isValidElement(content) && !isFunction(content)) {\n    return null;\n  }\n  if (/*#__PURE__*/isValidElement(content)) {\n    return /*#__PURE__*/cloneElement(content, props);\n  }\n  var label;\n  if (isFunction(content)) {\n    label = /*#__PURE__*/createElement(content, props);\n    if (/*#__PURE__*/isValidElement(label)) {\n      return label;\n    }\n  } else {\n    label = getLabel(props);\n  }\n  var isPolarLabel = isPolar(viewBox);\n  var attrs = filterProps(props, true);\n  if (isPolarLabel && (position === 'insideStart' || position === 'insideEnd' || position === 'end')) {\n    return renderRadialLabel(props, label, attrs);\n  }\n  var positionAttrs = isPolarLabel ? getAttrsOfPolarLabel(props) : getAttrsOfCartesianLabel(props);\n  return /*#__PURE__*/React.createElement(Text, _extends({\n    className: clsx('recharts-label', className)\n  }, attrs, positionAttrs, {\n    breakAll: textBreakAll\n  }), label);\n}\nLabel.displayName = 'Label';\nvar parseViewBox = function parseViewBox(props) {\n  var cx = props.cx,\n    cy = props.cy,\n    angle = props.angle,\n    startAngle = props.startAngle,\n    endAngle = props.endAngle,\n    r = props.r,\n    radius = props.radius,\n    innerRadius = props.innerRadius,\n    outerRadius = props.outerRadius,\n    x = props.x,\n    y = props.y,\n    top = props.top,\n    left = props.left,\n    width = props.width,\n    height = props.height,\n    clockWise = props.clockWise,\n    labelViewBox = props.labelViewBox;\n  if (labelViewBox) {\n    return labelViewBox;\n  }\n  if (isNumber(width) && isNumber(height)) {\n    if (isNumber(x) && isNumber(y)) {\n      return {\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      };\n    }\n    if (isNumber(top) && isNumber(left)) {\n      return {\n        x: top,\n        y: left,\n        width: width,\n        height: height\n      };\n    }\n  }\n  if (isNumber(x) && isNumber(y)) {\n    return {\n      x: x,\n      y: y,\n      width: 0,\n      height: 0\n    };\n  }\n  if (isNumber(cx) && isNumber(cy)) {\n    return {\n      cx: cx,\n      cy: cy,\n      startAngle: startAngle || angle || 0,\n      endAngle: endAngle || angle || 0,\n      innerRadius: innerRadius || 0,\n      outerRadius: outerRadius || radius || r || 0,\n      clockWise: clockWise\n    };\n  }\n  if (props.viewBox) {\n    return props.viewBox;\n  }\n  return {};\n};\nvar parseLabel = function parseLabel(label, viewBox) {\n  if (!label) {\n    return null;\n  }\n  if (label === true) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      viewBox: viewBox\n    });\n  }\n  if (isNumOrStr(label)) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      viewBox: viewBox,\n      value: label\n    });\n  }\n  if (/*#__PURE__*/isValidElement(label)) {\n    if (label.type === Label) {\n      return /*#__PURE__*/cloneElement(label, {\n        key: 'label-implicit',\n        viewBox: viewBox\n      });\n    }\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      content: label,\n      viewBox: viewBox\n    });\n  }\n  if (isFunction(label)) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      content: label,\n      viewBox: viewBox\n    });\n  }\n  if (isObject(label)) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      viewBox: viewBox\n    }, label, {\n      key: \"label-implicit\"\n    }));\n  }\n  return null;\n};\nvar renderCallByParent = function renderCallByParent(parentProps, viewBox) {\n  var checkPropsLabel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (!parentProps || !parentProps.children && checkPropsLabel && !parentProps.label) {\n    return null;\n  }\n  var children = parentProps.children;\n  var parentViewBox = parseViewBox(parentProps);\n  var explicitChildren = findAllByType(children, Label).map(function (child, index) {\n    return /*#__PURE__*/cloneElement(child, {\n      viewBox: viewBox || parentViewBox,\n      // eslint-disable-next-line react/no-array-index-key\n      key: \"label-\".concat(index)\n    });\n  });\n  if (!checkPropsLabel) {\n    return explicitChildren;\n  }\n  var implicitLabel = parseLabel(parentProps.label, viewBox || parentViewBox);\n  return [implicitLabel].concat(_toConsumableArray(explicitChildren));\n};\nLabel.parseViewBox = parseViewBox;\nLabel.renderCallByParent = renderCallByParent;", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_excluded", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "toString", "call", "slice", "name", "Array", "from", "test", "iter", "isArray", "len", "length", "i", "arr2", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "hasOwnProperty", "ownKeys", "e", "r", "t", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "_toPrimitive", "toPrimitive", "String", "Number", "_extends", "assign", "bind", "React", "cloneElement", "isValidElement", "createElement", "isNil", "isFunction", "isObject", "clsx", "Text", "findAllByType", "filterProps", "isNumOrStr", "isNumber", "isPercent", "getPercentValue", "uniqueId", "mathSign", "polarToCartesian", "get<PERSON><PERSON><PERSON>", "props", "formatter", "label", "children", "getDeltaAngle", "startAngle", "endAngle", "sign", "deltaAngle", "Math", "min", "abs", "renderRadialLabel", "labelProps", "attrs", "position", "viewBox", "offset", "className", "_ref", "cx", "cy", "innerRadius", "outerRadius", "clockWise", "radius", "labelAngle", "direction", "startPoint", "endPoint", "path", "concat", "x", "y", "id", "dominantBaseline", "d", "xlinkHref", "getAttrsOfPolarLabel", "_ref2", "midAngle", "_polarToCartesian", "_x", "_y", "textAnchor", "verticalAnchor", "_polarToCartesian2", "getAttrsOfCartesianLabel", "parentViewBox", "_ref3", "width", "height", "verticalSign", "verticalOffset", "verticalEnd", "verticalStart", "horizontalSign", "horizontalOffset", "horizontalEnd", "horizontalStart", "max", "_attrs", "_attrs2", "_attrs3", "sizeAttrs", "isPolar", "Label", "_ref4", "_ref4$offset", "restProps", "content", "_props$className", "textBreakAll", "isPolarLabel", "positionAttrs", "breakAll", "displayName", "parseViewBox", "angle", "top", "left", "labelViewBox", "parseLabel", "type", "renderCallByParent", "parentProps", "checkPropsLabel", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "child", "index", "implicit<PERSON><PERSON><PERSON>"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/component/Label.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar _excluded = [\"offset\"];\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nimport React, { cloneElement, isValidElement, createElement } from 'react';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport isObject from 'lodash/isObject';\nimport clsx from 'clsx';\nimport { Text } from './Text';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { isNumOrStr, isNumber, isPercent, getPercentValue, uniqueId, mathSign } from '../util/DataUtils';\nimport { polarToCartesian } from '../util/PolarUtils';\nvar getLabel = function getLabel(props) {\n  var value = props.value,\n    formatter = props.formatter;\n  var label = isNil(props.children) ? value : props.children;\n  if (isFunction(formatter)) {\n    return formatter(label);\n  }\n  return label;\n};\nvar getDeltaAngle = function getDeltaAngle(startAngle, endAngle) {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n};\nvar renderRadialLabel = function renderRadialLabel(labelProps, label, attrs) {\n  var position = labelProps.position,\n    viewBox = labelProps.viewBox,\n    offset = labelProps.offset,\n    className = labelProps.className;\n  var _ref = viewBox,\n    cx = _ref.cx,\n    cy = _ref.cy,\n    innerRadius = _ref.innerRadius,\n    outerRadius = _ref.outerRadius,\n    startAngle = _ref.startAngle,\n    endAngle = _ref.endAngle,\n    clockWise = _ref.clockWise;\n  var radius = (innerRadius + outerRadius) / 2;\n  var deltaAngle = getDeltaAngle(startAngle, endAngle);\n  var sign = deltaAngle >= 0 ? 1 : -1;\n  var labelAngle, direction;\n  if (position === 'insideStart') {\n    labelAngle = startAngle + sign * offset;\n    direction = clockWise;\n  } else if (position === 'insideEnd') {\n    labelAngle = endAngle - sign * offset;\n    direction = !clockWise;\n  } else if (position === 'end') {\n    labelAngle = endAngle + sign * offset;\n    direction = clockWise;\n  }\n  direction = deltaAngle <= 0 ? direction : !direction;\n  var startPoint = polarToCartesian(cx, cy, radius, labelAngle);\n  var endPoint = polarToCartesian(cx, cy, radius, labelAngle + (direction ? 1 : -1) * 359);\n  var path = \"M\".concat(startPoint.x, \",\").concat(startPoint.y, \"\\n    A\").concat(radius, \",\").concat(radius, \",0,1,\").concat(direction ? 0 : 1, \",\\n    \").concat(endPoint.x, \",\").concat(endPoint.y);\n  var id = isNil(labelProps.id) ? uniqueId('recharts-radial-line-') : labelProps.id;\n  return /*#__PURE__*/React.createElement(\"text\", _extends({}, attrs, {\n    dominantBaseline: \"central\",\n    className: clsx('recharts-radial-bar-label', className)\n  }), /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"path\", {\n    id: id,\n    d: path\n  })), /*#__PURE__*/React.createElement(\"textPath\", {\n    xlinkHref: \"#\".concat(id)\n  }, label));\n};\nvar getAttrsOfPolarLabel = function getAttrsOfPolarLabel(props) {\n  var viewBox = props.viewBox,\n    offset = props.offset,\n    position = props.position;\n  var _ref2 = viewBox,\n    cx = _ref2.cx,\n    cy = _ref2.cy,\n    innerRadius = _ref2.innerRadius,\n    outerRadius = _ref2.outerRadius,\n    startAngle = _ref2.startAngle,\n    endAngle = _ref2.endAngle;\n  var midAngle = (startAngle + endAngle) / 2;\n  if (position === 'outside') {\n    var _polarToCartesian = polarToCartesian(cx, cy, outerRadius + offset, midAngle),\n      _x = _polarToCartesian.x,\n      _y = _polarToCartesian.y;\n    return {\n      x: _x,\n      y: _y,\n      textAnchor: _x >= cx ? 'start' : 'end',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'center') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'middle'\n    };\n  }\n  if (position === 'centerTop') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'start'\n    };\n  }\n  if (position === 'centerBottom') {\n    return {\n      x: cx,\n      y: cy,\n      textAnchor: 'middle',\n      verticalAnchor: 'end'\n    };\n  }\n  var r = (innerRadius + outerRadius) / 2;\n  var _polarToCartesian2 = polarToCartesian(cx, cy, r, midAngle),\n    x = _polarToCartesian2.x,\n    y = _polarToCartesian2.y;\n  return {\n    x: x,\n    y: y,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  };\n};\nvar getAttrsOfCartesianLabel = function getAttrsOfCartesianLabel(props) {\n  var viewBox = props.viewBox,\n    parentViewBox = props.parentViewBox,\n    offset = props.offset,\n    position = props.position;\n  var _ref3 = viewBox,\n    x = _ref3.x,\n    y = _ref3.y,\n    width = _ref3.width,\n    height = _ref3.height;\n\n  // Define vertical offsets and position inverts based on the value being positive or negative\n  var verticalSign = height >= 0 ? 1 : -1;\n  var verticalOffset = verticalSign * offset;\n  var verticalEnd = verticalSign > 0 ? 'end' : 'start';\n  var verticalStart = verticalSign > 0 ? 'start' : 'end';\n\n  // Define horizontal offsets and position inverts based on the value being positive or negative\n  var horizontalSign = width >= 0 ? 1 : -1;\n  var horizontalOffset = horizontalSign * offset;\n  var horizontalEnd = horizontalSign > 0 ? 'end' : 'start';\n  var horizontalStart = horizontalSign > 0 ? 'start' : 'end';\n  if (position === 'top') {\n    var attrs = {\n      x: x + width / 2,\n      y: y - verticalSign * offset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    };\n    return _objectSpread(_objectSpread({}, attrs), parentViewBox ? {\n      height: Math.max(y - parentViewBox.y, 0),\n      width: width\n    } : {});\n  }\n  if (position === 'bottom') {\n    var _attrs = {\n      x: x + width / 2,\n      y: y + height + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    };\n    return _objectSpread(_objectSpread({}, _attrs), parentViewBox ? {\n      height: Math.max(parentViewBox.y + parentViewBox.height - (y + height), 0),\n      width: width\n    } : {});\n  }\n  if (position === 'left') {\n    var _attrs2 = {\n      x: x - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs2), parentViewBox ? {\n      width: Math.max(_attrs2.x - parentViewBox.x, 0),\n      height: height\n    } : {});\n  }\n  if (position === 'right') {\n    var _attrs3 = {\n      x: x + width + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    };\n    return _objectSpread(_objectSpread({}, _attrs3), parentViewBox ? {\n      width: Math.max(parentViewBox.x + parentViewBox.width - _attrs3.x, 0),\n      height: height\n    } : {});\n  }\n  var sizeAttrs = parentViewBox ? {\n    width: width,\n    height: height\n  } : {};\n  if (position === 'insideLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalStart,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height / 2,\n      textAnchor: horizontalEnd,\n      verticalAnchor: 'middle'\n    }, sizeAttrs);\n  }\n  if (position === 'insideTop') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottom') {\n    return _objectSpread({\n      x: x + width / 2,\n      y: y + height - verticalOffset,\n      textAnchor: 'middle',\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideTopRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalStart\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomLeft') {\n    return _objectSpread({\n      x: x + horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalStart,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (position === 'insideBottomRight') {\n    return _objectSpread({\n      x: x + width - horizontalOffset,\n      y: y + height - verticalOffset,\n      textAnchor: horizontalEnd,\n      verticalAnchor: verticalEnd\n    }, sizeAttrs);\n  }\n  if (isObject(position) && (isNumber(position.x) || isPercent(position.x)) && (isNumber(position.y) || isPercent(position.y))) {\n    return _objectSpread({\n      x: x + getPercentValue(position.x, width),\n      y: y + getPercentValue(position.y, height),\n      textAnchor: 'end',\n      verticalAnchor: 'end'\n    }, sizeAttrs);\n  }\n  return _objectSpread({\n    x: x + width / 2,\n    y: y + height / 2,\n    textAnchor: 'middle',\n    verticalAnchor: 'middle'\n  }, sizeAttrs);\n};\nvar isPolar = function isPolar(viewBox) {\n  return 'cx' in viewBox && isNumber(viewBox.cx);\n};\nexport function Label(_ref4) {\n  var _ref4$offset = _ref4.offset,\n    offset = _ref4$offset === void 0 ? 5 : _ref4$offset,\n    restProps = _objectWithoutProperties(_ref4, _excluded);\n  var props = _objectSpread({\n    offset: offset\n  }, restProps);\n  var viewBox = props.viewBox,\n    position = props.position,\n    value = props.value,\n    children = props.children,\n    content = props.content,\n    _props$className = props.className,\n    className = _props$className === void 0 ? '' : _props$className,\n    textBreakAll = props.textBreakAll;\n  if (!viewBox || isNil(value) && isNil(children) && ! /*#__PURE__*/isValidElement(content) && !isFunction(content)) {\n    return null;\n  }\n  if ( /*#__PURE__*/isValidElement(content)) {\n    return /*#__PURE__*/cloneElement(content, props);\n  }\n  var label;\n  if (isFunction(content)) {\n    label = /*#__PURE__*/createElement(content, props);\n    if ( /*#__PURE__*/isValidElement(label)) {\n      return label;\n    }\n  } else {\n    label = getLabel(props);\n  }\n  var isPolarLabel = isPolar(viewBox);\n  var attrs = filterProps(props, true);\n  if (isPolarLabel && (position === 'insideStart' || position === 'insideEnd' || position === 'end')) {\n    return renderRadialLabel(props, label, attrs);\n  }\n  var positionAttrs = isPolarLabel ? getAttrsOfPolarLabel(props) : getAttrsOfCartesianLabel(props);\n  return /*#__PURE__*/React.createElement(Text, _extends({\n    className: clsx('recharts-label', className)\n  }, attrs, positionAttrs, {\n    breakAll: textBreakAll\n  }), label);\n}\nLabel.displayName = 'Label';\nvar parseViewBox = function parseViewBox(props) {\n  var cx = props.cx,\n    cy = props.cy,\n    angle = props.angle,\n    startAngle = props.startAngle,\n    endAngle = props.endAngle,\n    r = props.r,\n    radius = props.radius,\n    innerRadius = props.innerRadius,\n    outerRadius = props.outerRadius,\n    x = props.x,\n    y = props.y,\n    top = props.top,\n    left = props.left,\n    width = props.width,\n    height = props.height,\n    clockWise = props.clockWise,\n    labelViewBox = props.labelViewBox;\n  if (labelViewBox) {\n    return labelViewBox;\n  }\n  if (isNumber(width) && isNumber(height)) {\n    if (isNumber(x) && isNumber(y)) {\n      return {\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      };\n    }\n    if (isNumber(top) && isNumber(left)) {\n      return {\n        x: top,\n        y: left,\n        width: width,\n        height: height\n      };\n    }\n  }\n  if (isNumber(x) && isNumber(y)) {\n    return {\n      x: x,\n      y: y,\n      width: 0,\n      height: 0\n    };\n  }\n  if (isNumber(cx) && isNumber(cy)) {\n    return {\n      cx: cx,\n      cy: cy,\n      startAngle: startAngle || angle || 0,\n      endAngle: endAngle || angle || 0,\n      innerRadius: innerRadius || 0,\n      outerRadius: outerRadius || radius || r || 0,\n      clockWise: clockWise\n    };\n  }\n  if (props.viewBox) {\n    return props.viewBox;\n  }\n  return {};\n};\nvar parseLabel = function parseLabel(label, viewBox) {\n  if (!label) {\n    return null;\n  }\n  if (label === true) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      viewBox: viewBox\n    });\n  }\n  if (isNumOrStr(label)) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      viewBox: viewBox,\n      value: label\n    });\n  }\n  if ( /*#__PURE__*/isValidElement(label)) {\n    if (label.type === Label) {\n      return /*#__PURE__*/cloneElement(label, {\n        key: 'label-implicit',\n        viewBox: viewBox\n      });\n    }\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      content: label,\n      viewBox: viewBox\n    });\n  }\n  if (isFunction(label)) {\n    return /*#__PURE__*/React.createElement(Label, {\n      key: \"label-implicit\",\n      content: label,\n      viewBox: viewBox\n    });\n  }\n  if (isObject(label)) {\n    return /*#__PURE__*/React.createElement(Label, _extends({\n      viewBox: viewBox\n    }, label, {\n      key: \"label-implicit\"\n    }));\n  }\n  return null;\n};\nvar renderCallByParent = function renderCallByParent(parentProps, viewBox) {\n  var checkPropsLabel = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (!parentProps || !parentProps.children && checkPropsLabel && !parentProps.label) {\n    return null;\n  }\n  var children = parentProps.children;\n  var parentViewBox = parseViewBox(parentProps);\n  var explicitChildren = findAllByType(children, Label).map(function (child, index) {\n    return /*#__PURE__*/cloneElement(child, {\n      viewBox: viewBox || parentViewBox,\n      // eslint-disable-next-line react/no-array-index-key\n      key: \"label-\".concat(index)\n    });\n  });\n  if (!checkPropsLabel) {\n    return explicitChildren;\n  }\n  var implicitLabel = parseLabel(parentProps.label, viewBox || parentViewBox);\n  return [implicitLabel].concat(_toConsumableArray(explicitChildren));\n};\nLabel.parseViewBox = parseViewBox;\nLabel.renderCallByParent = renderCallByParent;"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,IAAIK,SAAS,GAAG,CAAC,QAAQ,CAAC;AAC1B,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACV,CAAC,EAAEa,MAAM,EAAE;EAAE,IAAI,CAACb,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOc,iBAAiB,CAACd,CAAC,EAAEa,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACZ,SAAS,CAACa,QAAQ,CAACC,IAAI,CAAClB,CAAC,CAAC,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAIf,CAAC,CAACG,WAAW,EAAEY,CAAC,GAAGf,CAAC,CAACG,WAAW,CAACiB,IAAI;EAAE,IAAIL,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOM,KAAK,CAACC,IAAI,CAACtB,CAAC,CAAC;EAAE,IAAIe,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACd,CAAC,EAAEa,MAAM,CAAC;AAAE;AAC/Z,SAASJ,gBAAgBA,CAACe,IAAI,EAAE;EAAE,IAAI,OAAOvB,MAAM,KAAK,WAAW,IAAIuB,IAAI,CAACvB,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIsB,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAAShB,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIc,KAAK,CAACI,OAAO,CAAClB,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AAAE;AAC1F,SAASO,iBAAiBA,CAACP,GAAG,EAAEmB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGnB,GAAG,CAACoB,MAAM,EAAED,GAAG,GAAGnB,GAAG,CAACoB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIR,KAAK,CAACK,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAEC,IAAI,CAACD,CAAC,CAAC,GAAGrB,GAAG,CAACqB,CAAC,CAAC;EAAE,OAAOC,IAAI;AAAE;AAClL,SAASC,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEP,CAAC;EAAE,IAAIZ,MAAM,CAACoB,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGrB,MAAM,CAACoB,qBAAqB,CAACL,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,gBAAgB,CAACV,MAAM,EAAEC,CAAC,EAAE,EAAE;MAAEO,GAAG,GAAGE,gBAAgB,CAACT,CAAC,CAAC;MAAE,IAAII,QAAQ,CAACM,OAAO,CAACH,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACnB,MAAM,CAACZ,SAAS,CAACmC,oBAAoB,CAACrB,IAAI,CAACa,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIE,GAAG,IAAIJ,MAAM,EAAE;IAAE,IAAIf,MAAM,CAACZ,SAAS,CAACoC,cAAc,CAACtB,IAAI,CAACa,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAE,IAAIH,QAAQ,CAACM,OAAO,CAACH,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AACtR,SAASQ,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAG5B,MAAM,CAAC6B,IAAI,CAACH,CAAC,CAAC;EAAE,IAAI1B,MAAM,CAACoB,qBAAqB,EAAE;IAAE,IAAIpC,CAAC,GAAGgB,MAAM,CAACoB,qBAAqB,CAACM,CAAC,CAAC;IAAEC,CAAC,KAAK3C,CAAC,GAAGA,CAAC,CAAC8C,MAAM,CAAC,UAAUH,CAAC,EAAE;MAAE,OAAO3B,MAAM,CAAC+B,wBAAwB,CAACL,CAAC,EAAEC,CAAC,CAAC,CAACK,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEJ,CAAC,CAACK,IAAI,CAACC,KAAK,CAACN,CAAC,EAAE5C,CAAC,CAAC;EAAE;EAAE,OAAO4C,CAAC;AAAE;AAC9P,SAASO,aAAaA,CAACT,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,SAAS,CAACzB,MAAM,EAAEgB,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIQ,SAAS,CAACT,CAAC,CAAC,GAAGS,SAAS,CAACT,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACzB,MAAM,CAAC4B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACS,OAAO,CAAC,UAAUV,CAAC,EAAE;MAAEW,eAAe,CAACZ,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAG3B,MAAM,CAACuC,yBAAyB,GAAGvC,MAAM,CAACwC,gBAAgB,CAACd,CAAC,EAAE1B,MAAM,CAACuC,yBAAyB,CAACX,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACzB,MAAM,CAAC4B,CAAC,CAAC,CAAC,CAACS,OAAO,CAAC,UAAUV,CAAC,EAAE;MAAE3B,MAAM,CAACyC,cAAc,CAACf,CAAC,EAAEC,CAAC,EAAE3B,MAAM,CAAC+B,wBAAwB,CAACH,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASY,eAAeA,CAACI,GAAG,EAAEvB,GAAG,EAAEwB,KAAK,EAAE;EAAExB,GAAG,GAAGyB,cAAc,CAACzB,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIuB,GAAG,EAAE;IAAE1C,MAAM,CAACyC,cAAc,CAACC,GAAG,EAAEvB,GAAG,EAAE;MAAEwB,KAAK,EAAEA,KAAK;MAAEX,UAAU,EAAE,IAAI;MAAEa,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEJ,GAAG,CAACvB,GAAG,CAAC,GAAGwB,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAC3O,SAASE,cAAcA,CAAChB,CAAC,EAAE;EAAE,IAAIhB,CAAC,GAAGmC,YAAY,CAACnB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI7C,OAAO,CAAC6B,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASmC,YAAYA,CAACnB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI5C,OAAO,CAAC6C,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAAC3C,MAAM,CAAC+D,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKtB,CAAC,EAAE;IAAE,IAAId,CAAC,GAAGc,CAAC,CAACxB,IAAI,CAAC0B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI5C,OAAO,CAAC6B,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIhB,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK+B,CAAC,GAAGsB,MAAM,GAAGC,MAAM,EAAEtB,CAAC,CAAC;AAAE;AAC3T,SAASuB,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGnD,MAAM,CAACoD,MAAM,GAAGpD,MAAM,CAACoD,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUpC,MAAM,EAAE;IAAE,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,SAAS,CAACzB,MAAM,EAAEC,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGqB,SAAS,CAACxB,CAAC,CAAC;MAAE,KAAK,IAAIO,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIf,MAAM,CAACZ,SAAS,CAACoC,cAAc,CAACtB,IAAI,CAACa,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOkC,QAAQ,CAACjB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AAClV,OAAOkB,KAAK,IAAIC,YAAY,EAAEC,cAAc,EAAEC,aAAa,QAAQ,OAAO;AAC1E,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,mBAAmB;AACxG,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAI9B,KAAK,GAAG8B,KAAK,CAAC9B,KAAK;IACrB+B,SAAS,GAAGD,KAAK,CAACC,SAAS;EAC7B,IAAIC,KAAK,GAAGjB,KAAK,CAACe,KAAK,CAACG,QAAQ,CAAC,GAAGjC,KAAK,GAAG8B,KAAK,CAACG,QAAQ;EAC1D,IAAIjB,UAAU,CAACe,SAAS,CAAC,EAAE;IACzB,OAAOA,SAAS,CAACC,KAAK,CAAC;EACzB;EACA,OAAOA,KAAK;AACd,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EAC/D,IAAIC,IAAI,GAAGV,QAAQ,CAACS,QAAQ,GAAGD,UAAU,CAAC;EAC1C,IAAIG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,QAAQ,GAAGD,UAAU,CAAC,EAAE,GAAG,CAAC;EAC/D,OAAOE,IAAI,GAAGC,UAAU;AAC1B,CAAC;AACD,IAAII,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,UAAU,EAAEX,KAAK,EAAEY,KAAK,EAAE;EAC3E,IAAIC,QAAQ,GAAGF,UAAU,CAACE,QAAQ;IAChCC,OAAO,GAAGH,UAAU,CAACG,OAAO;IAC5BC,MAAM,GAAGJ,UAAU,CAACI,MAAM;IAC1BC,SAAS,GAAGL,UAAU,CAACK,SAAS;EAClC,IAAIC,IAAI,GAAGH,OAAO;IAChBI,EAAE,GAAGD,IAAI,CAACC,EAAE;IACZC,EAAE,GAAGF,IAAI,CAACE,EAAE;IACZC,WAAW,GAAGH,IAAI,CAACG,WAAW;IAC9BC,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAC9BlB,UAAU,GAAGc,IAAI,CAACd,UAAU;IAC5BC,QAAQ,GAAGa,IAAI,CAACb,QAAQ;IACxBkB,SAAS,GAAGL,IAAI,CAACK,SAAS;EAC5B,IAAIC,MAAM,GAAG,CAACH,WAAW,GAAGC,WAAW,IAAI,CAAC;EAC5C,IAAIf,UAAU,GAAGJ,aAAa,CAACC,UAAU,EAAEC,QAAQ,CAAC;EACpD,IAAIC,IAAI,GAAGC,UAAU,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACnC,IAAIkB,UAAU,EAAEC,SAAS;EACzB,IAAIZ,QAAQ,KAAK,aAAa,EAAE;IAC9BW,UAAU,GAAGrB,UAAU,GAAGE,IAAI,GAAGU,MAAM;IACvCU,SAAS,GAAGH,SAAS;EACvB,CAAC,MAAM,IAAIT,QAAQ,KAAK,WAAW,EAAE;IACnCW,UAAU,GAAGpB,QAAQ,GAAGC,IAAI,GAAGU,MAAM;IACrCU,SAAS,GAAG,CAACH,SAAS;EACxB,CAAC,MAAM,IAAIT,QAAQ,KAAK,KAAK,EAAE;IAC7BW,UAAU,GAAGpB,QAAQ,GAAGC,IAAI,GAAGU,MAAM;IACrCU,SAAS,GAAGH,SAAS;EACvB;EACAG,SAAS,GAAGnB,UAAU,IAAI,CAAC,GAAGmB,SAAS,GAAG,CAACA,SAAS;EACpD,IAAIC,UAAU,GAAG9B,gBAAgB,CAACsB,EAAE,EAAEC,EAAE,EAAEI,MAAM,EAAEC,UAAU,CAAC;EAC7D,IAAIG,QAAQ,GAAG/B,gBAAgB,CAACsB,EAAE,EAAEC,EAAE,EAAEI,MAAM,EAAEC,UAAU,GAAG,CAACC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;EACxF,IAAIG,IAAI,GAAG,GAAG,CAACC,MAAM,CAACH,UAAU,CAACI,CAAC,EAAE,GAAG,CAAC,CAACD,MAAM,CAACH,UAAU,CAACK,CAAC,EAAE,SAAS,CAAC,CAACF,MAAM,CAACN,MAAM,EAAE,GAAG,CAAC,CAACM,MAAM,CAACN,MAAM,EAAE,OAAO,CAAC,CAACM,MAAM,CAACJ,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAACI,MAAM,CAACF,QAAQ,CAACG,CAAC,EAAE,GAAG,CAAC,CAACD,MAAM,CAACF,QAAQ,CAACI,CAAC,CAAC;EACpM,IAAIC,EAAE,GAAGjD,KAAK,CAAC4B,UAAU,CAACqB,EAAE,CAAC,GAAGtC,QAAQ,CAAC,uBAAuB,CAAC,GAAGiB,UAAU,CAACqB,EAAE;EACjF,OAAO,aAAarD,KAAK,CAACG,aAAa,CAAC,MAAM,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;IAClEqB,gBAAgB,EAAE,SAAS;IAC3BjB,SAAS,EAAE9B,IAAI,CAAC,2BAA2B,EAAE8B,SAAS;EACxD,CAAC,CAAC,EAAE,aAAarC,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAaH,KAAK,CAACG,aAAa,CAAC,MAAM,EAAE;IAC1FkD,EAAE,EAAEA,EAAE;IACNE,CAAC,EAAEN;EACL,CAAC,CAAC,CAAC,EAAE,aAAajD,KAAK,CAACG,aAAa,CAAC,UAAU,EAAE;IAChDqD,SAAS,EAAE,GAAG,CAACN,MAAM,CAACG,EAAE;EAC1B,CAAC,EAAEhC,KAAK,CAAC,CAAC;AACZ,CAAC;AACD,IAAIoC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACtC,KAAK,EAAE;EAC9D,IAAIgB,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACzBC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBF,QAAQ,GAAGf,KAAK,CAACe,QAAQ;EAC3B,IAAIwB,KAAK,GAAGvB,OAAO;IACjBI,EAAE,GAAGmB,KAAK,CAACnB,EAAE;IACbC,EAAE,GAAGkB,KAAK,CAAClB,EAAE;IACbC,WAAW,GAAGiB,KAAK,CAACjB,WAAW;IAC/BC,WAAW,GAAGgB,KAAK,CAAChB,WAAW;IAC/BlB,UAAU,GAAGkC,KAAK,CAAClC,UAAU;IAC7BC,QAAQ,GAAGiC,KAAK,CAACjC,QAAQ;EAC3B,IAAIkC,QAAQ,GAAG,CAACnC,UAAU,GAAGC,QAAQ,IAAI,CAAC;EAC1C,IAAIS,QAAQ,KAAK,SAAS,EAAE;IAC1B,IAAI0B,iBAAiB,GAAG3C,gBAAgB,CAACsB,EAAE,EAAEC,EAAE,EAAEE,WAAW,GAAGN,MAAM,EAAEuB,QAAQ,CAAC;MAC9EE,EAAE,GAAGD,iBAAiB,CAACT,CAAC;MACxBW,EAAE,GAAGF,iBAAiB,CAACR,CAAC;IAC1B,OAAO;MACLD,CAAC,EAAEU,EAAE;MACLT,CAAC,EAAEU,EAAE;MACLC,UAAU,EAAEF,EAAE,IAAItB,EAAE,GAAG,OAAO,GAAG,KAAK;MACtCyB,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI9B,QAAQ,KAAK,QAAQ,EAAE;IACzB,OAAO;MACLiB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLuB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI9B,QAAQ,KAAK,WAAW,EAAE;IAC5B,OAAO;MACLiB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLuB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI9B,QAAQ,KAAK,cAAc,EAAE;IAC/B,OAAO;MACLiB,CAAC,EAAEZ,EAAE;MACLa,CAAC,EAAEZ,EAAE;MACLuB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAC;EACH;EACA,IAAI3F,CAAC,GAAG,CAACoE,WAAW,GAAGC,WAAW,IAAI,CAAC;EACvC,IAAIuB,kBAAkB,GAAGhD,gBAAgB,CAACsB,EAAE,EAAEC,EAAE,EAAEnE,CAAC,EAAEsF,QAAQ,CAAC;IAC5DR,CAAC,GAAGc,kBAAkB,CAACd,CAAC;IACxBC,CAAC,GAAGa,kBAAkB,CAACb,CAAC;EAC1B,OAAO;IACLD,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJW,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;AACH,CAAC;AACD,IAAIE,wBAAwB,GAAG,SAASA,wBAAwBA,CAAC/C,KAAK,EAAE;EACtE,IAAIgB,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACzBgC,aAAa,GAAGhD,KAAK,CAACgD,aAAa;IACnC/B,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBF,QAAQ,GAAGf,KAAK,CAACe,QAAQ;EAC3B,IAAIkC,KAAK,GAAGjC,OAAO;IACjBgB,CAAC,GAAGiB,KAAK,CAACjB,CAAC;IACXC,CAAC,GAAGgB,KAAK,CAAChB,CAAC;IACXiB,KAAK,GAAGD,KAAK,CAACC,KAAK;IACnBC,MAAM,GAAGF,KAAK,CAACE,MAAM;;EAEvB;EACA,IAAIC,YAAY,GAAGD,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvC,IAAIE,cAAc,GAAGD,YAAY,GAAGnC,MAAM;EAC1C,IAAIqC,WAAW,GAAGF,YAAY,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO;EACpD,IAAIG,aAAa,GAAGH,YAAY,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK;;EAEtD;EACA,IAAII,cAAc,GAAGN,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACxC,IAAIO,gBAAgB,GAAGD,cAAc,GAAGvC,MAAM;EAC9C,IAAIyC,aAAa,GAAGF,cAAc,GAAG,CAAC,GAAG,KAAK,GAAG,OAAO;EACxD,IAAIG,eAAe,GAAGH,cAAc,GAAG,CAAC,GAAG,OAAO,GAAG,KAAK;EAC1D,IAAIzC,QAAQ,KAAK,KAAK,EAAE;IACtB,IAAID,KAAK,GAAG;MACVkB,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAG,CAAC;MAChBjB,CAAC,EAAEA,CAAC,GAAGmB,YAAY,GAAGnC,MAAM;MAC5B2B,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAES;IAClB,CAAC;IACD,OAAO5F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,KAAK,CAAC,EAAEkC,aAAa,GAAG;MAC7DG,MAAM,EAAE1C,IAAI,CAACmD,GAAG,CAAC3B,CAAC,GAAGe,aAAa,CAACf,CAAC,EAAE,CAAC,CAAC;MACxCiB,KAAK,EAAEA;IACT,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAInC,QAAQ,KAAK,QAAQ,EAAE;IACzB,IAAI8C,MAAM,GAAG;MACX7B,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAG,CAAC;MAChBjB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAGE,cAAc;MAC9BT,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEU;IAClB,CAAC;IACD,OAAO7F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmG,MAAM,CAAC,EAAEb,aAAa,GAAG;MAC9DG,MAAM,EAAE1C,IAAI,CAACmD,GAAG,CAACZ,aAAa,CAACf,CAAC,GAAGe,aAAa,CAACG,MAAM,IAAIlB,CAAC,GAAGkB,MAAM,CAAC,EAAE,CAAC,CAAC;MAC1ED,KAAK,EAAEA;IACT,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAInC,QAAQ,KAAK,MAAM,EAAE;IACvB,IAAI+C,OAAO,GAAG;MACZ9B,CAAC,EAAEA,CAAC,GAAGyB,gBAAgB;MACvBxB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAG,CAAC;MACjBP,UAAU,EAAEc,aAAa;MACzBb,cAAc,EAAE;IAClB,CAAC;IACD,OAAOnF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoG,OAAO,CAAC,EAAEd,aAAa,GAAG;MAC/DE,KAAK,EAAEzC,IAAI,CAACmD,GAAG,CAACE,OAAO,CAAC9B,CAAC,GAAGgB,aAAa,CAAChB,CAAC,EAAE,CAAC,CAAC;MAC/CmB,MAAM,EAAEA;IACV,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAIpC,QAAQ,KAAK,OAAO,EAAE;IACxB,IAAIgD,OAAO,GAAG;MACZ/B,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAGO,gBAAgB;MAC/BxB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAG,CAAC;MACjBP,UAAU,EAAEe,eAAe;MAC3Bd,cAAc,EAAE;IAClB,CAAC;IACD,OAAOnF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqG,OAAO,CAAC,EAAEf,aAAa,GAAG;MAC/DE,KAAK,EAAEzC,IAAI,CAACmD,GAAG,CAACZ,aAAa,CAAChB,CAAC,GAAGgB,aAAa,CAACE,KAAK,GAAGa,OAAO,CAAC/B,CAAC,EAAE,CAAC,CAAC;MACrEmB,MAAM,EAAEA;IACV,CAAC,GAAG,CAAC,CAAC,CAAC;EACT;EACA,IAAIa,SAAS,GAAGhB,aAAa,GAAG;IAC9BE,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA;EACV,CAAC,GAAG,CAAC,CAAC;EACN,IAAIpC,QAAQ,KAAK,YAAY,EAAE;IAC7B,OAAOrD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGyB,gBAAgB;MACvBxB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAG,CAAC;MACjBP,UAAU,EAAEe,eAAe;MAC3Bd,cAAc,EAAE;IAClB,CAAC,EAAEmB,SAAS,CAAC;EACf;EACA,IAAIjD,QAAQ,KAAK,aAAa,EAAE;IAC9B,OAAOrD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAGO,gBAAgB;MAC/BxB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAG,CAAC;MACjBP,UAAU,EAAEc,aAAa;MACzBb,cAAc,EAAE;IAClB,CAAC,EAAEmB,SAAS,CAAC;EACf;EACA,IAAIjD,QAAQ,KAAK,WAAW,EAAE;IAC5B,OAAOrD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAG,CAAC;MAChBjB,CAAC,EAAEA,CAAC,GAAGoB,cAAc;MACrBT,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAEU;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAIjD,QAAQ,KAAK,cAAc,EAAE;IAC/B,OAAOrD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAG,CAAC;MAChBjB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAGE,cAAc;MAC9BT,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAES;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAIjD,QAAQ,KAAK,eAAe,EAAE;IAChC,OAAOrD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGyB,gBAAgB;MACvBxB,CAAC,EAAEA,CAAC,GAAGoB,cAAc;MACrBT,UAAU,EAAEe,eAAe;MAC3Bd,cAAc,EAAEU;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAIjD,QAAQ,KAAK,gBAAgB,EAAE;IACjC,OAAOrD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAGO,gBAAgB;MAC/BxB,CAAC,EAAEA,CAAC,GAAGoB,cAAc;MACrBT,UAAU,EAAEc,aAAa;MACzBb,cAAc,EAAEU;IAClB,CAAC,EAAES,SAAS,CAAC;EACf;EACA,IAAIjD,QAAQ,KAAK,kBAAkB,EAAE;IACnC,OAAOrD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGyB,gBAAgB;MACvBxB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAGE,cAAc;MAC9BT,UAAU,EAAEe,eAAe;MAC3Bd,cAAc,EAAES;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAIjD,QAAQ,KAAK,mBAAmB,EAAE;IACpC,OAAOrD,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAGO,gBAAgB;MAC/BxB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAGE,cAAc;MAC9BT,UAAU,EAAEc,aAAa;MACzBb,cAAc,EAAES;IAClB,CAAC,EAAEU,SAAS,CAAC;EACf;EACA,IAAI7E,QAAQ,CAAC4B,QAAQ,CAAC,KAAKtB,QAAQ,CAACsB,QAAQ,CAACiB,CAAC,CAAC,IAAItC,SAAS,CAACqB,QAAQ,CAACiB,CAAC,CAAC,CAAC,KAAKvC,QAAQ,CAACsB,QAAQ,CAACkB,CAAC,CAAC,IAAIvC,SAAS,CAACqB,QAAQ,CAACkB,CAAC,CAAC,CAAC,EAAE;IAC5H,OAAOvE,aAAa,CAAC;MACnBsE,CAAC,EAAEA,CAAC,GAAGrC,eAAe,CAACoB,QAAQ,CAACiB,CAAC,EAAEkB,KAAK,CAAC;MACzCjB,CAAC,EAAEA,CAAC,GAAGtC,eAAe,CAACoB,QAAQ,CAACkB,CAAC,EAAEkB,MAAM,CAAC;MAC1CP,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE;IAClB,CAAC,EAAEmB,SAAS,CAAC;EACf;EACA,OAAOtG,aAAa,CAAC;IACnBsE,CAAC,EAAEA,CAAC,GAAGkB,KAAK,GAAG,CAAC;IAChBjB,CAAC,EAAEA,CAAC,GAAGkB,MAAM,GAAG,CAAC;IACjBP,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC,EAAEmB,SAAS,CAAC;AACf,CAAC;AACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACjD,OAAO,EAAE;EACtC,OAAO,IAAI,IAAIA,OAAO,IAAIvB,QAAQ,CAACuB,OAAO,CAACI,EAAE,CAAC;AAChD,CAAC;AACD,OAAO,SAAS8C,KAAKA,CAACC,KAAK,EAAE;EAC3B,IAAIC,YAAY,GAAGD,KAAK,CAAClD,MAAM;IAC7BA,MAAM,GAAGmD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IACnDC,SAAS,GAAGhI,wBAAwB,CAAC8H,KAAK,EAAEvJ,SAAS,CAAC;EACxD,IAAIoF,KAAK,GAAGtC,aAAa,CAAC;IACxBuD,MAAM,EAAEA;EACV,CAAC,EAAEoD,SAAS,CAAC;EACb,IAAIrD,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACzBD,QAAQ,GAAGf,KAAK,CAACe,QAAQ;IACzB7C,KAAK,GAAG8B,KAAK,CAAC9B,KAAK;IACnBiC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBmE,OAAO,GAAGtE,KAAK,CAACsE,OAAO;IACvBC,gBAAgB,GAAGvE,KAAK,CAACkB,SAAS;IAClCA,SAAS,GAAGqD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DC,YAAY,GAAGxE,KAAK,CAACwE,YAAY;EACnC,IAAI,CAACxD,OAAO,IAAI/B,KAAK,CAACf,KAAK,CAAC,IAAIe,KAAK,CAACkB,QAAQ,CAAC,IAAI,EAAE,aAAapB,cAAc,CAACuF,OAAO,CAAC,IAAI,CAACpF,UAAU,CAACoF,OAAO,CAAC,EAAE;IACjH,OAAO,IAAI;EACb;EACA,IAAK,aAAavF,cAAc,CAACuF,OAAO,CAAC,EAAE;IACzC,OAAO,aAAaxF,YAAY,CAACwF,OAAO,EAAEtE,KAAK,CAAC;EAClD;EACA,IAAIE,KAAK;EACT,IAAIhB,UAAU,CAACoF,OAAO,CAAC,EAAE;IACvBpE,KAAK,GAAG,aAAalB,aAAa,CAACsF,OAAO,EAAEtE,KAAK,CAAC;IAClD,IAAK,aAAajB,cAAc,CAACmB,KAAK,CAAC,EAAE;MACvC,OAAOA,KAAK;IACd;EACF,CAAC,MAAM;IACLA,KAAK,GAAGH,QAAQ,CAACC,KAAK,CAAC;EACzB;EACA,IAAIyE,YAAY,GAAGR,OAAO,CAACjD,OAAO,CAAC;EACnC,IAAIF,KAAK,GAAGvB,WAAW,CAACS,KAAK,EAAE,IAAI,CAAC;EACpC,IAAIyE,YAAY,KAAK1D,QAAQ,KAAK,aAAa,IAAIA,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IAClG,OAAOH,iBAAiB,CAACZ,KAAK,EAAEE,KAAK,EAAEY,KAAK,CAAC;EAC/C;EACA,IAAI4D,aAAa,GAAGD,YAAY,GAAGnC,oBAAoB,CAACtC,KAAK,CAAC,GAAG+C,wBAAwB,CAAC/C,KAAK,CAAC;EAChG,OAAO,aAAanB,KAAK,CAACG,aAAa,CAACK,IAAI,EAAEX,QAAQ,CAAC;IACrDwC,SAAS,EAAE9B,IAAI,CAAC,gBAAgB,EAAE8B,SAAS;EAC7C,CAAC,EAAEJ,KAAK,EAAE4D,aAAa,EAAE;IACvBC,QAAQ,EAAEH;EACZ,CAAC,CAAC,EAAEtE,KAAK,CAAC;AACZ;AACAgE,KAAK,CAACU,WAAW,GAAG,OAAO;AAC3B,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAC7E,KAAK,EAAE;EAC9C,IAAIoB,EAAE,GAAGpB,KAAK,CAACoB,EAAE;IACfC,EAAE,GAAGrB,KAAK,CAACqB,EAAE;IACbyD,KAAK,GAAG9E,KAAK,CAAC8E,KAAK;IACnBzE,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBpD,CAAC,GAAG8C,KAAK,CAAC9C,CAAC;IACXuE,MAAM,GAAGzB,KAAK,CAACyB,MAAM;IACrBH,WAAW,GAAGtB,KAAK,CAACsB,WAAW;IAC/BC,WAAW,GAAGvB,KAAK,CAACuB,WAAW;IAC/BS,CAAC,GAAGhC,KAAK,CAACgC,CAAC;IACXC,CAAC,GAAGjC,KAAK,CAACiC,CAAC;IACX8C,GAAG,GAAG/E,KAAK,CAAC+E,GAAG;IACfC,IAAI,GAAGhF,KAAK,CAACgF,IAAI;IACjB9B,KAAK,GAAGlD,KAAK,CAACkD,KAAK;IACnBC,MAAM,GAAGnD,KAAK,CAACmD,MAAM;IACrB3B,SAAS,GAAGxB,KAAK,CAACwB,SAAS;IAC3ByD,YAAY,GAAGjF,KAAK,CAACiF,YAAY;EACnC,IAAIA,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;EACA,IAAIxF,QAAQ,CAACyD,KAAK,CAAC,IAAIzD,QAAQ,CAAC0D,MAAM,CAAC,EAAE;IACvC,IAAI1D,QAAQ,CAACuC,CAAC,CAAC,IAAIvC,QAAQ,CAACwC,CAAC,CAAC,EAAE;MAC9B,OAAO;QACLD,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA,CAAC;QACJiB,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;IACH;IACA,IAAI1D,QAAQ,CAACsF,GAAG,CAAC,IAAItF,QAAQ,CAACuF,IAAI,CAAC,EAAE;MACnC,OAAO;QACLhD,CAAC,EAAE+C,GAAG;QACN9C,CAAC,EAAE+C,IAAI;QACP9B,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA;MACV,CAAC;IACH;EACF;EACA,IAAI1D,QAAQ,CAACuC,CAAC,CAAC,IAAIvC,QAAQ,CAACwC,CAAC,CAAC,EAAE;IAC9B,OAAO;MACLD,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJiB,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;EACH;EACA,IAAI1D,QAAQ,CAAC2B,EAAE,CAAC,IAAI3B,QAAQ,CAAC4B,EAAE,CAAC,EAAE;IAChC,OAAO;MACLD,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNhB,UAAU,EAAEA,UAAU,IAAIyE,KAAK,IAAI,CAAC;MACpCxE,QAAQ,EAAEA,QAAQ,IAAIwE,KAAK,IAAI,CAAC;MAChCxD,WAAW,EAAEA,WAAW,IAAI,CAAC;MAC7BC,WAAW,EAAEA,WAAW,IAAIE,MAAM,IAAIvE,CAAC,IAAI,CAAC;MAC5CsE,SAAS,EAAEA;IACb,CAAC;EACH;EACA,IAAIxB,KAAK,CAACgB,OAAO,EAAE;IACjB,OAAOhB,KAAK,CAACgB,OAAO;EACtB;EACA,OAAO,CAAC,CAAC;AACX,CAAC;AACD,IAAIkE,UAAU,GAAG,SAASA,UAAUA,CAAChF,KAAK,EAAEc,OAAO,EAAE;EACnD,IAAI,CAACd,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,IAAIA,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,aAAarB,KAAK,CAACG,aAAa,CAACkF,KAAK,EAAE;MAC7CxH,GAAG,EAAE,gBAAgB;MACrBsE,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ;EACA,IAAIxB,UAAU,CAACU,KAAK,CAAC,EAAE;IACrB,OAAO,aAAarB,KAAK,CAACG,aAAa,CAACkF,KAAK,EAAE;MAC7CxH,GAAG,EAAE,gBAAgB;MACrBsE,OAAO,EAAEA,OAAO;MAChB9C,KAAK,EAAEgC;IACT,CAAC,CAAC;EACJ;EACA,IAAK,aAAanB,cAAc,CAACmB,KAAK,CAAC,EAAE;IACvC,IAAIA,KAAK,CAACiF,IAAI,KAAKjB,KAAK,EAAE;MACxB,OAAO,aAAapF,YAAY,CAACoB,KAAK,EAAE;QACtCxD,GAAG,EAAE,gBAAgB;QACrBsE,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ;IACA,OAAO,aAAanC,KAAK,CAACG,aAAa,CAACkF,KAAK,EAAE;MAC7CxH,GAAG,EAAE,gBAAgB;MACrB4H,OAAO,EAAEpE,KAAK;MACdc,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ;EACA,IAAI9B,UAAU,CAACgB,KAAK,CAAC,EAAE;IACrB,OAAO,aAAarB,KAAK,CAACG,aAAa,CAACkF,KAAK,EAAE;MAC7CxH,GAAG,EAAE,gBAAgB;MACrB4H,OAAO,EAAEpE,KAAK;MACdc,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ;EACA,IAAI7B,QAAQ,CAACe,KAAK,CAAC,EAAE;IACnB,OAAO,aAAarB,KAAK,CAACG,aAAa,CAACkF,KAAK,EAAExF,QAAQ,CAAC;MACtDsC,OAAO,EAAEA;IACX,CAAC,EAAEd,KAAK,EAAE;MACRxD,GAAG,EAAE;IACP,CAAC,CAAC,CAAC;EACL;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAI0I,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,WAAW,EAAErE,OAAO,EAAE;EACzE,IAAIsE,eAAe,GAAG3H,SAAS,CAACzB,MAAM,GAAG,CAAC,IAAIyB,SAAS,CAAC,CAAC,CAAC,KAAK4H,SAAS,GAAG5H,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EAC9F,IAAI,CAAC0H,WAAW,IAAI,CAACA,WAAW,CAAClF,QAAQ,IAAImF,eAAe,IAAI,CAACD,WAAW,CAACnF,KAAK,EAAE;IAClF,OAAO,IAAI;EACb;EACA,IAAIC,QAAQ,GAAGkF,WAAW,CAAClF,QAAQ;EACnC,IAAI6C,aAAa,GAAG6B,YAAY,CAACQ,WAAW,CAAC;EAC7C,IAAIG,gBAAgB,GAAGlG,aAAa,CAACa,QAAQ,EAAE+D,KAAK,CAAC,CAACuB,GAAG,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAChF,OAAO,aAAa7G,YAAY,CAAC4G,KAAK,EAAE;MACtC1E,OAAO,EAAEA,OAAO,IAAIgC,aAAa;MACjC;MACAtG,GAAG,EAAE,QAAQ,CAACqF,MAAM,CAAC4D,KAAK;IAC5B,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI,CAACL,eAAe,EAAE;IACpB,OAAOE,gBAAgB;EACzB;EACA,IAAII,aAAa,GAAGV,UAAU,CAACG,WAAW,CAACnF,KAAK,EAAEc,OAAO,IAAIgC,aAAa,CAAC;EAC3E,OAAO,CAAC4C,aAAa,CAAC,CAAC7D,MAAM,CAAClH,kBAAkB,CAAC2K,gBAAgB,CAAC,CAAC;AACrE,CAAC;AACDtB,KAAK,CAACW,YAAY,GAAGA,YAAY;AACjCX,KAAK,CAACkB,kBAAkB,GAAGA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}