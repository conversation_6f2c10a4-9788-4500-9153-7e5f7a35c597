{"ast": null, "code": "var root = require('./_root');\n\n/** Built-in value references. */\nvar Uint8Array = root.Uint8Array;\nmodule.exports = Uint8Array;", "map": {"version": 3, "names": ["root", "require", "Uint8Array", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_Uint8Array.js"], "sourcesContent": ["var root = require('./_root');\n\n/** Built-in value references. */\nvar Uint8Array = root.Uint8Array;\n\nmodule.exports = Uint8Array;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIC,UAAU,GAAGF,IAAI,CAACE,UAAU;AAEhCC,MAAM,CAACC,OAAO,GAAGF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}