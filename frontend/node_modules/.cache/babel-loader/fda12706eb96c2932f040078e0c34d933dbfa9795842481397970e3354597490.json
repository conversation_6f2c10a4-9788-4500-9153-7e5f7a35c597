{"ast": null, "code": "var roundingMap = {\n  ceil: Math.ceil,\n  round: Math.round,\n  floor: Math.floor,\n  trunc: function trunc(value) {\n    return value < 0 ? Math.ceil(value) : Math.floor(value);\n  } // Math.trunc is not supported by IE\n};\nvar defaultRoundingMethod = 'trunc';\nexport function getRoundingMethod(method) {\n  return method ? roundingMap[method] : roundingMap[defaultRoundingMethod];\n}", "map": {"version": 3, "names": ["roundingMap", "ceil", "Math", "round", "floor", "trunc", "value", "defaultRoundingMethod", "getRoundingMethod", "method"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/_lib/roundingMethods/index.js"], "sourcesContent": ["var roundingMap = {\n  ceil: Math.ceil,\n  round: Math.round,\n  floor: Math.floor,\n  trunc: function trunc(value) {\n    return value < 0 ? Math.ceil(value) : Math.floor(value);\n  } // Math.trunc is not supported by IE\n};\n\nvar defaultRoundingMethod = 'trunc';\nexport function getRoundingMethod(method) {\n  return method ? roundingMap[method] : roundingMap[defaultRoundingMethod];\n}"], "mappings": "AAAA,IAAIA,WAAW,GAAG;EAChBC,IAAI,EAAEC,IAAI,CAACD,IAAI;EACfE,KAAK,EAAED,IAAI,CAACC,KAAK;EACjBC,KAAK,EAAEF,IAAI,CAACE,KAAK;EACjBC,KAAK,EAAE,SAASA,KAAKA,CAACC,KAAK,EAAE;IAC3B,OAAOA,KAAK,GAAG,CAAC,GAAGJ,IAAI,CAACD,IAAI,CAACK,KAAK,CAAC,GAAGJ,IAAI,CAACE,KAAK,CAACE,KAAK,CAAC;EACzD,CAAC,CAAC;AACJ,CAAC;AAED,IAAIC,qBAAqB,GAAG,OAAO;AACnC,OAAO,SAASC,iBAAiBA,CAACC,MAAM,EAAE;EACxC,OAAOA,MAAM,GAAGT,WAAW,CAACS,MAAM,CAAC,GAAGT,WAAW,CAACO,qBAAqB,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}