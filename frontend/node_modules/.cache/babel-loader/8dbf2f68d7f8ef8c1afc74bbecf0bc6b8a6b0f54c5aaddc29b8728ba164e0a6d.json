{"ast": null, "code": "import { InternSet } from \"internmap\";\nexport default function difference(values) {\n  values = new InternSet(values);\n  for (var _len = arguments.length, others = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    others[_key - 1] = arguments[_key];\n  }\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}", "map": {"version": 3, "names": ["InternSet", "difference", "values", "_len", "arguments", "length", "others", "Array", "_key", "other", "value", "delete"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-array/src/difference.js"], "sourcesContent": ["import {InternSet} from \"internmap\";\n\nexport default function difference(values, ...others) {\n  values = new InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,UAAUA,CAACC,MAAM,EAAa;EACpDA,MAAM,GAAG,IAAIF,SAAS,CAACE,MAAM,CAAC;EAAC,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADaC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAElD,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;IAC1B,KAAK,MAAMI,KAAK,IAAID,KAAK,EAAE;MACzBP,MAAM,CAACS,MAAM,CAACD,KAAK,CAAC;IACtB;EACF;EACA,OAAOR,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}