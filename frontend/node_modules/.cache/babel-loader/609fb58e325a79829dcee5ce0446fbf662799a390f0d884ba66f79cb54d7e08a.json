{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nexport var EraParser = /*#__PURE__*/function (_Parser) {\n  _inherits(EraParser, _Parser);\n  var _super = _createSuper(EraParser);\n  function EraParser() {\n    var _this;\n    _classCallCheck(this, EraParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 140);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['R', 'u', 't', 'T']);\n    return _this;\n  }\n  _createClass(EraParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        // AD, BC\n        case 'G':\n        case 'GG':\n        case 'GGG':\n          return match.era(dateString, {\n            width: 'abbreviated'\n          }) || match.era(dateString, {\n            width: 'narrow'\n          });\n        // A, B\n        case 'GGGGG':\n          return match.era(dateString, {\n            width: 'narrow'\n          });\n        // Anno Domini, Before Christ\n        case 'GGGG':\n        default:\n          return match.era(dateString, {\n            width: 'wide'\n          }) || match.era(dateString, {\n            width: 'abbreviated'\n          }) || match.era(dateString, {\n            width: 'narrow'\n          });\n      }\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, flags, value) {\n      flags.era = value;\n      date.setUTCFullYear(value, 0, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return EraParser;\n}(Parser);", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "key", "value", "parse", "dateString", "token", "match", "era", "width", "set", "date", "flags", "setUTCFullYear", "setUTCHours"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/parse/_lib/parsers/EraParser.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nexport var EraParser = /*#__PURE__*/function (_Parser) {\n  _inherits(EraParser, _Parser);\n  var _super = _createSuper(EraParser);\n  function EraParser() {\n    var _this;\n    _classCallCheck(this, EraParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 140);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['R', 'u', 't', 'T']);\n    return _this;\n  }\n  _createClass(EraParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      switch (token) {\n        // AD, BC\n        case 'G':\n        case 'GG':\n        case 'GGG':\n          return match.era(dateString, {\n            width: 'abbreviated'\n          }) || match.era(dateString, {\n            width: 'narrow'\n          });\n        // A, B\n        case 'GGGGG':\n          return match.era(dateString, {\n            width: 'narrow'\n          });\n        // Anno Domini, Before Christ\n        case 'GGGG':\n        default:\n          return match.era(dateString, {\n            width: 'wide'\n          }) || match.era(dateString, {\n            width: 'abbreviated'\n          }) || match.era(dateString, {\n            width: 'narrow'\n          });\n      }\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, flags, value) {\n      flags.era = value;\n      date.setUTCFullYear(value, 0, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return EraParser;\n}(Parser);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,MAAM,QAAQ,cAAc;AACrC,OAAO,IAAIC,SAAS,GAAG,aAAa,UAAUC,OAAO,EAAE;EACrDL,SAAS,CAACI,SAAS,EAAEC,OAAO,CAAC;EAC7B,IAAIC,MAAM,GAAGL,YAAY,CAACG,SAAS,CAAC;EACpC,SAASA,SAASA,CAAA,EAAG;IACnB,IAAIG,KAAK;IACTV,eAAe,CAAC,IAAI,EAAEO,SAAS,CAAC;IAChC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDT,eAAe,CAACH,sBAAsB,CAACQ,KAAK,CAAC,EAAE,UAAU,EAAE,GAAG,CAAC;IAC/DL,eAAe,CAACH,sBAAsB,CAACQ,KAAK,CAAC,EAAE,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC1F,OAAOA,KAAK;EACd;EACAT,YAAY,CAACM,SAAS,EAAE,CAAC;IACvBa,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;MAC9C,QAAQD,KAAK;QACX;QACA,KAAK,GAAG;QACR,KAAK,IAAI;QACT,KAAK,KAAK;UACR,OAAOC,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;YAC3BI,KAAK,EAAE;UACT,CAAC,CAAC,IAAIF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;YAC1BI,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;QACA,KAAK,OAAO;UACV,OAAOF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;YAC3BI,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;QACA,KAAK,MAAM;QACX;UACE,OAAOF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;YAC3BI,KAAK,EAAE;UACT,CAAC,CAAC,IAAIF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;YAC1BI,KAAK,EAAE;UACT,CAAC,CAAC,IAAIF,KAAK,CAACC,GAAG,CAACH,UAAU,EAAE;YAC1BI,KAAK,EAAE;UACT,CAAC,CAAC;MACN;IACF;EACF,CAAC,EAAE;IACDP,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASO,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAET,KAAK,EAAE;MACtCS,KAAK,CAACJ,GAAG,GAAGL,KAAK;MACjBQ,IAAI,CAACE,cAAc,CAACV,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MAChCQ,IAAI,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5B,OAAOH,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOtB,SAAS;AAClB,CAAC,CAACD,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}