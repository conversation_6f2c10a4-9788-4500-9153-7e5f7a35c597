{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { daysInWeek } from \"../constants/index.js\";\n/**\n * @name weeksToDays\n * @category Conversion Helpers\n * @summary Convert weeks to days.\n *\n * @description\n * Convert a number of weeks to a full number of days.\n *\n * @param {number} weeks - number of weeks to be converted\n *\n * @returns {number} the number of weeks converted in days\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 weeks into days\n * const result = weeksToDays(2)\n * //=> 14\n */\nexport default function weeksToDays(weeks) {\n  requiredArgs(1, arguments);\n  return Math.floor(weeks * daysInWeek);\n}", "map": {"version": 3, "names": ["requiredArgs", "daysInWeek", "weeksToDays", "weeks", "arguments", "Math", "floor"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/weeksToDays/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { daysInWeek } from \"../constants/index.js\";\n/**\n * @name weeksToDays\n * @category Conversion Helpers\n * @summary Convert weeks to days.\n *\n * @description\n * Convert a number of weeks to a full number of days.\n *\n * @param {number} weeks - number of weeks to be converted\n *\n * @returns {number} the number of weeks converted in days\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 weeks into days\n * const result = weeksToDays(2)\n * //=> 14\n */\nexport default function weeksToDays(weeks) {\n  requiredArgs(1, arguments);\n  return Math.floor(weeks * daysInWeek);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,UAAU,QAAQ,uBAAuB;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzCH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAGF,UAAU,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}