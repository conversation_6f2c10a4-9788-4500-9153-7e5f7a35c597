{"ast": null, "code": "import _objectSpread from\"/Volumes/2tb/github/test-scrap/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{CogIcon,ChartBarIcon,ServerIcon,CircleStackIcon,DocumentTextIcon}from'@heroicons/react/24/outline';import Layout from'../components/Layout/Layout';// Import the new admin components\nimport ScraperControls from'../components/Admin/ScraperControls';import ScrapingLogs from'../components/Admin/ScrapingLogs';import SystemConfiguration from'../components/Admin/SystemConfiguration';import PerformanceMonitoring from'../components/Admin/PerformanceMonitoring';import DataManagement from'../components/Admin/DataManagement';// Import types\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Admin=()=>{const[activeTab,setActiveTab]=useState('overview');// Mock data - in real implementation, this would come from API calls\nconst[scraperStatuses,setScraperStatuses]=useState([{market:'coto',status:'running',lastRun:new Date(Date.now()-1000*60*30).toISOString(),// 30 minutes ago\nnextRun:new Date(Date.now()+1000*60*60*2).toISOString(),// 2 hours from now\nproductsScraped:1247,errors:3,config:{market:'coto',frequency:'daily',timeout:30,max_pages:10,rate_limit_delay:2000}},{market:'carrefour',status:'stopped',lastRun:new Date(Date.now()-1000*60*60*4).toISOString(),// 4 hours ago\nproductsScraped:892,errors:1,config:{market:'carrefour',frequency:'daily',timeout:45,max_pages:15,rate_limit_delay:1500}},{market:'jumbo',status:'error',lastRun:new Date(Date.now()-1000*60*60*2).toISOString(),// 2 hours ago\nproductsScraped:634,errors:12,config:{market:'jumbo',frequency:'daily',timeout:60,max_pages:12,rate_limit_delay:2000}},{market:'disco',status:'stopped',lastRun:new Date(Date.now()-1000*60*60*6).toISOString(),// 6 hours ago\nproductsScraped:445,errors:0,config:{market:'disco',frequency:'weekly',timeout:30,max_pages:8,rate_limit_delay:2500}},{market:'vea',status:'running',lastRun:new Date(Date.now()-1000*60*45).toISOString(),// 45 minutes ago\nnextRun:new Date(Date.now()+1000*60*60*1.5).toISOString(),// 1.5 hours from now\nproductsScraped:723,errors:2,config:{market:'vea',frequency:'daily',timeout:40,max_pages:10,rate_limit_delay:2000}}]);const[logs,setLogs]=useState([{id:'1',timestamp:new Date(Date.now()-1000*60*5).toISOString(),market:'coto',level:'info',message:'Successfully scraped 45 products from category \"Lácteos\"',details:{category:'Lácteos',products:45,duration:'2.3s'}},{id:'2',timestamp:new Date(Date.now()-1000*60*12).toISOString(),market:'carrefour',level:'warning',message:'High response time detected (3.2s average)',details:{averageResponseTime:'3.2s',threshold:'2.0s'}},{id:'3',timestamp:new Date(Date.now()-1000*60*18).toISOString(),market:'jumbo',level:'error',message:'Failed to load product page: Connection timeout',details:{url:'https://jumbo.com.ar/products/page/5',error:'TIMEOUT'}},{id:'4',timestamp:new Date(Date.now()-1000*60*25).toISOString(),market:'vea',level:'info',message:'Scraping session completed successfully',details:{totalProducts:156,duration:'4m 32s',errors:0}},{id:'5',timestamp:new Date(Date.now()-1000*60*35).toISOString(),market:'disco',level:'warning',message:'Product price format changed, using fallback parser',details:{oldFormat:'$XX.XX',newFormat:'XX,XX $',fallbackUsed:true}}]);const systemMetrics={productsScrapedLast24h:3941,successRate:94.2,averageResponseTime:1247,cpuUsage:34.5,memoryUsage:67.8,databaseSize:'2.4 GB'};const databaseStats={totalProducts:15847,productsByMarket:{coto:4521,carrefour:3892,jumbo:3234,disco:2145,vea:2055},lastUpdated:new Date().toISOString(),databaseSize:'2.4 GB'};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:flex md:items-center md:justify-between\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\",children:\"Admin Panel\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"Manage scrapers, monitor performance, and configure system settings\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"-mb-px flex space-x-8 overflow-x-auto\",children:[{id:'overview',name:'Overview',icon:ChartBarIcon},{id:'scrapers',name:'Scrapers',icon:CogIcon},{id:'logs',name:'Logs',icon:DocumentTextIcon},{id:'performance',name:'Performance',icon:ChartBarIcon},{id:'config',name:'Configuration',icon:ServerIcon},{id:'data',name:'Data Management',icon:CircleStackIcon}].map(tab=>{const Icon=tab.icon;return/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center \".concat(activeTab===tab.id?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:[/*#__PURE__*/_jsx(Icon,{className:\"h-4 w-4 mr-2\"}),tab.name]},tab.id);})})}),(()=>{const handleStartScraper=market=>{console.log(\"Starting scraper for \".concat(market));setScraperStatuses(prev=>prev.map(s=>s.market===market?_objectSpread(_objectSpread({},s),{},{status:'running'}):s));};const handleStopScraper=market=>{console.log(\"Stopping scraper for \".concat(market));setScraperStatuses(prev=>prev.map(s=>s.market===market?_objectSpread(_objectSpread({},s),{},{status:'stopped'}):s));};const handleStartAll=()=>{console.log('Starting all scrapers');setScraperStatuses(prev=>prev.map(s=>_objectSpread(_objectSpread({},s),{},{status:'running'})));};const handleStopAll=()=>{console.log('Stopping all scrapers');setScraperStatuses(prev=>prev.map(s=>_objectSpread(_objectSpread({},s),{},{status:'stopped'})));};const handleConfigChange=(market,config)=>{console.log(\"Updating config for \".concat(market,\":\"),config);setScraperStatuses(prev=>prev.map(s=>s.market===market?_objectSpread(_objectSpread({},s),{},{config}):s));};const handleExportLogs=()=>console.log('Exporting logs');const handleClearLogs=()=>{console.log('Clearing logs');setLogs([]);};const handleRefreshLogs=()=>console.log('Refreshing logs');const handleSaveConfig=config=>console.log('Saving system configuration:',config);const handleExportData=market=>console.log(\"Exporting data\".concat(market?\" for \".concat(market):''));const handleImportData=file=>console.log('Importing data from file:',file.name);const handleCleanupData=options=>console.log('Cleaning up data with options:',options);const handleOptimizeDatabase=()=>console.log('Optimizing database');return null;// This is just to define the handlers in scope\n})(),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-8\",children:[activeTab==='overview'&&/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(CogIcon,{className:\"h-8 w-8 text-blue-500 mr-3\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900\",children:\"Active Scrapers\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-blue-600 mb-2\",children:scraperStatuses.filter(s=>s.status==='running').length}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[\"of \",scraperStatuses.length,\" total scrapers\"]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(ChartBarIcon,{className:\"h-8 w-8 text-green-500 mr-3\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900\",children:\"Products Today\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-green-600 mb-2\",children:systemMetrics.productsScrapedLast24h.toLocaleString()}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[systemMetrics.successRate,\"% success rate\"]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(CircleStackIcon,{className:\"h-8 w-8 text-purple-500 mr-3\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900\",children:\"Total Products\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-purple-600 mb-2\",children:databaseStats.totalProducts.toLocaleString()}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[databaseStats.databaseSize,\" database size\"]})]})})]}),activeTab==='scrapers'&&/*#__PURE__*/_jsx(ScraperControls,{scrapers:scraperStatuses,onStartScraper:market=>{console.log(\"Starting scraper for \".concat(market));setScraperStatuses(prev=>prev.map(s=>s.market===market?_objectSpread(_objectSpread({},s),{},{status:'running'}):s));},onStopScraper:market=>{console.log(\"Stopping scraper for \".concat(market));setScraperStatuses(prev=>prev.map(s=>s.market===market?_objectSpread(_objectSpread({},s),{},{status:'stopped'}):s));},onStartAll:()=>{console.log('Starting all scrapers');setScraperStatuses(prev=>prev.map(s=>_objectSpread(_objectSpread({},s),{},{status:'running'})));},onStopAll:()=>{console.log('Stopping all scrapers');setScraperStatuses(prev=>prev.map(s=>_objectSpread(_objectSpread({},s),{},{status:'stopped'})));},onConfigChange:(market,config)=>{console.log(\"Updating config for \".concat(market,\":\"),config);setScraperStatuses(prev=>prev.map(s=>s.market===market?_objectSpread(_objectSpread({},s),{},{config}):s));}}),activeTab==='logs'&&/*#__PURE__*/_jsx(ScrapingLogs,{logs:logs,onExportLogs:()=>console.log('Exporting logs'),onClearLogs:()=>{console.log('Clearing logs');setLogs([]);},onRefresh:()=>console.log('Refreshing logs')}),activeTab==='performance'&&/*#__PURE__*/_jsx(PerformanceMonitoring,{metrics:systemMetrics}),activeTab==='config'&&/*#__PURE__*/_jsx(SystemConfiguration,{onSaveConfig:config=>console.log('Saving system configuration:',config)}),activeTab==='data'&&/*#__PURE__*/_jsx(DataManagement,{stats:databaseStats,onExportData:market=>console.log(\"Exporting data\".concat(market?\" for \".concat(market):'')),onImportData:file=>console.log('Importing data from file:',file.name),onCleanupData:options=>console.log('Cleaning up data with options:',options),onOptimizeDatabase:()=>console.log('Optimizing database')})]})]})});};export default Admin;", "map": {"version": 3, "names": ["React", "useState", "CogIcon", "ChartBarIcon", "ServerIcon", "CircleStackIcon", "DocumentTextIcon", "Layout", "ScraperControls", "ScrapingLogs", "SystemConfiguration", "PerformanceMonitoring", "DataManagement", "jsx", "_jsx", "jsxs", "_jsxs", "Admin", "activeTab", "setActiveTab", "scraperStatuses", "setScraperStatuses", "market", "status", "lastRun", "Date", "now", "toISOString", "nextRun", "productsScraped", "errors", "config", "frequency", "timeout", "max_pages", "rate_limit_delay", "logs", "setLogs", "id", "timestamp", "level", "message", "details", "category", "products", "duration", "averageResponseTime", "threshold", "url", "error", "totalProducts", "oldFormat", "newFormat", "fallbackUsed", "systemMetrics", "productsScrapedLast24h", "successRate", "cpuUsage", "memoryUsage", "databaseSize", "databaseStats", "productsByMarket", "coto", "carrefour", "jumbo", "disco", "vea", "lastUpdated", "children", "className", "name", "icon", "map", "tab", "Icon", "onClick", "concat", "handleStartScraper", "console", "log", "prev", "s", "_objectSpread", "handleStopScraper", "handleStartAll", "handleStopAll", "handleConfigChange", "handleExportLogs", "handleClearLogs", "handleRefreshLogs", "handleSaveConfig", "handleExportData", "handleImportData", "file", "handleCleanupData", "options", "handleOptimizeDatabase", "filter", "length", "toLocaleString", "scrapers", "onStartScraper", "onStopScraper", "onStartAll", "onStopAll", "onConfigChange", "onExportLogs", "onClearLogs", "onRefresh", "metrics", "onSaveConfig", "stats", "onExportData", "onImportData", "onCleanupData", "onOptimizeDatabase"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/src/pages/Admin.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  CogIcon,\n  ChartBarIcon,\n  ServerIcon,\n  CircleStackIcon,\n  DocumentTextIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\nimport Layout from '../components/Layout/Layout';\n\n// Import the new admin components\nimport ScraperControls from '../components/Admin/ScraperControls';\nimport ScrapingLogs from '../components/Admin/ScrapingLogs';\nimport SystemConfiguration from '../components/Admin/SystemConfiguration';\nimport PerformanceMonitoring from '../components/Admin/PerformanceMonitoring';\nimport DataManagement from '../components/Admin/DataManagement';\n\n// Import types\nimport {\n  ScraperStatus,\n  LogEntry,\n  SystemMetrics,\n  DatabaseStats,\n  MarketName\n} from '../types';\n\nconst Admin: React.FC = () => {\n  const [activeTab, setActiveTab] = useState('overview');\n\n  // Mock data - in real implementation, this would come from API calls\n  const [scraperStatuses, setScraperStatuses] = useState<ScraperStatus[]>([\n    {\n      market: 'coto',\n      status: 'running',\n      lastRun: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago\n      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 2).toISOString(), // 2 hours from now\n      productsScraped: 1247,\n      errors: 3,\n      config: {\n        market: 'coto',\n        frequency: 'daily',\n        timeout: 30,\n        max_pages: 10,\n        rate_limit_delay: 2000,\n      }\n    },\n    {\n      market: 'carrefour',\n      status: 'stopped',\n      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago\n      productsScraped: 892,\n      errors: 1,\n      config: {\n        market: 'carrefour',\n        frequency: 'daily',\n        timeout: 45,\n        max_pages: 15,\n        rate_limit_delay: 1500,\n      }\n    },\n    {\n      market: 'jumbo',\n      status: 'error',\n      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago\n      productsScraped: 634,\n      errors: 12,\n      config: {\n        market: 'jumbo',\n        frequency: 'daily',\n        timeout: 60,\n        max_pages: 12,\n        rate_limit_delay: 2000,\n      }\n    },\n    {\n      market: 'disco',\n      status: 'stopped',\n      lastRun: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago\n      productsScraped: 445,\n      errors: 0,\n      config: {\n        market: 'disco',\n        frequency: 'weekly',\n        timeout: 30,\n        max_pages: 8,\n        rate_limit_delay: 2500,\n      }\n    },\n    {\n      market: 'vea',\n      status: 'running',\n      lastRun: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago\n      nextRun: new Date(Date.now() + 1000 * 60 * 60 * 1.5).toISOString(), // 1.5 hours from now\n      productsScraped: 723,\n      errors: 2,\n      config: {\n        market: 'vea',\n        frequency: 'daily',\n        timeout: 40,\n        max_pages: 10,\n        rate_limit_delay: 2000,\n      }\n    },\n  ]);\n\n  const [logs, setLogs] = useState<LogEntry[]>([\n    {\n      id: '1',\n      timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),\n      market: 'coto',\n      level: 'info',\n      message: 'Successfully scraped 45 products from category \"Lácteos\"',\n      details: { category: 'Lácteos', products: 45, duration: '2.3s' }\n    },\n    {\n      id: '2',\n      timestamp: new Date(Date.now() - 1000 * 60 * 12).toISOString(),\n      market: 'carrefour',\n      level: 'warning',\n      message: 'High response time detected (3.2s average)',\n      details: { averageResponseTime: '3.2s', threshold: '2.0s' }\n    },\n    {\n      id: '3',\n      timestamp: new Date(Date.now() - 1000 * 60 * 18).toISOString(),\n      market: 'jumbo',\n      level: 'error',\n      message: 'Failed to load product page: Connection timeout',\n      details: { url: 'https://jumbo.com.ar/products/page/5', error: 'TIMEOUT' }\n    },\n    {\n      id: '4',\n      timestamp: new Date(Date.now() - 1000 * 60 * 25).toISOString(),\n      market: 'vea',\n      level: 'info',\n      message: 'Scraping session completed successfully',\n      details: { totalProducts: 156, duration: '4m 32s', errors: 0 }\n    },\n    {\n      id: '5',\n      timestamp: new Date(Date.now() - 1000 * 60 * 35).toISOString(),\n      market: 'disco',\n      level: 'warning',\n      message: 'Product price format changed, using fallback parser',\n      details: { oldFormat: '$XX.XX', newFormat: 'XX,XX $', fallbackUsed: true }\n    },\n  ]);\n\n  const systemMetrics: SystemMetrics = {\n    productsScrapedLast24h: 3941,\n    successRate: 94.2,\n    averageResponseTime: 1247,\n    cpuUsage: 34.5,\n    memoryUsage: 67.8,\n    databaseSize: '2.4 GB',\n  };\n\n  const databaseStats: DatabaseStats = {\n    totalProducts: 15847,\n    productsByMarket: {\n      coto: 4521,\n      carrefour: 3892,\n      jumbo: 3234,\n      disco: 2145,\n      vea: 2055,\n    },\n    lastUpdated: new Date().toISOString(),\n    databaseSize: '2.4 GB',\n  };\n\n  return (\n    <Layout>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"md:flex md:items-center md:justify-between\">\n          <div className=\"flex-1 min-w-0\">\n            <h1 className=\"text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate\">\n              Admin Panel\n            </h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Manage scrapers, monitor performance, and configure system settings\n            </p>\n          </div>\n        </div>\n\n        {/* Navigation Tabs */}\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 overflow-x-auto\">\n            {[\n              { id: 'overview', name: 'Overview', icon: ChartBarIcon },\n              { id: 'scrapers', name: 'Scrapers', icon: CogIcon },\n              { id: 'logs', name: 'Logs', icon: DocumentTextIcon },\n              { id: 'performance', name: 'Performance', icon: ChartBarIcon },\n              { id: 'config', name: 'Configuration', icon: ServerIcon },\n              { id: 'data', name: 'Data Management', icon: CircleStackIcon },\n            ].map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4 mr-2\" />\n                  {tab.name}\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* Mock handlers - in real implementation, these would make API calls */}\n        {(() => {\n          const handleStartScraper = (market: MarketName) => {\n            console.log(`Starting scraper for ${market}`);\n            setScraperStatuses(prev =>\n              prev.map(s => s.market === market ? { ...s, status: 'running' as const } : s)\n            );\n          };\n\n          const handleStopScraper = (market: MarketName) => {\n            console.log(`Stopping scraper for ${market}`);\n            setScraperStatuses(prev =>\n              prev.map(s => s.market === market ? { ...s, status: 'stopped' as const } : s)\n            );\n          };\n\n          const handleStartAll = () => {\n            console.log('Starting all scrapers');\n            setScraperStatuses(prev =>\n              prev.map(s => ({ ...s, status: 'running' as const }))\n            );\n          };\n\n          const handleStopAll = () => {\n            console.log('Stopping all scrapers');\n            setScraperStatuses(prev =>\n              prev.map(s => ({ ...s, status: 'stopped' as const }))\n            );\n          };\n\n          const handleConfigChange = (market: MarketName, config: any) => {\n            console.log(`Updating config for ${market}:`, config);\n            setScraperStatuses(prev =>\n              prev.map(s => s.market === market ? { ...s, config } : s)\n            );\n          };\n\n          const handleExportLogs = () => console.log('Exporting logs');\n          const handleClearLogs = () => { console.log('Clearing logs'); setLogs([]); };\n          const handleRefreshLogs = () => console.log('Refreshing logs');\n          const handleSaveConfig = (config: any) => console.log('Saving system configuration:', config);\n          const handleExportData = (market?: MarketName) => console.log(`Exporting data${market ? ` for ${market}` : ''}`);\n          const handleImportData = (file: File) => console.log('Importing data from file:', file.name);\n          const handleCleanupData = (options: any) => console.log('Cleaning up data with options:', options);\n          const handleOptimizeDatabase = () => console.log('Optimizing database');\n\n          return null; // This is just to define the handlers in scope\n        })()}\n\n        {/* Tab Content */}\n        <div className=\"space-y-8\">\n          {activeTab === 'overview' && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {/* Quick Stats Cards */}\n              <div className=\"card\">\n                <div className=\"card-body\">\n                  <div className=\"flex items-center mb-4\">\n                    <CogIcon className=\"h-8 w-8 text-blue-500 mr-3\" />\n                    <h2 className=\"text-xl font-semibold text-gray-900\">\n                      Active Scrapers\n                    </h2>\n                  </div>\n                  <div className=\"text-3xl font-bold text-blue-600 mb-2\">\n                    {scraperStatuses.filter(s => s.status === 'running').length}\n                  </div>\n                  <p className=\"text-sm text-gray-600\">\n                    of {scraperStatuses.length} total scrapers\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"card\">\n                <div className=\"card-body\">\n                  <div className=\"flex items-center mb-4\">\n                    <ChartBarIcon className=\"h-8 w-8 text-green-500 mr-3\" />\n                    <h2 className=\"text-xl font-semibold text-gray-900\">\n                      Products Today\n                    </h2>\n                  </div>\n                  <div className=\"text-3xl font-bold text-green-600 mb-2\">\n                    {systemMetrics.productsScrapedLast24h.toLocaleString()}\n                  </div>\n                  <p className=\"text-sm text-gray-600\">\n                    {systemMetrics.successRate}% success rate\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"card\">\n                <div className=\"card-body\">\n                  <div className=\"flex items-center mb-4\">\n                    <CircleStackIcon className=\"h-8 w-8 text-purple-500 mr-3\" />\n                    <h2 className=\"text-xl font-semibold text-gray-900\">\n                      Total Products\n                    </h2>\n                  </div>\n                  <div className=\"text-3xl font-bold text-purple-600 mb-2\">\n                    {databaseStats.totalProducts.toLocaleString()}\n                  </div>\n                  <p className=\"text-sm text-gray-600\">\n                    {databaseStats.databaseSize} database size\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'scrapers' && (\n            <ScraperControls\n              scrapers={scraperStatuses}\n              onStartScraper={(market: MarketName) => {\n                console.log(`Starting scraper for ${market}`);\n                setScraperStatuses(prev =>\n                  prev.map(s => s.market === market ? { ...s, status: 'running' as const } : s)\n                );\n              }}\n              onStopScraper={(market: MarketName) => {\n                console.log(`Stopping scraper for ${market}`);\n                setScraperStatuses(prev =>\n                  prev.map(s => s.market === market ? { ...s, status: 'stopped' as const } : s)\n                );\n              }}\n              onStartAll={() => {\n                console.log('Starting all scrapers');\n                setScraperStatuses(prev =>\n                  prev.map(s => ({ ...s, status: 'running' as const }))\n                );\n              }}\n              onStopAll={() => {\n                console.log('Stopping all scrapers');\n                setScraperStatuses(prev =>\n                  prev.map(s => ({ ...s, status: 'stopped' as const }))\n                );\n              }}\n              onConfigChange={(market: MarketName, config: any) => {\n                console.log(`Updating config for ${market}:`, config);\n                setScraperStatuses(prev =>\n                  prev.map(s => s.market === market ? { ...s, config } : s)\n                );\n              }}\n            />\n          )}\n\n          {activeTab === 'logs' && (\n            <ScrapingLogs\n              logs={logs}\n              onExportLogs={() => console.log('Exporting logs')}\n              onClearLogs={() => { console.log('Clearing logs'); setLogs([]); }}\n              onRefresh={() => console.log('Refreshing logs')}\n            />\n          )}\n\n          {activeTab === 'performance' && (\n            <PerformanceMonitoring metrics={systemMetrics} />\n          )}\n\n          {activeTab === 'config' && (\n            <SystemConfiguration onSaveConfig={(config: any) => console.log('Saving system configuration:', config)} />\n          )}\n\n          {activeTab === 'data' && (\n            <DataManagement\n              stats={databaseStats}\n              onExportData={(market?: MarketName) => console.log(`Exporting data${market ? ` for ${market}` : ''}`)}\n              onImportData={(file: File) => console.log('Importing data from file:', file.name)}\n              onCleanupData={(options: any) => console.log('Cleaning up data with options:', options)}\n              onOptimizeDatabase={() => console.log('Optimizing database')}\n            />\n          )}\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default Admin;\n"], "mappings": "4HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAmB,OAAO,CAClD,OACEC,OAAO,CACPC,YAAY,CACZC,UAAU,CACVC,eAAe,CACfC,gBAAgB,KAEX,6BAA6B,CACpC,MAAO,CAAAC,MAAM,KAAM,6BAA6B,CAEhD;AACA,MAAO,CAAAC,eAAe,KAAM,qCAAqC,CACjE,MAAO,CAAAC,YAAY,KAAM,kCAAkC,CAC3D,MAAO,CAAAC,mBAAmB,KAAM,yCAAyC,CACzE,MAAO,CAAAC,qBAAqB,KAAM,2CAA2C,CAC7E,MAAO,CAAAC,cAAc,KAAM,oCAAoC,CAE/D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASA,KAAM,CAAAC,KAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,UAAU,CAAC,CAEtD;AACA,KAAM,CAACmB,eAAe,CAAEC,kBAAkB,CAAC,CAAGpB,QAAQ,CAAkB,CACtE,CACEqB,MAAM,CAAE,MAAM,CACdC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAE;AAC9DC,OAAO,CAAE,GAAI,CAAAH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAE;AAClEE,eAAe,CAAE,IAAI,CACrBC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,CACNT,MAAM,CAAE,MAAM,CACdU,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,gBAAgB,CAAE,IACpB,CACF,CAAC,CACD,CACEb,MAAM,CAAE,WAAW,CACnBC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAE;AAClEE,eAAe,CAAE,GAAG,CACpBC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,CACNT,MAAM,CAAE,WAAW,CACnBU,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,gBAAgB,CAAE,IACpB,CACF,CAAC,CACD,CACEb,MAAM,CAAE,OAAO,CACfC,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAE;AAClEE,eAAe,CAAE,GAAG,CACpBC,MAAM,CAAE,EAAE,CACVC,MAAM,CAAE,CACNT,MAAM,CAAE,OAAO,CACfU,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,gBAAgB,CAAE,IACpB,CACF,CAAC,CACD,CACEb,MAAM,CAAE,OAAO,CACfC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAE;AAClEE,eAAe,CAAE,GAAG,CACpBC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,CACNT,MAAM,CAAE,OAAO,CACfU,SAAS,CAAE,QAAQ,CACnBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,CAAC,CACZC,gBAAgB,CAAE,IACpB,CACF,CAAC,CACD,CACEb,MAAM,CAAE,KAAK,CACbC,MAAM,CAAE,SAAS,CACjBC,OAAO,CAAE,GAAI,CAAAC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAE;AAC9DC,OAAO,CAAE,GAAI,CAAAH,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CAAE;AACpEE,eAAe,CAAE,GAAG,CACpBC,MAAM,CAAE,CAAC,CACTC,MAAM,CAAE,CACNT,MAAM,CAAE,KAAK,CACbU,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,gBAAgB,CAAE,IACpB,CACF,CAAC,CACF,CAAC,CAEF,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGpC,QAAQ,CAAa,CAC3C,CACEqC,EAAE,CAAE,GAAG,CACPC,SAAS,CAAE,GAAI,CAAAd,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC7DL,MAAM,CAAE,MAAM,CACdkB,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,0DAA0D,CACnEC,OAAO,CAAE,CAAEC,QAAQ,CAAE,SAAS,CAAEC,QAAQ,CAAE,EAAE,CAAEC,QAAQ,CAAE,MAAO,CACjE,CAAC,CACD,CACEP,EAAE,CAAE,GAAG,CACPC,SAAS,CAAE,GAAI,CAAAd,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAC9DL,MAAM,CAAE,WAAW,CACnBkB,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,4CAA4C,CACrDC,OAAO,CAAE,CAAEI,mBAAmB,CAAE,MAAM,CAAEC,SAAS,CAAE,MAAO,CAC5D,CAAC,CACD,CACET,EAAE,CAAE,GAAG,CACPC,SAAS,CAAE,GAAI,CAAAd,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAC9DL,MAAM,CAAE,OAAO,CACfkB,KAAK,CAAE,OAAO,CACdC,OAAO,CAAE,iDAAiD,CAC1DC,OAAO,CAAE,CAAEM,GAAG,CAAE,sCAAsC,CAAEC,KAAK,CAAE,SAAU,CAC3E,CAAC,CACD,CACEX,EAAE,CAAE,GAAG,CACPC,SAAS,CAAE,GAAI,CAAAd,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAC9DL,MAAM,CAAE,KAAK,CACbkB,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,yCAAyC,CAClDC,OAAO,CAAE,CAAEQ,aAAa,CAAE,GAAG,CAAEL,QAAQ,CAAE,QAAQ,CAAEf,MAAM,CAAE,CAAE,CAC/D,CAAC,CACD,CACEQ,EAAE,CAAE,GAAG,CACPC,SAAS,CAAE,GAAI,CAAAd,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAC9DL,MAAM,CAAE,OAAO,CACfkB,KAAK,CAAE,SAAS,CAChBC,OAAO,CAAE,qDAAqD,CAC9DC,OAAO,CAAE,CAAES,SAAS,CAAE,QAAQ,CAAEC,SAAS,CAAE,SAAS,CAAEC,YAAY,CAAE,IAAK,CAC3E,CAAC,CACF,CAAC,CAEF,KAAM,CAAAC,aAA4B,CAAG,CACnCC,sBAAsB,CAAE,IAAI,CAC5BC,WAAW,CAAE,IAAI,CACjBV,mBAAmB,CAAE,IAAI,CACzBW,QAAQ,CAAE,IAAI,CACdC,WAAW,CAAE,IAAI,CACjBC,YAAY,CAAE,QAChB,CAAC,CAED,KAAM,CAAAC,aAA4B,CAAG,CACnCV,aAAa,CAAE,KAAK,CACpBW,gBAAgB,CAAE,CAChBC,IAAI,CAAE,IAAI,CACVC,SAAS,CAAE,IAAI,CACfC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IAAI,CACXC,GAAG,CAAE,IACP,CAAC,CACDC,WAAW,CAAE,GAAI,CAAA1C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CACrCgC,YAAY,CAAE,QAChB,CAAC,CAED,mBACE7C,IAAA,CAACP,MAAM,EAAA6D,QAAA,cACLpD,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAD,QAAA,eAExBtD,IAAA,QAAKuD,SAAS,CAAC,4CAA4C,CAAAD,QAAA,cACzDpD,KAAA,QAAKqD,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BtD,IAAA,OAAIuD,SAAS,CAAC,oEAAoE,CAAAD,QAAA,CAAC,aAEnF,CAAI,CAAC,cACLtD,IAAA,MAAGuD,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,qEAE1C,CAAG,CAAC,EACD,CAAC,CACH,CAAC,cAGNtD,IAAA,QAAKuD,SAAS,CAAC,0BAA0B,CAAAD,QAAA,cACvCtD,IAAA,QAAKuD,SAAS,CAAC,uCAAuC,CAAAD,QAAA,CACnD,CACC,CAAE9B,EAAE,CAAE,UAAU,CAAEgC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAEpE,YAAa,CAAC,CACxD,CAAEmC,EAAE,CAAE,UAAU,CAAEgC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAErE,OAAQ,CAAC,CACnD,CAAEoC,EAAE,CAAE,MAAM,CAAEgC,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAEjE,gBAAiB,CAAC,CACpD,CAAEgC,EAAE,CAAE,aAAa,CAAEgC,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAEpE,YAAa,CAAC,CAC9D,CAAEmC,EAAE,CAAE,QAAQ,CAAEgC,IAAI,CAAE,eAAe,CAAEC,IAAI,CAAEnE,UAAW,CAAC,CACzD,CAAEkC,EAAE,CAAE,MAAM,CAAEgC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAElE,eAAgB,CAAC,CAC/D,CAACmE,GAAG,CAAEC,GAAG,EAAK,CACb,KAAM,CAAAC,IAAI,CAAGD,GAAG,CAACF,IAAI,CACrB,mBACEvD,KAAA,WAEE2D,OAAO,CAAEA,CAAA,GAAMxD,YAAY,CAACsD,GAAG,CAACnC,EAAE,CAAE,CACpC+B,SAAS,iFAAAO,MAAA,CACP1D,SAAS,GAAKuD,GAAG,CAACnC,EAAE,CAChB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAA8B,QAAA,eAEHtD,IAAA,CAAC4D,IAAI,EAACL,SAAS,CAAC,cAAc,CAAE,CAAC,CAChCI,GAAG,CAACH,IAAI,GATJG,GAAG,CAACnC,EAUH,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,CACH,CAAC,CAGL,CAAC,IAAM,CACN,KAAM,CAAAuC,kBAAkB,CAAIvD,MAAkB,EAAK,CACjDwD,OAAO,CAACC,GAAG,yBAAAH,MAAA,CAAyBtD,MAAM,CAAE,CAAC,CAC7CD,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKA,MAAM,CAAA4D,aAAA,CAAAA,aAAA,IAAQD,CAAC,MAAE1D,MAAM,CAAE,SAAkB,GAAK0D,CAAC,CAC9E,CAAC,CACH,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAI7D,MAAkB,EAAK,CAChDwD,OAAO,CAACC,GAAG,yBAAAH,MAAA,CAAyBtD,MAAM,CAAE,CAAC,CAC7CD,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKA,MAAM,CAAA4D,aAAA,CAAAA,aAAA,IAAQD,CAAC,MAAE1D,MAAM,CAAE,SAAkB,GAAK0D,CAAC,CAC9E,CAAC,CACH,CAAC,CAED,KAAM,CAAAG,cAAc,CAAGA,CAAA,GAAM,CAC3BN,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC,CACpC1D,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAAC,aAAA,CAAAA,aAAA,IAAUD,CAAC,MAAE1D,MAAM,CAAE,SAAkB,EAAG,CACtD,CAAC,CACH,CAAC,CAED,KAAM,CAAA8D,aAAa,CAAGA,CAAA,GAAM,CAC1BP,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC,CACpC1D,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAAC,aAAA,CAAAA,aAAA,IAAUD,CAAC,MAAE1D,MAAM,CAAE,SAAkB,EAAG,CACtD,CAAC,CACH,CAAC,CAED,KAAM,CAAA+D,kBAAkB,CAAGA,CAAChE,MAAkB,CAAES,MAAW,GAAK,CAC9D+C,OAAO,CAACC,GAAG,wBAAAH,MAAA,CAAwBtD,MAAM,MAAKS,MAAM,CAAC,CACrDV,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKA,MAAM,CAAA4D,aAAA,CAAAA,aAAA,IAAQD,CAAC,MAAElD,MAAM,GAAKkD,CAAC,CAC1D,CAAC,CACH,CAAC,CAED,KAAM,CAAAM,gBAAgB,CAAGA,CAAA,GAAMT,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC5D,KAAM,CAAAS,eAAe,CAAGA,CAAA,GAAM,CAAEV,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAAE1C,OAAO,CAAC,EAAE,CAAC,CAAE,CAAC,CAC5E,KAAM,CAAAoD,iBAAiB,CAAGA,CAAA,GAAMX,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAC9D,KAAM,CAAAW,gBAAgB,CAAI3D,MAAW,EAAK+C,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEhD,MAAM,CAAC,CAC7F,KAAM,CAAA4D,gBAAgB,CAAIrE,MAAmB,EAAKwD,OAAO,CAACC,GAAG,kBAAAH,MAAA,CAAkBtD,MAAM,SAAAsD,MAAA,CAAWtD,MAAM,EAAK,EAAE,CAAE,CAAC,CAChH,KAAM,CAAAsE,gBAAgB,CAAIC,IAAU,EAAKf,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEc,IAAI,CAACvB,IAAI,CAAC,CAC5F,KAAM,CAAAwB,iBAAiB,CAAIC,OAAY,EAAKjB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEgB,OAAO,CAAC,CAClG,KAAM,CAAAC,sBAAsB,CAAGA,CAAA,GAAMlB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAEvE,MAAO,KAAI,CAAE;AACf,CAAC,EAAE,CAAC,cAGJ/D,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAD,QAAA,EACvBlD,SAAS,GAAK,UAAU,eACvBF,KAAA,QAAKqD,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eAEnEtD,IAAA,QAAKuD,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBpD,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBpD,KAAA,QAAKqD,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCtD,IAAA,CAACZ,OAAO,EAACmE,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAClDvD,IAAA,OAAIuD,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,iBAEpD,CAAI,CAAC,EACF,CAAC,cACNtD,IAAA,QAAKuD,SAAS,CAAC,uCAAuC,CAAAD,QAAA,CACnDhD,eAAe,CAAC6E,MAAM,CAAChB,CAAC,EAAIA,CAAC,CAAC1D,MAAM,GAAK,SAAS,CAAC,CAAC2E,MAAM,CACxD,CAAC,cACNlF,KAAA,MAAGqD,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAC,KAChC,CAAChD,eAAe,CAAC8E,MAAM,CAAC,iBAC7B,EAAG,CAAC,EACD,CAAC,CACH,CAAC,cAENpF,IAAA,QAAKuD,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBpD,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBpD,KAAA,QAAKqD,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCtD,IAAA,CAACX,YAAY,EAACkE,SAAS,CAAC,6BAA6B,CAAE,CAAC,cACxDvD,IAAA,OAAIuD,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,gBAEpD,CAAI,CAAC,EACF,CAAC,cACNtD,IAAA,QAAKuD,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpDd,aAAa,CAACC,sBAAsB,CAAC4C,cAAc,CAAC,CAAC,CACnD,CAAC,cACNnF,KAAA,MAAGqD,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EACjCd,aAAa,CAACE,WAAW,CAAC,gBAC7B,EAAG,CAAC,EACD,CAAC,CACH,CAAC,cAEN1C,IAAA,QAAKuD,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBpD,KAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBpD,KAAA,QAAKqD,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCtD,IAAA,CAACT,eAAe,EAACgE,SAAS,CAAC,8BAA8B,CAAE,CAAC,cAC5DvD,IAAA,OAAIuD,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,gBAEpD,CAAI,CAAC,EACF,CAAC,cACNtD,IAAA,QAAKuD,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CACrDR,aAAa,CAACV,aAAa,CAACiD,cAAc,CAAC,CAAC,CAC1C,CAAC,cACNnF,KAAA,MAAGqD,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EACjCR,aAAa,CAACD,YAAY,CAAC,gBAC9B,EAAG,CAAC,EACD,CAAC,CACH,CAAC,EACH,CACN,CAEAzC,SAAS,GAAK,UAAU,eACvBJ,IAAA,CAACN,eAAe,EACd4F,QAAQ,CAAEhF,eAAgB,CAC1BiF,cAAc,CAAG/E,MAAkB,EAAK,CACtCwD,OAAO,CAACC,GAAG,yBAAAH,MAAA,CAAyBtD,MAAM,CAAE,CAAC,CAC7CD,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKA,MAAM,CAAA4D,aAAA,CAAAA,aAAA,IAAQD,CAAC,MAAE1D,MAAM,CAAE,SAAkB,GAAK0D,CAAC,CAC9E,CAAC,CACH,CAAE,CACFqB,aAAa,CAAGhF,MAAkB,EAAK,CACrCwD,OAAO,CAACC,GAAG,yBAAAH,MAAA,CAAyBtD,MAAM,CAAE,CAAC,CAC7CD,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKA,MAAM,CAAA4D,aAAA,CAAAA,aAAA,IAAQD,CAAC,MAAE1D,MAAM,CAAE,SAAkB,GAAK0D,CAAC,CAC9E,CAAC,CACH,CAAE,CACFsB,UAAU,CAAEA,CAAA,GAAM,CAChBzB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC,CACpC1D,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAAC,aAAA,CAAAA,aAAA,IAAUD,CAAC,MAAE1D,MAAM,CAAE,SAAkB,EAAG,CACtD,CAAC,CACH,CAAE,CACFiF,SAAS,CAAEA,CAAA,GAAM,CACf1B,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC,CACpC1D,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAAC,aAAA,CAAAA,aAAA,IAAUD,CAAC,MAAE1D,MAAM,CAAE,SAAkB,EAAG,CACtD,CAAC,CACH,CAAE,CACFkF,cAAc,CAAEA,CAACnF,MAAkB,CAAES,MAAW,GAAK,CACnD+C,OAAO,CAACC,GAAG,wBAAAH,MAAA,CAAwBtD,MAAM,MAAKS,MAAM,CAAC,CACrDV,kBAAkB,CAAC2D,IAAI,EACrBA,IAAI,CAACR,GAAG,CAACS,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAKA,MAAM,CAAA4D,aAAA,CAAAA,aAAA,IAAQD,CAAC,MAAElD,MAAM,GAAKkD,CAAC,CAC1D,CAAC,CACH,CAAE,CACH,CACF,CAEA/D,SAAS,GAAK,MAAM,eACnBJ,IAAA,CAACL,YAAY,EACX2B,IAAI,CAAEA,IAAK,CACXsE,YAAY,CAAEA,CAAA,GAAM5B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE,CAClD4B,WAAW,CAAEA,CAAA,GAAM,CAAE7B,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAAE1C,OAAO,CAAC,EAAE,CAAC,CAAE,CAAE,CAClEuE,SAAS,CAAEA,CAAA,GAAM9B,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAE,CACjD,CACF,CAEA7D,SAAS,GAAK,aAAa,eAC1BJ,IAAA,CAACH,qBAAqB,EAACkG,OAAO,CAAEvD,aAAc,CAAE,CACjD,CAEApC,SAAS,GAAK,QAAQ,eACrBJ,IAAA,CAACJ,mBAAmB,EAACoG,YAAY,CAAG/E,MAAW,EAAK+C,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEhD,MAAM,CAAE,CAAE,CAC3G,CAEAb,SAAS,GAAK,MAAM,eACnBJ,IAAA,CAACF,cAAc,EACbmG,KAAK,CAAEnD,aAAc,CACrBoD,YAAY,CAAG1F,MAAmB,EAAKwD,OAAO,CAACC,GAAG,kBAAAH,MAAA,CAAkBtD,MAAM,SAAAsD,MAAA,CAAWtD,MAAM,EAAK,EAAE,CAAE,CAAE,CACtG2F,YAAY,CAAGpB,IAAU,EAAKf,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEc,IAAI,CAACvB,IAAI,CAAE,CAClF4C,aAAa,CAAGnB,OAAY,EAAKjB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEgB,OAAO,CAAE,CACxFoB,kBAAkB,CAAEA,CAAA,GAAMrC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAC9D,CACF,EACE,CAAC,EACH,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAA9D,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}