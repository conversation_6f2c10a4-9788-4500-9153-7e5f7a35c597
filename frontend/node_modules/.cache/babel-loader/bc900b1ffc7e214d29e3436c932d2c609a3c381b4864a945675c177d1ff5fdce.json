{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDecade\n * @category Decade Helpers\n * @summary Get the decade of the given date.\n *\n * @description\n * Get the decade of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the year of decade\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which decade belongs 27 November 1942?\n * const result = getDecade(new Date(1942, 10, 27))\n * //=> 1940\n */\nexport default function getDecade(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var decade = Math.floor(year / 10) * 10;\n  return decade;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "getDecade", "dirtyDate", "arguments", "date", "year", "getFullYear", "decade", "Math", "floor"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/getDecade/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDecade\n * @category Decade Helpers\n * @summary Get the decade of the given date.\n *\n * @description\n * Get the decade of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the year of decade\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which decade belongs 27 November 1942?\n * const result = getDecade(new Date(1942, 10, 27))\n * //=> 1940\n */\nexport default function getDecade(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var decade = Math.floor(year / 10) * 10;\n  return decade;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,SAAS,EAAE;EAC3CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGL,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIG,IAAI,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;EAC7B,IAAIC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,EAAE,CAAC,GAAG,EAAE;EACvC,OAAOE,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}