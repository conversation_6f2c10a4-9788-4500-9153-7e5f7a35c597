{"ast": null, "code": "var isArray = require('./isArray'),\n  isSymbol = require('./isSymbol');\n\n/** Used to match property names within property paths. */\nvar reIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,\n  reIsPlainProp = /^\\w*$/;\n\n/**\n * Checks if `value` is a property name and not a property path.\n *\n * @private\n * @param {*} value The value to check.\n * @param {Object} [object] The object to query keys on.\n * @returns {boolean} Returns `true` if `value` is a property name, else `false`.\n */\nfunction isKey(value, object) {\n  if (isArray(value)) {\n    return false;\n  }\n  var type = typeof value;\n  if (type == 'number' || type == 'symbol' || type == 'boolean' || value == null || isSymbol(value)) {\n    return true;\n  }\n  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) || object != null && value in Object(object);\n}\nmodule.exports = isKey;", "map": {"version": 3, "names": ["isArray", "require", "isSymbol", "reIsDeepProp", "reIsPlainProp", "is<PERSON>ey", "value", "object", "type", "test", "Object", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_isKey.js"], "sourcesContent": ["var isArray = require('./isArray'),\n    isSymbol = require('./isSymbol');\n\n/** Used to match property names within property paths. */\nvar reIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,\n    reIsPlainProp = /^\\w*$/;\n\n/**\n * Checks if `value` is a property name and not a property path.\n *\n * @private\n * @param {*} value The value to check.\n * @param {Object} [object] The object to query keys on.\n * @returns {boolean} Returns `true` if `value` is a property name, else `false`.\n */\nfunction isKey(value, object) {\n  if (isArray(value)) {\n    return false;\n  }\n  var type = typeof value;\n  if (type == 'number' || type == 'symbol' || type == 'boolean' ||\n      value == null || isSymbol(value)) {\n    return true;\n  }\n  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) ||\n    (object != null && value in Object(object));\n}\n\nmodule.exports = isKey;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;EAC9BC,QAAQ,GAAGD,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA,IAAIE,YAAY,GAAG,kDAAkD;EACjEC,aAAa,GAAG,OAAO;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC5B,IAAIP,OAAO,CAACM,KAAK,CAAC,EAAE;IAClB,OAAO,KAAK;EACd;EACA,IAAIE,IAAI,GAAG,OAAOF,KAAK;EACvB,IAAIE,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,SAAS,IACzDF,KAAK,IAAI,IAAI,IAAIJ,QAAQ,CAACI,KAAK,CAAC,EAAE;IACpC,OAAO,IAAI;EACb;EACA,OAAOF,aAAa,CAACK,IAAI,CAACH,KAAK,CAAC,IAAI,CAACH,YAAY,CAACM,IAAI,CAACH,KAAK,CAAC,IAC1DC,MAAM,IAAI,IAAI,IAAID,KAAK,IAAII,MAAM,CAACH,MAAM,CAAE;AAC/C;AAEAI,MAAM,CAACC,OAAO,GAAGP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}