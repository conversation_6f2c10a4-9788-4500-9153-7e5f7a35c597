{"ast": null, "code": "import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport getUTCWeek from \"../getUTCWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function setUTCWeek(dirtyDate, dirtyWeek, options) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var week = toInteger(dirtyWeek);\n  var diff = getUTCWeek(date, options) - week;\n  date.setUTCDate(date.getUTCDate() - diff * 7);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "getUTCWeek", "requiredArgs", "setUTCWeek", "dirtyDate", "dirtyWeek", "options", "arguments", "date", "week", "diff", "setUTCDate", "getUTCDate"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/_lib/setUTCWeek/index.js"], "sourcesContent": ["import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport getUTCWeek from \"../getUTCWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function setUTCWeek(dirtyDate, dirtyWeek, options) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var week = toInteger(dirtyWeek);\n  var diff = getUTCWeek(date, options) - week;\n  date.setUTCDate(date.getUTCDate() - diff * 7);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAChEJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAIK,IAAI,GAAGV,SAAS,CAACM,SAAS,CAAC;EAC/B,IAAIK,IAAI,GAAGT,UAAU,CAACO,IAAI,EAAEF,OAAO,CAAC,GAAGG,IAAI;EAC3CD,IAAI,CAACG,UAAU,CAACH,IAAI,CAACI,UAAU,CAAC,CAAC,GAAGF,IAAI,GAAG,CAAC,CAAC;EAC7C,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}