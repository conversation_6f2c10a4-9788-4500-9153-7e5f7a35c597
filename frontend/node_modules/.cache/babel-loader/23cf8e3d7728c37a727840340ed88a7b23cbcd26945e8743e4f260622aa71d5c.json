{"ast": null, "code": "import noop from \"../noop.js\";\nfunction LinearClosed(context) {\n  this._context = context;\n}\nLinearClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function () {\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._point) this._context.closePath();\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);else this._point = 1, this._context.moveTo(x, y);\n  }\n};\nexport default function (context) {\n  return new LinearClosed(context);\n}", "map": {"version": 3, "names": ["noop", "LinearClosed", "context", "_context", "prototype", "areaStart", "areaEnd", "lineStart", "_point", "lineEnd", "closePath", "point", "x", "y", "lineTo", "moveTo"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-shape/src/curve/linearClosed.js"], "sourcesContent": ["import noop from \"../noop.js\";\n\nfunction LinearClosed(context) {\n  this._context = context;\n}\n\nLinearClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._point) this._context.closePath();\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);\n    else this._point = 1, this._context.moveTo(x, y);\n  }\n};\n\nexport default function(context) {\n  return new LinearClosed(context);\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAE7B,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC7B,IAAI,CAACC,QAAQ,GAAGD,OAAO;AACzB;AAEAD,YAAY,CAACG,SAAS,GAAG;EACvBC,SAAS,EAAEL,IAAI;EACfM,OAAO,EAAEN,IAAI;EACbO,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,IAAI,CAACD,MAAM,EAAE,IAAI,CAACL,QAAQ,CAACO,SAAS,CAAC,CAAC;EAC5C,CAAC;EACDC,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IACd,IAAI,IAAI,CAACL,MAAM,EAAE,IAAI,CAACL,QAAQ,CAACW,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC,KACvC,IAAI,CAACL,MAAM,GAAG,CAAC,EAAE,IAAI,CAACL,QAAQ,CAACY,MAAM,CAACH,CAAC,EAAEC,CAAC,CAAC;EAClD;AACF,CAAC;AAED,eAAe,UAASX,OAAO,EAAE;EAC/B,OAAO,IAAID,YAAY,CAACC,OAAO,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}