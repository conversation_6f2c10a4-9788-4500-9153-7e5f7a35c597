{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isMonday\n * @category Weekday Helpers\n * @summary Is the given date Monday?\n *\n * @description\n * Is the given date Monday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Monday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 22 September 2014 Monday?\n * const result = isMonday(new Date(2014, 8, 22))\n * //=> true\n */\nexport default function isMonday(date) {\n  requiredArgs(1, arguments);\n  return toDate(date).getDay() === 1;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isMonday", "date", "arguments", "getDay"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/isMonday/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isMonday\n * @category Weekday Helpers\n * @summary Is the given date Monday?\n *\n * @description\n * Is the given date Monday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Monday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 22 September 2014 Monday?\n * const result = isMonday(new Date(2014, 8, 22))\n * //=> true\n */\nexport default function isMonday(date) {\n  requiredArgs(1, arguments);\n  return toDate(date).getDay() === 1;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAE;EACrCF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,MAAM,CAACG,IAAI,CAAC,CAACE,MAAM,CAAC,CAAC,KAAK,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}