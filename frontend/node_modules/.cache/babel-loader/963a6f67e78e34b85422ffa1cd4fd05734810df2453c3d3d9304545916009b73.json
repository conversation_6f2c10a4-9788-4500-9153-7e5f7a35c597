{"ast": null, "code": "var _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\"],\n  _excluded2 = [\"sourceX\", \"sourceY\", \"sourceControlX\", \"targetX\", \"targetY\", \"targetControlX\", \"linkWidth\"];\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @file TreemapChart\n */\nimport React, { PureComponent } from 'react';\nimport maxBy from 'lodash/maxBy';\nimport min from 'lodash/min';\nimport get from 'lodash/get';\nimport sumBy from 'lodash/sumBy';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Tooltip } from '../component/Tooltip';\nimport { Rectangle } from '../shape/Rectangle';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { filterSvgElements, validateWidthHeight, findChildByType, filterProps } from '../util/ReactUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nvar defaultCoordinateOfTooltip = {\n  x: 0,\n  y: 0\n};\nvar interpolationGenerator = function interpolationGenerator(a, b) {\n  var ka = +a;\n  var kb = b - ka;\n  return function (t) {\n    return ka + kb * t;\n  };\n};\nvar centerY = function centerY(node) {\n  return node.y + node.dy / 2;\n};\nvar getValue = function getValue(entry) {\n  return entry && entry.value || 0;\n};\nvar getSumOfIds = function getSumOfIds(links, ids) {\n  return ids.reduce(function (result, id) {\n    return result + getValue(links[id]);\n  }, 0);\n};\nvar getSumWithWeightedSource = function getSumWithWeightedSource(tree, links, ids) {\n  return ids.reduce(function (result, id) {\n    var link = links[id];\n    var sourceNode = tree[link.source];\n    return result + centerY(sourceNode) * getValue(links[id]);\n  }, 0);\n};\nvar getSumWithWeightedTarget = function getSumWithWeightedTarget(tree, links, ids) {\n  return ids.reduce(function (result, id) {\n    var link = links[id];\n    var targetNode = tree[link.target];\n    return result + centerY(targetNode) * getValue(links[id]);\n  }, 0);\n};\nvar ascendingY = function ascendingY(a, b) {\n  return a.y - b.y;\n};\nvar searchTargetsAndSources = function searchTargetsAndSources(links, id) {\n  var sourceNodes = [];\n  var sourceLinks = [];\n  var targetNodes = [];\n  var targetLinks = [];\n  for (var i = 0, len = links.length; i < len; i++) {\n    var link = links[i];\n    if (link.source === id) {\n      targetNodes.push(link.target);\n      targetLinks.push(i);\n    }\n    if (link.target === id) {\n      sourceNodes.push(link.source);\n      sourceLinks.push(i);\n    }\n  }\n  return {\n    sourceNodes: sourceNodes,\n    sourceLinks: sourceLinks,\n    targetLinks: targetLinks,\n    targetNodes: targetNodes\n  };\n};\nvar updateDepthOfTargets = function updateDepthOfTargets(tree, curNode) {\n  var targetNodes = curNode.targetNodes;\n  for (var i = 0, len = targetNodes.length; i < len; i++) {\n    var target = tree[targetNodes[i]];\n    if (target) {\n      target.depth = Math.max(curNode.depth + 1, target.depth);\n      updateDepthOfTargets(tree, target);\n    }\n  }\n};\nvar getNodesTree = function getNodesTree(_ref, width, nodeWidth) {\n  var nodes = _ref.nodes,\n    links = _ref.links;\n  var tree = nodes.map(function (entry, index) {\n    var result = searchTargetsAndSources(links, index);\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), result), {}, {\n      value: Math.max(getSumOfIds(links, result.sourceLinks), getSumOfIds(links, result.targetLinks)),\n      depth: 0\n    });\n  });\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!node.sourceNodes.length) {\n      updateDepthOfTargets(tree, node);\n    }\n  }\n  var maxDepth = maxBy(tree, function (entry) {\n    return entry.depth;\n  }).depth;\n  if (maxDepth >= 1) {\n    var childWidth = (width - nodeWidth) / maxDepth;\n    for (var _i = 0, _len = tree.length; _i < _len; _i++) {\n      var _node = tree[_i];\n      if (!_node.targetNodes.length) {\n        _node.depth = maxDepth;\n      }\n      _node.x = _node.depth * childWidth;\n      _node.dx = nodeWidth;\n    }\n  }\n  return {\n    tree: tree,\n    maxDepth: maxDepth\n  };\n};\nvar getDepthTree = function getDepthTree(tree) {\n  var result = [];\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!result[node.depth]) {\n      result[node.depth] = [];\n    }\n    result[node.depth].push(node);\n  }\n  return result;\n};\nvar updateYOfTree = function updateYOfTree(depthTree, height, nodePadding, links) {\n  var yRatio = min(depthTree.map(function (nodes) {\n    return (height - (nodes.length - 1) * nodePadding) / sumBy(nodes, getValue);\n  }));\n  for (var d = 0, maxDepth = depthTree.length; d < maxDepth; d++) {\n    for (var i = 0, len = depthTree[d].length; i < len; i++) {\n      var node = depthTree[d][i];\n      node.y = i;\n      node.dy = node.value * yRatio;\n    }\n  }\n  return links.map(function (link) {\n    return _objectSpread(_objectSpread({}, link), {}, {\n      dy: getValue(link) * yRatio\n    });\n  });\n};\nvar resolveCollisions = function resolveCollisions(depthTree, height, nodePadding) {\n  var sort = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  for (var i = 0, len = depthTree.length; i < len; i++) {\n    var nodes = depthTree[i];\n    var n = nodes.length;\n\n    // Sort by the value of y\n    if (sort) {\n      nodes.sort(ascendingY);\n    }\n    var y0 = 0;\n    for (var j = 0; j < n; j++) {\n      var node = nodes[j];\n      var dy = y0 - node.y;\n      if (dy > 0) {\n        node.y += dy;\n      }\n      y0 = node.y + node.dy + nodePadding;\n    }\n    y0 = height + nodePadding;\n    for (var _j = n - 1; _j >= 0; _j--) {\n      var _node2 = nodes[_j];\n      var _dy = _node2.y + _node2.dy + nodePadding - y0;\n      if (_dy > 0) {\n        _node2.y -= _dy;\n        y0 = _node2.y;\n      } else {\n        break;\n      }\n    }\n  }\n};\nvar relaxLeftToRight = function relaxLeftToRight(tree, depthTree, links, alpha) {\n  for (var i = 0, maxDepth = depthTree.length; i < maxDepth; i++) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.sourceLinks.length) {\n        var sourceSum = getSumOfIds(links, node.sourceLinks);\n        var weightedSum = getSumWithWeightedSource(tree, links, node.sourceLinks);\n        var y = weightedSum / sourceSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar relaxRightToLeft = function relaxRightToLeft(tree, depthTree, links, alpha) {\n  for (var i = depthTree.length - 1; i >= 0; i--) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.targetLinks.length) {\n        var targetSum = getSumOfIds(links, node.targetLinks);\n        var weightedSum = getSumWithWeightedTarget(tree, links, node.targetLinks);\n        var y = weightedSum / targetSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar updateYOfLinks = function updateYOfLinks(tree, links) {\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    var sy = 0;\n    var ty = 0;\n    node.targetLinks.sort(function (a, b) {\n      return tree[links[a].target].y - tree[links[b].target].y;\n    });\n    node.sourceLinks.sort(function (a, b) {\n      return tree[links[a].source].y - tree[links[b].source].y;\n    });\n    for (var j = 0, tLen = node.targetLinks.length; j < tLen; j++) {\n      var link = links[node.targetLinks[j]];\n      if (link) {\n        link.sy = sy;\n        sy += link.dy;\n      }\n    }\n    for (var _j2 = 0, sLen = node.sourceLinks.length; _j2 < sLen; _j2++) {\n      var _link = links[node.sourceLinks[_j2]];\n      if (_link) {\n        _link.ty = ty;\n        ty += _link.dy;\n      }\n    }\n  }\n};\nvar computeData = function computeData(_ref2) {\n  var data = _ref2.data,\n    width = _ref2.width,\n    height = _ref2.height,\n    iterations = _ref2.iterations,\n    nodeWidth = _ref2.nodeWidth,\n    nodePadding = _ref2.nodePadding,\n    sort = _ref2.sort;\n  var links = data.links;\n  var _getNodesTree = getNodesTree(data, width, nodeWidth),\n    tree = _getNodesTree.tree;\n  var depthTree = getDepthTree(tree);\n  var newLinks = updateYOfTree(depthTree, height, nodePadding, links);\n  resolveCollisions(depthTree, height, nodePadding, sort);\n  var alpha = 1;\n  for (var i = 1; i <= iterations; i++) {\n    relaxRightToLeft(tree, depthTree, newLinks, alpha *= 0.99);\n    resolveCollisions(depthTree, height, nodePadding, sort);\n    relaxLeftToRight(tree, depthTree, newLinks, alpha);\n    resolveCollisions(depthTree, height, nodePadding, sort);\n  }\n  updateYOfLinks(tree, newLinks);\n  return {\n    nodes: tree,\n    links: newLinks\n  };\n};\nvar getCoordinateOfTooltip = function getCoordinateOfTooltip(el, type) {\n  if (type === 'node') {\n    return {\n      x: el.x + el.width / 2,\n      y: el.y + el.height / 2\n    };\n  }\n  return {\n    x: (el.sourceX + el.targetX) / 2,\n    y: (el.sourceY + el.targetY) / 2\n  };\n};\nvar getPayloadOfTooltip = function getPayloadOfTooltip(el, type, nameKey) {\n  var payload = el.payload;\n  if (type === 'node') {\n    return [{\n      payload: el,\n      name: getValueByDataKey(payload, nameKey, ''),\n      value: getValueByDataKey(payload, 'value')\n    }];\n  }\n  if (payload.source && payload.target) {\n    var sourceName = getValueByDataKey(payload.source, nameKey, '');\n    var targetName = getValueByDataKey(payload.target, nameKey, '');\n    return [{\n      payload: el,\n      name: \"\".concat(sourceName, \" - \").concat(targetName),\n      value: getValueByDataKey(payload, 'value')\n    }];\n  }\n  return [];\n};\nexport var Sankey = /*#__PURE__*/function (_PureComponent) {\n  function Sankey() {\n    var _this;\n    _classCallCheck(this, Sankey);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key = 0; _key < _len2; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Sankey, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      activeElement: null,\n      activeElementType: null,\n      isTooltipActive: false,\n      nodes: [],\n      links: []\n    });\n    return _this;\n  }\n  _inherits(Sankey, _PureComponent);\n  return _createClass(Sankey, [{\n    key: \"handleMouseEnter\",\n    value: function handleMouseEnter(el, type, e) {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        children = _this$props.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState(function (prev) {\n          if (tooltipItem.props.trigger === 'hover') {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: el,\n              activeElementType: type,\n              isTooltipActive: true\n            });\n          }\n          return prev;\n        }, function () {\n          if (onMouseEnter) {\n            onMouseEnter(el, type, e);\n          }\n        });\n      } else if (onMouseEnter) {\n        onMouseEnter(el, type, e);\n      }\n    }\n  }, {\n    key: \"handleMouseLeave\",\n    value: function handleMouseLeave(el, type, e) {\n      var _this$props2 = this.props,\n        onMouseLeave = _this$props2.onMouseLeave,\n        children = _this$props2.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState(function (prev) {\n          if (tooltipItem.props.trigger === 'hover') {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: undefined,\n              activeElementType: undefined,\n              isTooltipActive: false\n            });\n          }\n          return prev;\n        }, function () {\n          if (onMouseLeave) {\n            onMouseLeave(el, type, e);\n          }\n        });\n      } else if (onMouseLeave) {\n        onMouseLeave(el, type, e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(el, type, e) {\n      var _this$props3 = this.props,\n        onClick = _this$props3.onClick,\n        children = _this$props3.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem && tooltipItem.props.trigger === 'click') {\n        if (this.state.isTooltipActive) {\n          this.setState(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: undefined,\n              activeElementType: undefined,\n              isTooltipActive: false\n            });\n          });\n        } else {\n          this.setState(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: el,\n              activeElementType: type,\n              isTooltipActive: true\n            });\n          });\n        }\n      }\n      if (onClick) onClick(el, type, e);\n    }\n  }, {\n    key: \"renderLinks\",\n    value: function renderLinks(links, nodes) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        linkCurvature = _this$props4.linkCurvature,\n        linkContent = _this$props4.link,\n        margin = _this$props4.margin;\n      var top = get(margin, 'top') || 0;\n      var left = get(margin, 'left') || 0;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-sankey-links\",\n        key: \"recharts-sankey-links\"\n      }, links.map(function (link, i) {\n        var sourceRelativeY = link.sy,\n          targetRelativeY = link.ty,\n          linkWidth = link.dy;\n        var source = nodes[link.source];\n        var target = nodes[link.target];\n        var sourceX = source.x + source.dx + left;\n        var targetX = target.x + left;\n        var interpolationFunc = interpolationGenerator(sourceX, targetX);\n        var sourceControlX = interpolationFunc(linkCurvature);\n        var targetControlX = interpolationFunc(1 - linkCurvature);\n        var sourceY = source.y + sourceRelativeY + linkWidth / 2 + top;\n        var targetY = target.y + targetRelativeY + linkWidth / 2 + top;\n        var linkProps = _objectSpread({\n          sourceX: sourceX,\n          targetX: targetX,\n          sourceY: sourceY,\n          targetY: targetY,\n          sourceControlX: sourceControlX,\n          targetControlX: targetControlX,\n          sourceRelativeY: sourceRelativeY,\n          targetRelativeY: targetRelativeY,\n          linkWidth: linkWidth,\n          index: i,\n          payload: _objectSpread(_objectSpread({}, link), {}, {\n            source: source,\n            target: target\n          })\n        }, filterProps(linkContent, false));\n        var events = {\n          onMouseEnter: _this2.handleMouseEnter.bind(_this2, linkProps, 'link'),\n          onMouseLeave: _this2.handleMouseLeave.bind(_this2, linkProps, 'link'),\n          onClick: _this2.handleClick.bind(_this2, linkProps, 'link')\n        };\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          key: \"link-\".concat(link.source, \"-\").concat(link.target, \"-\").concat(link.value)\n        }, events), _this2.constructor.renderLinkItem(linkContent, linkProps));\n      }));\n    }\n  }, {\n    key: \"renderNodes\",\n    value: function renderNodes(nodes) {\n      var _this3 = this;\n      var _this$props5 = this.props,\n        nodeContent = _this$props5.node,\n        margin = _this$props5.margin;\n      var top = get(margin, 'top') || 0;\n      var left = get(margin, 'left') || 0;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-sankey-nodes\",\n        key: \"recharts-sankey-nodes\"\n      }, nodes.map(function (node, i) {\n        var x = node.x,\n          y = node.y,\n          dx = node.dx,\n          dy = node.dy;\n        var nodeProps = _objectSpread(_objectSpread({}, filterProps(nodeContent, false)), {}, {\n          x: x + left,\n          y: y + top,\n          width: dx,\n          height: dy,\n          index: i,\n          payload: node\n        });\n        var events = {\n          onMouseEnter: _this3.handleMouseEnter.bind(_this3, nodeProps, 'node'),\n          onMouseLeave: _this3.handleMouseLeave.bind(_this3, nodeProps, 'node'),\n          onClick: _this3.handleClick.bind(_this3, nodeProps, 'node')\n        };\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          key: \"node-\".concat(node.x, \"-\").concat(node.y, \"-\").concat(node.value)\n        }, events), _this3.constructor.renderNodeItem(nodeContent, nodeProps));\n      }));\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      var _this$props6 = this.props,\n        children = _this$props6.children,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        nameKey = _this$props6.nameKey;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (!tooltipItem) {\n        return null;\n      }\n      var _this$state = this.state,\n        isTooltipActive = _this$state.isTooltipActive,\n        activeElement = _this$state.activeElement,\n        activeElementType = _this$state.activeElementType;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      };\n      var coordinate = activeElement ? getCoordinateOfTooltip(activeElement, activeElementType) : defaultCoordinateOfTooltip;\n      var payload = activeElement ? getPayloadOfTooltip(activeElement, activeElementType, nameKey) : [];\n      return /*#__PURE__*/React.cloneElement(tooltipItem, {\n        viewBox: viewBox,\n        active: isTooltipActive,\n        coordinate: coordinate,\n        label: '',\n        payload: payload\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!validateWidthHeight(this)) {\n        return null;\n      }\n      var _this$props7 = this.props,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        className = _this$props7.className,\n        style = _this$props7.style,\n        children = _this$props7.children,\n        others = _objectWithoutProperties(_this$props7, _excluded);\n      var _this$state2 = this.state,\n        links = _this$state2.links,\n        nodes = _this$state2.nodes;\n      var attrs = filterProps(others, false);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('recharts-wrapper', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n          position: 'relative',\n          cursor: 'default',\n          width: width,\n          height: height\n        }),\n        role: \"region\"\n      }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n        width: width,\n        height: height\n      }), filterSvgElements(children), this.renderLinks(links, nodes), this.renderNodes(nodes)), this.renderTooltip());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var data = nextProps.data,\n        width = nextProps.width,\n        height = nextProps.height,\n        margin = nextProps.margin,\n        iterations = nextProps.iterations,\n        nodeWidth = nextProps.nodeWidth,\n        nodePadding = nextProps.nodePadding,\n        sort = nextProps.sort;\n      if (data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || !shallowEqual(margin, prevState.prevMargin) || iterations !== prevState.prevIterations || nodeWidth !== prevState.prevNodeWidth || nodePadding !== prevState.prevNodePadding || sort !== prevState.sort) {\n        var contentWidth = width - (margin && margin.left || 0) - (margin && margin.right || 0);\n        var contentHeight = height - (margin && margin.top || 0) - (margin && margin.bottom || 0);\n        var _computeData = computeData({\n            data: data,\n            width: contentWidth,\n            height: contentHeight,\n            iterations: iterations,\n            nodeWidth: nodeWidth,\n            nodePadding: nodePadding,\n            sort: sort\n          }),\n          links = _computeData.links,\n          nodes = _computeData.nodes;\n        return _objectSpread(_objectSpread({}, prevState), {}, {\n          nodes: nodes,\n          links: links,\n          prevData: data,\n          prevWidth: iterations,\n          prevHeight: height,\n          prevMargin: margin,\n          prevNodePadding: nodePadding,\n          prevNodeWidth: nodeWidth,\n          prevIterations: iterations,\n          prevSort: sort\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderLinkItem\",\n    value: function renderLinkItem(option, props) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (isFunction(option)) {\n        return option(props);\n      }\n      var sourceX = props.sourceX,\n        sourceY = props.sourceY,\n        sourceControlX = props.sourceControlX,\n        targetX = props.targetX,\n        targetY = props.targetY,\n        targetControlX = props.targetControlX,\n        linkWidth = props.linkWidth,\n        others = _objectWithoutProperties(props, _excluded2);\n      return /*#__PURE__*/React.createElement(\"path\", _extends({\n        className: \"recharts-sankey-link\",\n        d: \"\\n          M\".concat(sourceX, \",\").concat(sourceY, \"\\n          C\").concat(sourceControlX, \",\").concat(sourceY, \" \").concat(targetControlX, \",\").concat(targetY, \" \").concat(targetX, \",\").concat(targetY, \"\\n        \"),\n        fill: \"none\",\n        stroke: \"#333\",\n        strokeWidth: linkWidth,\n        strokeOpacity: \"0.2\"\n      }, filterProps(others, false)));\n    }\n  }, {\n    key: \"renderNodeItem\",\n    value: function renderNodeItem(option, props) {\n      if (/*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (isFunction(option)) {\n        return option(props);\n      }\n      return /*#__PURE__*/React.createElement(Rectangle, _extends({\n        className: \"recharts-sankey-node\",\n        fill: \"#0088fe\",\n        fillOpacity: \"0.8\"\n      }, filterProps(props, false), {\n        role: \"img\"\n      }));\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Sankey, \"displayName\", 'Sankey');\n_defineProperty(Sankey, \"defaultProps\", {\n  nameKey: 'name',\n  dataKey: 'value',\n  nodePadding: 10,\n  nodeWidth: 10,\n  linkCurvature: 0.5,\n  iterations: 32,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  },\n  sort: true\n});", "map": {"version": 3, "names": ["_excluded", "_excluded2", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "_extends", "assign", "bind", "arguments", "apply", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "t", "e", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "ownKeys", "r", "keys", "filter", "getOwnPropertyDescriptor", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "PureComponent", "maxBy", "min", "get", "sumBy", "isFunction", "clsx", "Surface", "Layer", "<PERSON><PERSON><PERSON>", "Rectangle", "shallowEqual", "filterSvgElements", "validateWidthHeight", "findChildByType", "filterProps", "getValueByDataKey", "defaultCoordinateOfTooltip", "x", "y", "interpolationGenerator", "a", "b", "ka", "kb", "centerY", "node", "dy", "getValue", "entry", "getSumOfIds", "links", "ids", "reduce", "result", "id", "getSumWithWeightedSource", "tree", "link", "sourceNode", "getSumWithWeightedTarget", "targetNode", "ascendingY", "searchTargetsAndSources", "sourceNodes", "sourceLinks", "targetNodes", "targetLinks", "len", "updateDepthOfTargets", "curNode", "depth", "Math", "max", "getNodesTree", "_ref", "width", "nodeWidth", "nodes", "map", "index", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_i", "_len", "_node", "dx", "getDepth<PERSON>ree", "updateYOfTree", "depthTree", "height", "nodePadding", "yRatio", "d", "resolveCollisions", "sort", "undefined", "n", "y0", "j", "_j", "_node2", "_dy", "relaxLeftToRight", "alpha", "sourceSum", "weightedSum", "relaxRightToLeft", "targetSum", "updateYOfLinks", "sy", "ty", "tLen", "_j2", "sLen", "_link", "computeData", "_ref2", "data", "iterations", "_getNodesTree", "newLinks", "getCoordinateOfTooltip", "el", "type", "sourceX", "targetX", "sourceY", "targetY", "getPayloadOfTooltip", "<PERSON><PERSON><PERSON>", "payload", "name", "sourceName", "targetName", "concat", "<PERSON><PERSON>", "_PureComponent", "_this", "_len2", "args", "Array", "_key", "activeElement", "activeElementType", "isTooltipActive", "handleMouseEnter", "_this$props", "onMouseEnter", "children", "tooltipItem", "setState", "prev", "trigger", "handleMouseLeave", "_this$props2", "onMouseLeave", "handleClick", "_this$props3", "onClick", "state", "renderLinks", "_this2", "_this$props4", "linkCurvature", "linkContent", "margin", "top", "left", "createElement", "className", "sourceRelativeY", "targetRelativeY", "linkWidth", "interpolationFunc", "sourceControlX", "targetControlX", "linkProps", "events", "renderLinkItem", "renderNodes", "_this3", "_this$props5", "nodeContent", "nodeProps", "renderNodeItem", "renderTooltip", "_this$props6", "_this$state", "viewBox", "coordinate", "cloneElement", "active", "label", "render", "_this$props7", "style", "others", "_this$state2", "attrs", "position", "cursor", "role", "getDerivedStateFromProps", "nextProps", "prevState", "prevData", "prevWidth", "prevHeight", "<PERSON>v<PERSON><PERSON><PERSON>", "prevIterations", "prevNodeWidth", "prevNodePadding", "contentWidth", "right", "contentHeight", "bottom", "_computeData", "prevSort", "option", "isValidElement", "fill", "stroke", "strokeWidth", "strokeOpacity", "fillOpacity", "dataKey"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/chart/Sankey.js"], "sourcesContent": ["var _excluded = [\"width\", \"height\", \"className\", \"style\", \"children\"],\n  _excluded2 = [\"sourceX\", \"sourceY\", \"sourceControlX\", \"targetX\", \"targetY\", \"targetControlX\", \"linkWidth\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @file TreemapChart\n */\nimport React, { PureComponent } from 'react';\nimport maxBy from 'lodash/maxBy';\nimport min from 'lodash/min';\nimport get from 'lodash/get';\nimport sumBy from 'lodash/sumBy';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Surface } from '../container/Surface';\nimport { Layer } from '../container/Layer';\nimport { Tooltip } from '../component/Tooltip';\nimport { Rectangle } from '../shape/Rectangle';\nimport { shallowEqual } from '../util/ShallowEqual';\nimport { filterSvgElements, validateWidthHeight, findChildByType, filterProps } from '../util/ReactUtils';\nimport { getValueByDataKey } from '../util/ChartUtils';\nvar defaultCoordinateOfTooltip = {\n  x: 0,\n  y: 0\n};\nvar interpolationGenerator = function interpolationGenerator(a, b) {\n  var ka = +a;\n  var kb = b - ka;\n  return function (t) {\n    return ka + kb * t;\n  };\n};\nvar centerY = function centerY(node) {\n  return node.y + node.dy / 2;\n};\nvar getValue = function getValue(entry) {\n  return entry && entry.value || 0;\n};\nvar getSumOfIds = function getSumOfIds(links, ids) {\n  return ids.reduce(function (result, id) {\n    return result + getValue(links[id]);\n  }, 0);\n};\nvar getSumWithWeightedSource = function getSumWithWeightedSource(tree, links, ids) {\n  return ids.reduce(function (result, id) {\n    var link = links[id];\n    var sourceNode = tree[link.source];\n    return result + centerY(sourceNode) * getValue(links[id]);\n  }, 0);\n};\nvar getSumWithWeightedTarget = function getSumWithWeightedTarget(tree, links, ids) {\n  return ids.reduce(function (result, id) {\n    var link = links[id];\n    var targetNode = tree[link.target];\n    return result + centerY(targetNode) * getValue(links[id]);\n  }, 0);\n};\nvar ascendingY = function ascendingY(a, b) {\n  return a.y - b.y;\n};\nvar searchTargetsAndSources = function searchTargetsAndSources(links, id) {\n  var sourceNodes = [];\n  var sourceLinks = [];\n  var targetNodes = [];\n  var targetLinks = [];\n  for (var i = 0, len = links.length; i < len; i++) {\n    var link = links[i];\n    if (link.source === id) {\n      targetNodes.push(link.target);\n      targetLinks.push(i);\n    }\n    if (link.target === id) {\n      sourceNodes.push(link.source);\n      sourceLinks.push(i);\n    }\n  }\n  return {\n    sourceNodes: sourceNodes,\n    sourceLinks: sourceLinks,\n    targetLinks: targetLinks,\n    targetNodes: targetNodes\n  };\n};\nvar updateDepthOfTargets = function updateDepthOfTargets(tree, curNode) {\n  var targetNodes = curNode.targetNodes;\n  for (var i = 0, len = targetNodes.length; i < len; i++) {\n    var target = tree[targetNodes[i]];\n    if (target) {\n      target.depth = Math.max(curNode.depth + 1, target.depth);\n      updateDepthOfTargets(tree, target);\n    }\n  }\n};\nvar getNodesTree = function getNodesTree(_ref, width, nodeWidth) {\n  var nodes = _ref.nodes,\n    links = _ref.links;\n  var tree = nodes.map(function (entry, index) {\n    var result = searchTargetsAndSources(links, index);\n    return _objectSpread(_objectSpread(_objectSpread({}, entry), result), {}, {\n      value: Math.max(getSumOfIds(links, result.sourceLinks), getSumOfIds(links, result.targetLinks)),\n      depth: 0\n    });\n  });\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!node.sourceNodes.length) {\n      updateDepthOfTargets(tree, node);\n    }\n  }\n  var maxDepth = maxBy(tree, function (entry) {\n    return entry.depth;\n  }).depth;\n  if (maxDepth >= 1) {\n    var childWidth = (width - nodeWidth) / maxDepth;\n    for (var _i = 0, _len = tree.length; _i < _len; _i++) {\n      var _node = tree[_i];\n      if (!_node.targetNodes.length) {\n        _node.depth = maxDepth;\n      }\n      _node.x = _node.depth * childWidth;\n      _node.dx = nodeWidth;\n    }\n  }\n  return {\n    tree: tree,\n    maxDepth: maxDepth\n  };\n};\nvar getDepthTree = function getDepthTree(tree) {\n  var result = [];\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    if (!result[node.depth]) {\n      result[node.depth] = [];\n    }\n    result[node.depth].push(node);\n  }\n  return result;\n};\nvar updateYOfTree = function updateYOfTree(depthTree, height, nodePadding, links) {\n  var yRatio = min(depthTree.map(function (nodes) {\n    return (height - (nodes.length - 1) * nodePadding) / sumBy(nodes, getValue);\n  }));\n  for (var d = 0, maxDepth = depthTree.length; d < maxDepth; d++) {\n    for (var i = 0, len = depthTree[d].length; i < len; i++) {\n      var node = depthTree[d][i];\n      node.y = i;\n      node.dy = node.value * yRatio;\n    }\n  }\n  return links.map(function (link) {\n    return _objectSpread(_objectSpread({}, link), {}, {\n      dy: getValue(link) * yRatio\n    });\n  });\n};\nvar resolveCollisions = function resolveCollisions(depthTree, height, nodePadding) {\n  var sort = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  for (var i = 0, len = depthTree.length; i < len; i++) {\n    var nodes = depthTree[i];\n    var n = nodes.length;\n\n    // Sort by the value of y\n    if (sort) {\n      nodes.sort(ascendingY);\n    }\n    var y0 = 0;\n    for (var j = 0; j < n; j++) {\n      var node = nodes[j];\n      var dy = y0 - node.y;\n      if (dy > 0) {\n        node.y += dy;\n      }\n      y0 = node.y + node.dy + nodePadding;\n    }\n    y0 = height + nodePadding;\n    for (var _j = n - 1; _j >= 0; _j--) {\n      var _node2 = nodes[_j];\n      var _dy = _node2.y + _node2.dy + nodePadding - y0;\n      if (_dy > 0) {\n        _node2.y -= _dy;\n        y0 = _node2.y;\n      } else {\n        break;\n      }\n    }\n  }\n};\nvar relaxLeftToRight = function relaxLeftToRight(tree, depthTree, links, alpha) {\n  for (var i = 0, maxDepth = depthTree.length; i < maxDepth; i++) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.sourceLinks.length) {\n        var sourceSum = getSumOfIds(links, node.sourceLinks);\n        var weightedSum = getSumWithWeightedSource(tree, links, node.sourceLinks);\n        var y = weightedSum / sourceSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar relaxRightToLeft = function relaxRightToLeft(tree, depthTree, links, alpha) {\n  for (var i = depthTree.length - 1; i >= 0; i--) {\n    var nodes = depthTree[i];\n    for (var j = 0, len = nodes.length; j < len; j++) {\n      var node = nodes[j];\n      if (node.targetLinks.length) {\n        var targetSum = getSumOfIds(links, node.targetLinks);\n        var weightedSum = getSumWithWeightedTarget(tree, links, node.targetLinks);\n        var y = weightedSum / targetSum;\n        node.y += (y - centerY(node)) * alpha;\n      }\n    }\n  }\n};\nvar updateYOfLinks = function updateYOfLinks(tree, links) {\n  for (var i = 0, len = tree.length; i < len; i++) {\n    var node = tree[i];\n    var sy = 0;\n    var ty = 0;\n    node.targetLinks.sort(function (a, b) {\n      return tree[links[a].target].y - tree[links[b].target].y;\n    });\n    node.sourceLinks.sort(function (a, b) {\n      return tree[links[a].source].y - tree[links[b].source].y;\n    });\n    for (var j = 0, tLen = node.targetLinks.length; j < tLen; j++) {\n      var link = links[node.targetLinks[j]];\n      if (link) {\n        link.sy = sy;\n        sy += link.dy;\n      }\n    }\n    for (var _j2 = 0, sLen = node.sourceLinks.length; _j2 < sLen; _j2++) {\n      var _link = links[node.sourceLinks[_j2]];\n      if (_link) {\n        _link.ty = ty;\n        ty += _link.dy;\n      }\n    }\n  }\n};\nvar computeData = function computeData(_ref2) {\n  var data = _ref2.data,\n    width = _ref2.width,\n    height = _ref2.height,\n    iterations = _ref2.iterations,\n    nodeWidth = _ref2.nodeWidth,\n    nodePadding = _ref2.nodePadding,\n    sort = _ref2.sort;\n  var links = data.links;\n  var _getNodesTree = getNodesTree(data, width, nodeWidth),\n    tree = _getNodesTree.tree;\n  var depthTree = getDepthTree(tree);\n  var newLinks = updateYOfTree(depthTree, height, nodePadding, links);\n  resolveCollisions(depthTree, height, nodePadding, sort);\n  var alpha = 1;\n  for (var i = 1; i <= iterations; i++) {\n    relaxRightToLeft(tree, depthTree, newLinks, alpha *= 0.99);\n    resolveCollisions(depthTree, height, nodePadding, sort);\n    relaxLeftToRight(tree, depthTree, newLinks, alpha);\n    resolveCollisions(depthTree, height, nodePadding, sort);\n  }\n  updateYOfLinks(tree, newLinks);\n  return {\n    nodes: tree,\n    links: newLinks\n  };\n};\nvar getCoordinateOfTooltip = function getCoordinateOfTooltip(el, type) {\n  if (type === 'node') {\n    return {\n      x: el.x + el.width / 2,\n      y: el.y + el.height / 2\n    };\n  }\n  return {\n    x: (el.sourceX + el.targetX) / 2,\n    y: (el.sourceY + el.targetY) / 2\n  };\n};\nvar getPayloadOfTooltip = function getPayloadOfTooltip(el, type, nameKey) {\n  var payload = el.payload;\n  if (type === 'node') {\n    return [{\n      payload: el,\n      name: getValueByDataKey(payload, nameKey, ''),\n      value: getValueByDataKey(payload, 'value')\n    }];\n  }\n  if (payload.source && payload.target) {\n    var sourceName = getValueByDataKey(payload.source, nameKey, '');\n    var targetName = getValueByDataKey(payload.target, nameKey, '');\n    return [{\n      payload: el,\n      name: \"\".concat(sourceName, \" - \").concat(targetName),\n      value: getValueByDataKey(payload, 'value')\n    }];\n  }\n  return [];\n};\nexport var Sankey = /*#__PURE__*/function (_PureComponent) {\n  function Sankey() {\n    var _this;\n    _classCallCheck(this, Sankey);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key = 0; _key < _len2; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Sankey, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      activeElement: null,\n      activeElementType: null,\n      isTooltipActive: false,\n      nodes: [],\n      links: []\n    });\n    return _this;\n  }\n  _inherits(Sankey, _PureComponent);\n  return _createClass(Sankey, [{\n    key: \"handleMouseEnter\",\n    value: function handleMouseEnter(el, type, e) {\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        children = _this$props.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState(function (prev) {\n          if (tooltipItem.props.trigger === 'hover') {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: el,\n              activeElementType: type,\n              isTooltipActive: true\n            });\n          }\n          return prev;\n        }, function () {\n          if (onMouseEnter) {\n            onMouseEnter(el, type, e);\n          }\n        });\n      } else if (onMouseEnter) {\n        onMouseEnter(el, type, e);\n      }\n    }\n  }, {\n    key: \"handleMouseLeave\",\n    value: function handleMouseLeave(el, type, e) {\n      var _this$props2 = this.props,\n        onMouseLeave = _this$props2.onMouseLeave,\n        children = _this$props2.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem) {\n        this.setState(function (prev) {\n          if (tooltipItem.props.trigger === 'hover') {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: undefined,\n              activeElementType: undefined,\n              isTooltipActive: false\n            });\n          }\n          return prev;\n        }, function () {\n          if (onMouseLeave) {\n            onMouseLeave(el, type, e);\n          }\n        });\n      } else if (onMouseLeave) {\n        onMouseLeave(el, type, e);\n      }\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(el, type, e) {\n      var _this$props3 = this.props,\n        onClick = _this$props3.onClick,\n        children = _this$props3.children;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (tooltipItem && tooltipItem.props.trigger === 'click') {\n        if (this.state.isTooltipActive) {\n          this.setState(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: undefined,\n              activeElementType: undefined,\n              isTooltipActive: false\n            });\n          });\n        } else {\n          this.setState(function (prev) {\n            return _objectSpread(_objectSpread({}, prev), {}, {\n              activeElement: el,\n              activeElementType: type,\n              isTooltipActive: true\n            });\n          });\n        }\n      }\n      if (onClick) onClick(el, type, e);\n    }\n  }, {\n    key: \"renderLinks\",\n    value: function renderLinks(links, nodes) {\n      var _this2 = this;\n      var _this$props4 = this.props,\n        linkCurvature = _this$props4.linkCurvature,\n        linkContent = _this$props4.link,\n        margin = _this$props4.margin;\n      var top = get(margin, 'top') || 0;\n      var left = get(margin, 'left') || 0;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-sankey-links\",\n        key: \"recharts-sankey-links\"\n      }, links.map(function (link, i) {\n        var sourceRelativeY = link.sy,\n          targetRelativeY = link.ty,\n          linkWidth = link.dy;\n        var source = nodes[link.source];\n        var target = nodes[link.target];\n        var sourceX = source.x + source.dx + left;\n        var targetX = target.x + left;\n        var interpolationFunc = interpolationGenerator(sourceX, targetX);\n        var sourceControlX = interpolationFunc(linkCurvature);\n        var targetControlX = interpolationFunc(1 - linkCurvature);\n        var sourceY = source.y + sourceRelativeY + linkWidth / 2 + top;\n        var targetY = target.y + targetRelativeY + linkWidth / 2 + top;\n        var linkProps = _objectSpread({\n          sourceX: sourceX,\n          targetX: targetX,\n          sourceY: sourceY,\n          targetY: targetY,\n          sourceControlX: sourceControlX,\n          targetControlX: targetControlX,\n          sourceRelativeY: sourceRelativeY,\n          targetRelativeY: targetRelativeY,\n          linkWidth: linkWidth,\n          index: i,\n          payload: _objectSpread(_objectSpread({}, link), {}, {\n            source: source,\n            target: target\n          })\n        }, filterProps(linkContent, false));\n        var events = {\n          onMouseEnter: _this2.handleMouseEnter.bind(_this2, linkProps, 'link'),\n          onMouseLeave: _this2.handleMouseLeave.bind(_this2, linkProps, 'link'),\n          onClick: _this2.handleClick.bind(_this2, linkProps, 'link')\n        };\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          key: \"link-\".concat(link.source, \"-\").concat(link.target, \"-\").concat(link.value)\n        }, events), _this2.constructor.renderLinkItem(linkContent, linkProps));\n      }));\n    }\n  }, {\n    key: \"renderNodes\",\n    value: function renderNodes(nodes) {\n      var _this3 = this;\n      var _this$props5 = this.props,\n        nodeContent = _this$props5.node,\n        margin = _this$props5.margin;\n      var top = get(margin, 'top') || 0;\n      var left = get(margin, 'left') || 0;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-sankey-nodes\",\n        key: \"recharts-sankey-nodes\"\n      }, nodes.map(function (node, i) {\n        var x = node.x,\n          y = node.y,\n          dx = node.dx,\n          dy = node.dy;\n        var nodeProps = _objectSpread(_objectSpread({}, filterProps(nodeContent, false)), {}, {\n          x: x + left,\n          y: y + top,\n          width: dx,\n          height: dy,\n          index: i,\n          payload: node\n        });\n        var events = {\n          onMouseEnter: _this3.handleMouseEnter.bind(_this3, nodeProps, 'node'),\n          onMouseLeave: _this3.handleMouseLeave.bind(_this3, nodeProps, 'node'),\n          onClick: _this3.handleClick.bind(_this3, nodeProps, 'node')\n        };\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          key: \"node-\".concat(node.x, \"-\").concat(node.y, \"-\").concat(node.value)\n        }, events), _this3.constructor.renderNodeItem(nodeContent, nodeProps));\n      }));\n    }\n  }, {\n    key: \"renderTooltip\",\n    value: function renderTooltip() {\n      var _this$props6 = this.props,\n        children = _this$props6.children,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        nameKey = _this$props6.nameKey;\n      var tooltipItem = findChildByType(children, Tooltip);\n      if (!tooltipItem) {\n        return null;\n      }\n      var _this$state = this.state,\n        isTooltipActive = _this$state.isTooltipActive,\n        activeElement = _this$state.activeElement,\n        activeElementType = _this$state.activeElementType;\n      var viewBox = {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      };\n      var coordinate = activeElement ? getCoordinateOfTooltip(activeElement, activeElementType) : defaultCoordinateOfTooltip;\n      var payload = activeElement ? getPayloadOfTooltip(activeElement, activeElementType, nameKey) : [];\n      return /*#__PURE__*/React.cloneElement(tooltipItem, {\n        viewBox: viewBox,\n        active: isTooltipActive,\n        coordinate: coordinate,\n        label: '',\n        payload: payload\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (!validateWidthHeight(this)) {\n        return null;\n      }\n      var _this$props7 = this.props,\n        width = _this$props7.width,\n        height = _this$props7.height,\n        className = _this$props7.className,\n        style = _this$props7.style,\n        children = _this$props7.children,\n        others = _objectWithoutProperties(_this$props7, _excluded);\n      var _this$state2 = this.state,\n        links = _this$state2.links,\n        nodes = _this$state2.nodes;\n      var attrs = filterProps(others, false);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: clsx('recharts-wrapper', className),\n        style: _objectSpread(_objectSpread({}, style), {}, {\n          position: 'relative',\n          cursor: 'default',\n          width: width,\n          height: height\n        }),\n        role: \"region\"\n      }, /*#__PURE__*/React.createElement(Surface, _extends({}, attrs, {\n        width: width,\n        height: height\n      }), filterSvgElements(children), this.renderLinks(links, nodes), this.renderNodes(nodes)), this.renderTooltip());\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      var data = nextProps.data,\n        width = nextProps.width,\n        height = nextProps.height,\n        margin = nextProps.margin,\n        iterations = nextProps.iterations,\n        nodeWidth = nextProps.nodeWidth,\n        nodePadding = nextProps.nodePadding,\n        sort = nextProps.sort;\n      if (data !== prevState.prevData || width !== prevState.prevWidth || height !== prevState.prevHeight || !shallowEqual(margin, prevState.prevMargin) || iterations !== prevState.prevIterations || nodeWidth !== prevState.prevNodeWidth || nodePadding !== prevState.prevNodePadding || sort !== prevState.sort) {\n        var contentWidth = width - (margin && margin.left || 0) - (margin && margin.right || 0);\n        var contentHeight = height - (margin && margin.top || 0) - (margin && margin.bottom || 0);\n        var _computeData = computeData({\n            data: data,\n            width: contentWidth,\n            height: contentHeight,\n            iterations: iterations,\n            nodeWidth: nodeWidth,\n            nodePadding: nodePadding,\n            sort: sort\n          }),\n          links = _computeData.links,\n          nodes = _computeData.nodes;\n        return _objectSpread(_objectSpread({}, prevState), {}, {\n          nodes: nodes,\n          links: links,\n          prevData: data,\n          prevWidth: iterations,\n          prevHeight: height,\n          prevMargin: margin,\n          prevNodePadding: nodePadding,\n          prevNodeWidth: nodeWidth,\n          prevIterations: iterations,\n          prevSort: sort\n        });\n      }\n      return null;\n    }\n  }, {\n    key: \"renderLinkItem\",\n    value: function renderLinkItem(option, props) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (isFunction(option)) {\n        return option(props);\n      }\n      var sourceX = props.sourceX,\n        sourceY = props.sourceY,\n        sourceControlX = props.sourceControlX,\n        targetX = props.targetX,\n        targetY = props.targetY,\n        targetControlX = props.targetControlX,\n        linkWidth = props.linkWidth,\n        others = _objectWithoutProperties(props, _excluded2);\n      return /*#__PURE__*/React.createElement(\"path\", _extends({\n        className: \"recharts-sankey-link\",\n        d: \"\\n          M\".concat(sourceX, \",\").concat(sourceY, \"\\n          C\").concat(sourceControlX, \",\").concat(sourceY, \" \").concat(targetControlX, \",\").concat(targetY, \" \").concat(targetX, \",\").concat(targetY, \"\\n        \"),\n        fill: \"none\",\n        stroke: \"#333\",\n        strokeWidth: linkWidth,\n        strokeOpacity: \"0.2\"\n      }, filterProps(others, false)));\n    }\n  }, {\n    key: \"renderNodeItem\",\n    value: function renderNodeItem(option, props) {\n      if ( /*#__PURE__*/React.isValidElement(option)) {\n        return /*#__PURE__*/React.cloneElement(option, props);\n      }\n      if (isFunction(option)) {\n        return option(props);\n      }\n      return /*#__PURE__*/React.createElement(Rectangle, _extends({\n        className: \"recharts-sankey-node\",\n        fill: \"#0088fe\",\n        fillOpacity: \"0.8\"\n      }, filterProps(props, false), {\n        role: \"img\"\n      }));\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Sankey, \"displayName\", 'Sankey');\n_defineProperty(Sankey, \"defaultProps\", {\n  nameKey: 'name',\n  dataKey: 'value',\n  nodePadding: 10,\n  nodeWidth: 10,\n  linkCurvature: 0.5,\n  iterations: 32,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  },\n  sort: true\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC;EACnEC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,CAAC;AAC5G,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIE,GAAG,IAAIJ,MAAM,EAAE;IAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAE,IAAIH,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AACtR,SAASY,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGR,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUd,MAAM,EAAE;IAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACR,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAE,IAAIL,MAAM,GAAGiB,SAAS,CAACZ,CAAC,CAAC;MAAE,KAAK,IAAID,GAAG,IAAIJ,MAAM,EAAE;QAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;UAAEF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOF,MAAM;EAAE,CAAC;EAAE,OAAOY,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAAE;AAClV,SAASE,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACrB,MAAM,EAAEsB,KAAK,EAAE;EAAE,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,KAAK,CAACf,MAAM,EAAEJ,CAAC,EAAE,EAAE;IAAE,IAAIoB,UAAU,GAAGD,KAAK,CAACnB,CAAC,CAAC;IAAEoB,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEtB,MAAM,CAACuB,cAAc,CAAC3B,MAAM,EAAE4B,cAAc,CAACL,UAAU,CAACrB,GAAG,CAAC,EAAEqB,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASM,YAAYA,CAACV,WAAW,EAAEW,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAET,iBAAiB,CAACF,WAAW,CAACvB,SAAS,EAAEkC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEV,iBAAiB,CAACF,WAAW,EAAEY,WAAW,CAAC;EAAE3B,MAAM,CAACuB,cAAc,CAACR,WAAW,EAAE,WAAW,EAAE;IAAEO,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOP,WAAW;AAAE;AAC5R,SAASa,UAAUA,CAACC,CAAC,EAAEzC,CAAC,EAAE0C,CAAC,EAAE;EAAE,OAAO1C,CAAC,GAAG2C,eAAe,CAAC3C,CAAC,CAAC,EAAE4C,0BAA0B,CAACH,CAAC,EAAEI,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAC/C,CAAC,EAAE0C,CAAC,IAAI,EAAE,EAAEC,eAAe,CAACF,CAAC,CAAC,CAACtC,WAAW,CAAC,GAAGH,CAAC,CAACwB,KAAK,CAACiB,CAAC,EAAEC,CAAC,CAAC,CAAC;AAAE;AAC1M,SAASE,0BAA0BA,CAACI,IAAI,EAAE9B,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKnB,OAAO,CAACmB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIU,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOqB,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAIJ,CAAC,GAAG,CAACU,OAAO,CAAC/C,SAAS,CAACgD,OAAO,CAAClC,IAAI,CAAC4B,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOV,CAAC,EAAE,CAAC;EAAE,OAAO,CAACI,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAACJ,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAASE,eAAeA,CAAC3C,CAAC,EAAE;EAAE2C,eAAe,GAAG/B,MAAM,CAACyC,cAAc,GAAGzC,MAAM,CAAC0C,cAAc,CAAChC,IAAI,CAAC,CAAC,GAAG,SAASqB,eAAeA,CAAC3C,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACuD,SAAS,IAAI3C,MAAM,CAAC0C,cAAc,CAACtD,CAAC,CAAC;EAAE,CAAC;EAAE,OAAO2C,eAAe,CAAC3C,CAAC,CAAC;AAAE;AACnN,SAASwD,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI9B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAE6B,QAAQ,CAACrD,SAAS,GAAGQ,MAAM,CAAC+C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACtD,SAAS,EAAE;IAAED,WAAW,EAAE;MAAEyD,KAAK,EAAEH,QAAQ;MAAEvB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAErB,MAAM,CAACuB,cAAc,CAACsB,QAAQ,EAAE,WAAW,EAAE;IAAEvB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIwB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAAC7D,CAAC,EAAE8D,CAAC,EAAE;EAAED,eAAe,GAAGjD,MAAM,CAACyC,cAAc,GAAGzC,MAAM,CAACyC,cAAc,CAAC/B,IAAI,CAAC,CAAC,GAAG,SAASuC,eAAeA,CAAC7D,CAAC,EAAE8D,CAAC,EAAE;IAAE9D,CAAC,CAACuD,SAAS,GAAGO,CAAC;IAAE,OAAO9D,CAAC;EAAE,CAAC;EAAE,OAAO6D,eAAe,CAAC7D,CAAC,EAAE8D,CAAC,CAAC;AAAE;AACvM,SAASC,OAAOA,CAACrB,CAAC,EAAEsB,CAAC,EAAE;EAAE,IAAIvB,CAAC,GAAG7B,MAAM,CAACqD,IAAI,CAACvB,CAAC,CAAC;EAAE,IAAI9B,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIb,CAAC,GAAGY,MAAM,CAACC,qBAAqB,CAAC6B,CAAC,CAAC;IAAEsB,CAAC,KAAKhE,CAAC,GAAGA,CAAC,CAACkE,MAAM,CAAC,UAAUF,CAAC,EAAE;MAAE,OAAOpD,MAAM,CAACuD,wBAAwB,CAACzB,CAAC,EAAEsB,CAAC,CAAC,CAAChC,UAAU;IAAE,CAAC,CAAC,CAAC,EAAES,CAAC,CAAC2B,IAAI,CAAC5C,KAAK,CAACiB,CAAC,EAAEzC,CAAC,CAAC;EAAE;EAAE,OAAOyC,CAAC;AAAE;AAC9P,SAAS4B,aAAaA,CAAC3B,CAAC,EAAE;EAAE,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,SAAS,CAACR,MAAM,EAAEiD,CAAC,EAAE,EAAE;IAAE,IAAIvB,CAAC,GAAG,IAAI,IAAIlB,SAAS,CAACyC,CAAC,CAAC,GAAGzC,SAAS,CAACyC,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGD,OAAO,CAACnD,MAAM,CAAC6B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC6B,OAAO,CAAC,UAAUN,CAAC,EAAE;MAAEO,eAAe,CAAC7B,CAAC,EAAEsB,CAAC,EAAEvB,CAAC,CAACuB,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGpD,MAAM,CAAC4D,yBAAyB,GAAG5D,MAAM,CAAC6D,gBAAgB,CAAC/B,CAAC,EAAE9B,MAAM,CAAC4D,yBAAyB,CAAC/B,CAAC,CAAC,CAAC,GAAGsB,OAAO,CAACnD,MAAM,CAAC6B,CAAC,CAAC,CAAC,CAAC6B,OAAO,CAAC,UAAUN,CAAC,EAAE;MAAEpD,MAAM,CAACuB,cAAc,CAACO,CAAC,EAAEsB,CAAC,EAAEpD,MAAM,CAACuD,wBAAwB,CAAC1B,CAAC,EAAEuB,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOtB,CAAC;AAAE;AACtb,SAAS6B,eAAeA,CAACG,GAAG,EAAEhE,GAAG,EAAEkD,KAAK,EAAE;EAAElD,GAAG,GAAG0B,cAAc,CAAC1B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIgE,GAAG,EAAE;IAAE9D,MAAM,CAACuB,cAAc,CAACuC,GAAG,EAAEhE,GAAG,EAAE;MAAEkD,KAAK,EAAEA,KAAK;MAAE5B,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEwC,GAAG,CAAChE,GAAG,CAAC,GAAGkD,KAAK;EAAE;EAAE,OAAOc,GAAG;AAAE;AAC3O,SAAStC,cAAcA,CAACK,CAAC,EAAE;EAAE,IAAI9B,CAAC,GAAGgE,YAAY,CAAClC,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI1C,OAAO,CAACY,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASgE,YAAYA,CAAClC,CAAC,EAAEuB,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIjE,OAAO,CAAC0C,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIC,CAAC,GAAGD,CAAC,CAACxC,MAAM,CAAC2E,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlC,CAAC,EAAE;IAAE,IAAI/B,CAAC,GAAG+B,CAAC,CAACxB,IAAI,CAACuB,CAAC,EAAEuB,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIjE,OAAO,CAACY,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIiB,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKoC,CAAC,GAAGa,MAAM,GAAGC,MAAM,EAAErC,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAOsC,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,iBAAiB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,WAAW,QAAQ,oBAAoB;AACzG,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,IAAIC,0BAA0B,GAAG;EAC/BC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AACD,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjE,IAAIC,EAAE,GAAG,CAACF,CAAC;EACX,IAAIG,EAAE,GAAGF,CAAC,GAAGC,EAAE;EACf,OAAO,UAAU9D,CAAC,EAAE;IAClB,OAAO8D,EAAE,GAAGC,EAAE,GAAG/D,CAAC;EACpB,CAAC;AACH,CAAC;AACD,IAAIgE,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EACnC,OAAOA,IAAI,CAACP,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAG,CAAC;AAC7B,CAAC;AACD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EACtC,OAAOA,KAAK,IAAIA,KAAK,CAACjD,KAAK,IAAI,CAAC;AAClC,CAAC;AACD,IAAIkD,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAOA,GAAG,CAACC,MAAM,CAAC,UAAUC,MAAM,EAAEC,EAAE,EAAE;IACtC,OAAOD,MAAM,GAAGN,QAAQ,CAACG,KAAK,CAACI,EAAE,CAAC,CAAC;EACrC,CAAC,EAAE,CAAC,CAAC;AACP,CAAC;AACD,IAAIC,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,IAAI,EAAEN,KAAK,EAAEC,GAAG,EAAE;EACjF,OAAOA,GAAG,CAACC,MAAM,CAAC,UAAUC,MAAM,EAAEC,EAAE,EAAE;IACtC,IAAIG,IAAI,GAAGP,KAAK,CAACI,EAAE,CAAC;IACpB,IAAII,UAAU,GAAGF,IAAI,CAACC,IAAI,CAAChH,MAAM,CAAC;IAClC,OAAO4G,MAAM,GAAGT,OAAO,CAACc,UAAU,CAAC,GAAGX,QAAQ,CAACG,KAAK,CAACI,EAAE,CAAC,CAAC;EAC3D,CAAC,EAAE,CAAC,CAAC;AACP,CAAC;AACD,IAAIK,wBAAwB,GAAG,SAASA,wBAAwBA,CAACH,IAAI,EAAEN,KAAK,EAAEC,GAAG,EAAE;EACjF,OAAOA,GAAG,CAACC,MAAM,CAAC,UAAUC,MAAM,EAAEC,EAAE,EAAE;IACtC,IAAIG,IAAI,GAAGP,KAAK,CAACI,EAAE,CAAC;IACpB,IAAIM,UAAU,GAAGJ,IAAI,CAACC,IAAI,CAAC9G,MAAM,CAAC;IAClC,OAAO0G,MAAM,GAAGT,OAAO,CAACgB,UAAU,CAAC,GAAGb,QAAQ,CAACG,KAAK,CAACI,EAAE,CAAC,CAAC;EAC3D,CAAC,EAAE,CAAC,CAAC;AACP,CAAC;AACD,IAAIO,UAAU,GAAG,SAASA,UAAUA,CAACrB,CAAC,EAAEC,CAAC,EAAE;EACzC,OAAOD,CAAC,CAACF,CAAC,GAAGG,CAAC,CAACH,CAAC;AAClB,CAAC;AACD,IAAIwB,uBAAuB,GAAG,SAASA,uBAAuBA,CAACZ,KAAK,EAAEI,EAAE,EAAE;EACxE,IAAIS,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEqH,GAAG,GAAGjB,KAAK,CAAChG,MAAM,EAAEJ,CAAC,GAAGqH,GAAG,EAAErH,CAAC,EAAE,EAAE;IAChD,IAAI2G,IAAI,GAAGP,KAAK,CAACpG,CAAC,CAAC;IACnB,IAAI2G,IAAI,CAAChH,MAAM,KAAK6G,EAAE,EAAE;MACtBW,WAAW,CAAC1D,IAAI,CAACkD,IAAI,CAAC9G,MAAM,CAAC;MAC7BuH,WAAW,CAAC3D,IAAI,CAACzD,CAAC,CAAC;IACrB;IACA,IAAI2G,IAAI,CAAC9G,MAAM,KAAK2G,EAAE,EAAE;MACtBS,WAAW,CAACxD,IAAI,CAACkD,IAAI,CAAChH,MAAM,CAAC;MAC7BuH,WAAW,CAACzD,IAAI,CAACzD,CAAC,CAAC;IACrB;EACF;EACA,OAAO;IACLiH,WAAW,EAAEA,WAAW;IACxBC,WAAW,EAAEA,WAAW;IACxBE,WAAW,EAAEA,WAAW;IACxBD,WAAW,EAAEA;EACf,CAAC;AACH,CAAC;AACD,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAACZ,IAAI,EAAEa,OAAO,EAAE;EACtE,IAAIJ,WAAW,GAAGI,OAAO,CAACJ,WAAW;EACrC,KAAK,IAAInH,CAAC,GAAG,CAAC,EAAEqH,GAAG,GAAGF,WAAW,CAAC/G,MAAM,EAAEJ,CAAC,GAAGqH,GAAG,EAAErH,CAAC,EAAE,EAAE;IACtD,IAAIH,MAAM,GAAG6G,IAAI,CAACS,WAAW,CAACnH,CAAC,CAAC,CAAC;IACjC,IAAIH,MAAM,EAAE;MACVA,MAAM,CAAC2H,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACH,OAAO,CAACC,KAAK,GAAG,CAAC,EAAE3H,MAAM,CAAC2H,KAAK,CAAC;MACxDF,oBAAoB,CAACZ,IAAI,EAAE7G,MAAM,CAAC;IACpC;EACF;AACF,CAAC;AACD,IAAI8H,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC/D,IAAIC,KAAK,GAAGH,IAAI,CAACG,KAAK;IACpB3B,KAAK,GAAGwB,IAAI,CAACxB,KAAK;EACpB,IAAIM,IAAI,GAAGqB,KAAK,CAACC,GAAG,CAAC,UAAU9B,KAAK,EAAE+B,KAAK,EAAE;IAC3C,IAAI1B,MAAM,GAAGS,uBAAuB,CAACZ,KAAK,EAAE6B,KAAK,CAAC;IAClD,OAAOvE,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwC,KAAK,CAAC,EAAEK,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACxEtD,KAAK,EAAEwE,IAAI,CAACC,GAAG,CAACvB,WAAW,CAACC,KAAK,EAAEG,MAAM,CAACW,WAAW,CAAC,EAAEf,WAAW,CAACC,KAAK,EAAEG,MAAM,CAACa,WAAW,CAAC,CAAC;MAC/FI,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEqH,GAAG,GAAGX,IAAI,CAACtG,MAAM,EAAEJ,CAAC,GAAGqH,GAAG,EAAErH,CAAC,EAAE,EAAE;IAC/C,IAAI+F,IAAI,GAAGW,IAAI,CAAC1G,CAAC,CAAC;IAClB,IAAI,CAAC+F,IAAI,CAACkB,WAAW,CAAC7G,MAAM,EAAE;MAC5BkH,oBAAoB,CAACZ,IAAI,EAAEX,IAAI,CAAC;IAClC;EACF;EACA,IAAImC,QAAQ,GAAG5D,KAAK,CAACoC,IAAI,EAAE,UAAUR,KAAK,EAAE;IAC1C,OAAOA,KAAK,CAACsB,KAAK;EACpB,CAAC,CAAC,CAACA,KAAK;EACR,IAAIU,QAAQ,IAAI,CAAC,EAAE;IACjB,IAAIC,UAAU,GAAG,CAACN,KAAK,GAAGC,SAAS,IAAII,QAAQ;IAC/C,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEC,IAAI,GAAG3B,IAAI,CAACtG,MAAM,EAAEgI,EAAE,GAAGC,IAAI,EAAED,EAAE,EAAE,EAAE;MACpD,IAAIE,KAAK,GAAG5B,IAAI,CAAC0B,EAAE,CAAC;MACpB,IAAI,CAACE,KAAK,CAACnB,WAAW,CAAC/G,MAAM,EAAE;QAC7BkI,KAAK,CAACd,KAAK,GAAGU,QAAQ;MACxB;MACAI,KAAK,CAAC/C,CAAC,GAAG+C,KAAK,CAACd,KAAK,GAAGW,UAAU;MAClCG,KAAK,CAACC,EAAE,GAAGT,SAAS;IACtB;EACF;EACA,OAAO;IACLpB,IAAI,EAAEA,IAAI;IACVwB,QAAQ,EAAEA;EACZ,CAAC;AACH,CAAC;AACD,IAAIM,YAAY,GAAG,SAASA,YAAYA,CAAC9B,IAAI,EAAE;EAC7C,IAAIH,MAAM,GAAG,EAAE;EACf,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEqH,GAAG,GAAGX,IAAI,CAACtG,MAAM,EAAEJ,CAAC,GAAGqH,GAAG,EAAErH,CAAC,EAAE,EAAE;IAC/C,IAAI+F,IAAI,GAAGW,IAAI,CAAC1G,CAAC,CAAC;IAClB,IAAI,CAACuG,MAAM,CAACR,IAAI,CAACyB,KAAK,CAAC,EAAE;MACvBjB,MAAM,CAACR,IAAI,CAACyB,KAAK,CAAC,GAAG,EAAE;IACzB;IACAjB,MAAM,CAACR,IAAI,CAACyB,KAAK,CAAC,CAAC/D,IAAI,CAACsC,IAAI,CAAC;EAC/B;EACA,OAAOQ,MAAM;AACf,CAAC;AACD,IAAIkC,aAAa,GAAG,SAASA,aAAaA,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAExC,KAAK,EAAE;EAChF,IAAIyC,MAAM,GAAGtE,GAAG,CAACmE,SAAS,CAACV,GAAG,CAAC,UAAUD,KAAK,EAAE;IAC9C,OAAO,CAACY,MAAM,GAAG,CAACZ,KAAK,CAAC3H,MAAM,GAAG,CAAC,IAAIwI,WAAW,IAAInE,KAAK,CAACsD,KAAK,EAAE9B,QAAQ,CAAC;EAC7E,CAAC,CAAC,CAAC;EACH,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEZ,QAAQ,GAAGQ,SAAS,CAACtI,MAAM,EAAE0I,CAAC,GAAGZ,QAAQ,EAAEY,CAAC,EAAE,EAAE;IAC9D,KAAK,IAAI9I,CAAC,GAAG,CAAC,EAAEqH,GAAG,GAAGqB,SAAS,CAACI,CAAC,CAAC,CAAC1I,MAAM,EAAEJ,CAAC,GAAGqH,GAAG,EAAErH,CAAC,EAAE,EAAE;MACvD,IAAI+F,IAAI,GAAG2C,SAAS,CAACI,CAAC,CAAC,CAAC9I,CAAC,CAAC;MAC1B+F,IAAI,CAACP,CAAC,GAAGxF,CAAC;MACV+F,IAAI,CAACC,EAAE,GAAGD,IAAI,CAAC9C,KAAK,GAAG4F,MAAM;IAC/B;EACF;EACA,OAAOzC,KAAK,CAAC4B,GAAG,CAAC,UAAUrB,IAAI,EAAE;IAC/B,OAAOjD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAChDX,EAAE,EAAEC,QAAQ,CAACU,IAAI,CAAC,GAAGkC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,IAAIE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACL,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAE;EACjF,IAAII,IAAI,GAAGpI,SAAS,CAACR,MAAM,GAAG,CAAC,IAAIQ,SAAS,CAAC,CAAC,CAAC,KAAKqI,SAAS,GAAGrI,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACnF,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEqH,GAAG,GAAGqB,SAAS,CAACtI,MAAM,EAAEJ,CAAC,GAAGqH,GAAG,EAAErH,CAAC,EAAE,EAAE;IACpD,IAAI+H,KAAK,GAAGW,SAAS,CAAC1I,CAAC,CAAC;IACxB,IAAIkJ,CAAC,GAAGnB,KAAK,CAAC3H,MAAM;;IAEpB;IACA,IAAI4I,IAAI,EAAE;MACRjB,KAAK,CAACiB,IAAI,CAACjC,UAAU,CAAC;IACxB;IACA,IAAIoC,EAAE,GAAG,CAAC;IACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;MAC1B,IAAIrD,IAAI,GAAGgC,KAAK,CAACqB,CAAC,CAAC;MACnB,IAAIpD,EAAE,GAAGmD,EAAE,GAAGpD,IAAI,CAACP,CAAC;MACpB,IAAIQ,EAAE,GAAG,CAAC,EAAE;QACVD,IAAI,CAACP,CAAC,IAAIQ,EAAE;MACd;MACAmD,EAAE,GAAGpD,IAAI,CAACP,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAG4C,WAAW;IACrC;IACAO,EAAE,GAAGR,MAAM,GAAGC,WAAW;IACzB,KAAK,IAAIS,EAAE,GAAGH,CAAC,GAAG,CAAC,EAAEG,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;MAClC,IAAIC,MAAM,GAAGvB,KAAK,CAACsB,EAAE,CAAC;MACtB,IAAIE,GAAG,GAAGD,MAAM,CAAC9D,CAAC,GAAG8D,MAAM,CAACtD,EAAE,GAAG4C,WAAW,GAAGO,EAAE;MACjD,IAAII,GAAG,GAAG,CAAC,EAAE;QACXD,MAAM,CAAC9D,CAAC,IAAI+D,GAAG;QACfJ,EAAE,GAAGG,MAAM,CAAC9D,CAAC;MACf,CAAC,MAAM;QACL;MACF;IACF;EACF;AACF,CAAC;AACD,IAAIgE,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC9C,IAAI,EAAEgC,SAAS,EAAEtC,KAAK,EAAEqD,KAAK,EAAE;EAC9E,KAAK,IAAIzJ,CAAC,GAAG,CAAC,EAAEkI,QAAQ,GAAGQ,SAAS,CAACtI,MAAM,EAAEJ,CAAC,GAAGkI,QAAQ,EAAElI,CAAC,EAAE,EAAE;IAC9D,IAAI+H,KAAK,GAAGW,SAAS,CAAC1I,CAAC,CAAC;IACxB,KAAK,IAAIoJ,CAAC,GAAG,CAAC,EAAE/B,GAAG,GAAGU,KAAK,CAAC3H,MAAM,EAAEgJ,CAAC,GAAG/B,GAAG,EAAE+B,CAAC,EAAE,EAAE;MAChD,IAAIrD,IAAI,GAAGgC,KAAK,CAACqB,CAAC,CAAC;MACnB,IAAIrD,IAAI,CAACmB,WAAW,CAAC9G,MAAM,EAAE;QAC3B,IAAIsJ,SAAS,GAAGvD,WAAW,CAACC,KAAK,EAAEL,IAAI,CAACmB,WAAW,CAAC;QACpD,IAAIyC,WAAW,GAAGlD,wBAAwB,CAACC,IAAI,EAAEN,KAAK,EAAEL,IAAI,CAACmB,WAAW,CAAC;QACzE,IAAI1B,CAAC,GAAGmE,WAAW,GAAGD,SAAS;QAC/B3D,IAAI,CAACP,CAAC,IAAI,CAACA,CAAC,GAAGM,OAAO,CAACC,IAAI,CAAC,IAAI0D,KAAK;MACvC;IACF;EACF;AACF,CAAC;AACD,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAAClD,IAAI,EAAEgC,SAAS,EAAEtC,KAAK,EAAEqD,KAAK,EAAE;EAC9E,KAAK,IAAIzJ,CAAC,GAAG0I,SAAS,CAACtI,MAAM,GAAG,CAAC,EAAEJ,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9C,IAAI+H,KAAK,GAAGW,SAAS,CAAC1I,CAAC,CAAC;IACxB,KAAK,IAAIoJ,CAAC,GAAG,CAAC,EAAE/B,GAAG,GAAGU,KAAK,CAAC3H,MAAM,EAAEgJ,CAAC,GAAG/B,GAAG,EAAE+B,CAAC,EAAE,EAAE;MAChD,IAAIrD,IAAI,GAAGgC,KAAK,CAACqB,CAAC,CAAC;MACnB,IAAIrD,IAAI,CAACqB,WAAW,CAAChH,MAAM,EAAE;QAC3B,IAAIyJ,SAAS,GAAG1D,WAAW,CAACC,KAAK,EAAEL,IAAI,CAACqB,WAAW,CAAC;QACpD,IAAIuC,WAAW,GAAG9C,wBAAwB,CAACH,IAAI,EAAEN,KAAK,EAAEL,IAAI,CAACqB,WAAW,CAAC;QACzE,IAAI5B,CAAC,GAAGmE,WAAW,GAAGE,SAAS;QAC/B9D,IAAI,CAACP,CAAC,IAAI,CAACA,CAAC,GAAGM,OAAO,CAACC,IAAI,CAAC,IAAI0D,KAAK;MACvC;IACF;EACF;AACF,CAAC;AACD,IAAIK,cAAc,GAAG,SAASA,cAAcA,CAACpD,IAAI,EAAEN,KAAK,EAAE;EACxD,KAAK,IAAIpG,CAAC,GAAG,CAAC,EAAEqH,GAAG,GAAGX,IAAI,CAACtG,MAAM,EAAEJ,CAAC,GAAGqH,GAAG,EAAErH,CAAC,EAAE,EAAE;IAC/C,IAAI+F,IAAI,GAAGW,IAAI,CAAC1G,CAAC,CAAC;IAClB,IAAI+J,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACVjE,IAAI,CAACqB,WAAW,CAAC4B,IAAI,CAAC,UAAUtD,CAAC,EAAEC,CAAC,EAAE;MACpC,OAAOe,IAAI,CAACN,KAAK,CAACV,CAAC,CAAC,CAAC7F,MAAM,CAAC,CAAC2F,CAAC,GAAGkB,IAAI,CAACN,KAAK,CAACT,CAAC,CAAC,CAAC9F,MAAM,CAAC,CAAC2F,CAAC;IAC1D,CAAC,CAAC;IACFO,IAAI,CAACmB,WAAW,CAAC8B,IAAI,CAAC,UAAUtD,CAAC,EAAEC,CAAC,EAAE;MACpC,OAAOe,IAAI,CAACN,KAAK,CAACV,CAAC,CAAC,CAAC/F,MAAM,CAAC,CAAC6F,CAAC,GAAGkB,IAAI,CAACN,KAAK,CAACT,CAAC,CAAC,CAAChG,MAAM,CAAC,CAAC6F,CAAC;IAC1D,CAAC,CAAC;IACF,KAAK,IAAI4D,CAAC,GAAG,CAAC,EAAEa,IAAI,GAAGlE,IAAI,CAACqB,WAAW,CAAChH,MAAM,EAAEgJ,CAAC,GAAGa,IAAI,EAAEb,CAAC,EAAE,EAAE;MAC7D,IAAIzC,IAAI,GAAGP,KAAK,CAACL,IAAI,CAACqB,WAAW,CAACgC,CAAC,CAAC,CAAC;MACrC,IAAIzC,IAAI,EAAE;QACRA,IAAI,CAACoD,EAAE,GAAGA,EAAE;QACZA,EAAE,IAAIpD,IAAI,CAACX,EAAE;MACf;IACF;IACA,KAAK,IAAIkE,GAAG,GAAG,CAAC,EAAEC,IAAI,GAAGpE,IAAI,CAACmB,WAAW,CAAC9G,MAAM,EAAE8J,GAAG,GAAGC,IAAI,EAAED,GAAG,EAAE,EAAE;MACnE,IAAIE,KAAK,GAAGhE,KAAK,CAACL,IAAI,CAACmB,WAAW,CAACgD,GAAG,CAAC,CAAC;MACxC,IAAIE,KAAK,EAAE;QACTA,KAAK,CAACJ,EAAE,GAAGA,EAAE;QACbA,EAAE,IAAII,KAAK,CAACpE,EAAE;MAChB;IACF;EACF;AACF,CAAC;AACD,IAAIqE,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;IACnB1C,KAAK,GAAGyC,KAAK,CAACzC,KAAK;IACnBc,MAAM,GAAG2B,KAAK,CAAC3B,MAAM;IACrB6B,UAAU,GAAGF,KAAK,CAACE,UAAU;IAC7B1C,SAAS,GAAGwC,KAAK,CAACxC,SAAS;IAC3Bc,WAAW,GAAG0B,KAAK,CAAC1B,WAAW;IAC/BI,IAAI,GAAGsB,KAAK,CAACtB,IAAI;EACnB,IAAI5C,KAAK,GAAGmE,IAAI,CAACnE,KAAK;EACtB,IAAIqE,aAAa,GAAG9C,YAAY,CAAC4C,IAAI,EAAE1C,KAAK,EAAEC,SAAS,CAAC;IACtDpB,IAAI,GAAG+D,aAAa,CAAC/D,IAAI;EAC3B,IAAIgC,SAAS,GAAGF,YAAY,CAAC9B,IAAI,CAAC;EAClC,IAAIgE,QAAQ,GAAGjC,aAAa,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAExC,KAAK,CAAC;EACnE2C,iBAAiB,CAACL,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEI,IAAI,CAAC;EACvD,IAAIS,KAAK,GAAG,CAAC;EACb,KAAK,IAAIzJ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIwK,UAAU,EAAExK,CAAC,EAAE,EAAE;IACpC4J,gBAAgB,CAAClD,IAAI,EAAEgC,SAAS,EAAEgC,QAAQ,EAAEjB,KAAK,IAAI,IAAI,CAAC;IAC1DV,iBAAiB,CAACL,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEI,IAAI,CAAC;IACvDQ,gBAAgB,CAAC9C,IAAI,EAAEgC,SAAS,EAAEgC,QAAQ,EAAEjB,KAAK,CAAC;IAClDV,iBAAiB,CAACL,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEI,IAAI,CAAC;EACzD;EACAc,cAAc,CAACpD,IAAI,EAAEgE,QAAQ,CAAC;EAC9B,OAAO;IACL3C,KAAK,EAAErB,IAAI;IACXN,KAAK,EAAEsE;EACT,CAAC;AACH,CAAC;AACD,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACrE,IAAIA,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO;MACLtF,CAAC,EAAEqF,EAAE,CAACrF,CAAC,GAAGqF,EAAE,CAAC/C,KAAK,GAAG,CAAC;MACtBrC,CAAC,EAAEoF,EAAE,CAACpF,CAAC,GAAGoF,EAAE,CAACjC,MAAM,GAAG;IACxB,CAAC;EACH;EACA,OAAO;IACLpD,CAAC,EAAE,CAACqF,EAAE,CAACE,OAAO,GAAGF,EAAE,CAACG,OAAO,IAAI,CAAC;IAChCvF,CAAC,EAAE,CAACoF,EAAE,CAACI,OAAO,GAAGJ,EAAE,CAACK,OAAO,IAAI;EACjC,CAAC;AACH,CAAC;AACD,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACN,EAAE,EAAEC,IAAI,EAAEM,OAAO,EAAE;EACxE,IAAIC,OAAO,GAAGR,EAAE,CAACQ,OAAO;EACxB,IAAIP,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO,CAAC;MACNO,OAAO,EAAER,EAAE;MACXS,IAAI,EAAEhG,iBAAiB,CAAC+F,OAAO,EAAED,OAAO,EAAE,EAAE,CAAC;MAC7ClI,KAAK,EAAEoC,iBAAiB,CAAC+F,OAAO,EAAE,OAAO;IAC3C,CAAC,CAAC;EACJ;EACA,IAAIA,OAAO,CAACzL,MAAM,IAAIyL,OAAO,CAACvL,MAAM,EAAE;IACpC,IAAIyL,UAAU,GAAGjG,iBAAiB,CAAC+F,OAAO,CAACzL,MAAM,EAAEwL,OAAO,EAAE,EAAE,CAAC;IAC/D,IAAII,UAAU,GAAGlG,iBAAiB,CAAC+F,OAAO,CAACvL,MAAM,EAAEsL,OAAO,EAAE,EAAE,CAAC;IAC/D,OAAO,CAAC;MACNC,OAAO,EAAER,EAAE;MACXS,IAAI,EAAE,EAAE,CAACG,MAAM,CAACF,UAAU,EAAE,KAAK,CAAC,CAACE,MAAM,CAACD,UAAU,CAAC;MACrDtI,KAAK,EAAEoC,iBAAiB,CAAC+F,OAAO,EAAE,OAAO;IAC3C,CAAC,CAAC;EACJ;EACA,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIK,MAAM,GAAG,aAAa,UAAUC,cAAc,EAAE;EACzD,SAASD,MAAMA,CAAA,EAAG;IAChB,IAAIE,KAAK;IACT7K,eAAe,CAAC,IAAI,EAAE2K,MAAM,CAAC;IAC7B,KAAK,IAAIG,KAAK,GAAGhL,SAAS,CAACR,MAAM,EAAEyL,IAAI,GAAG,IAAIC,KAAK,CAACF,KAAK,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,KAAK,EAAEG,IAAI,EAAE,EAAE;MAC1FF,IAAI,CAACE,IAAI,CAAC,GAAGnL,SAAS,CAACmL,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAG9J,UAAU,CAAC,IAAI,EAAE4J,MAAM,EAAE,EAAE,CAACD,MAAM,CAACK,IAAI,CAAC,CAAC;IACjDjI,eAAe,CAAC+H,KAAK,EAAE,OAAO,EAAE;MAC9BK,aAAa,EAAE,IAAI;MACnBC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,KAAK;MACtBnE,KAAK,EAAE,EAAE;MACT3B,KAAK,EAAE;IACT,CAAC,CAAC;IACF,OAAOuF,KAAK;EACd;EACA9I,SAAS,CAAC4I,MAAM,EAAEC,cAAc,CAAC;EACjC,OAAOhK,YAAY,CAAC+J,MAAM,EAAE,CAAC;IAC3B1L,GAAG,EAAE,kBAAkB;IACvBkD,KAAK,EAAE,SAASkJ,gBAAgBA,CAACvB,EAAE,EAAEC,IAAI,EAAE9I,CAAC,EAAE;MAC5C,IAAIqK,WAAW,GAAG,IAAI,CAACjL,KAAK;QAC1BkL,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;MACjC,IAAIC,WAAW,GAAGpH,eAAe,CAACmH,QAAQ,EAAExH,OAAO,CAAC;MACpD,IAAIyH,WAAW,EAAE;QACf,IAAI,CAACC,QAAQ,CAAC,UAAUC,IAAI,EAAE;UAC5B,IAAIF,WAAW,CAACpL,KAAK,CAACuL,OAAO,KAAK,OAAO,EAAE;YACzC,OAAOhJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cAChDT,aAAa,EAAEpB,EAAE;cACjBqB,iBAAiB,EAAEpB,IAAI;cACvBqB,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ;UACA,OAAOO,IAAI;QACb,CAAC,EAAE,YAAY;UACb,IAAIJ,YAAY,EAAE;YAChBA,YAAY,CAACzB,EAAE,EAAEC,IAAI,EAAE9I,CAAC,CAAC;UAC3B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIsK,YAAY,EAAE;QACvBA,YAAY,CAACzB,EAAE,EAAEC,IAAI,EAAE9I,CAAC,CAAC;MAC3B;IACF;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,kBAAkB;IACvBkD,KAAK,EAAE,SAAS0J,gBAAgBA,CAAC/B,EAAE,EAAEC,IAAI,EAAE9I,CAAC,EAAE;MAC5C,IAAI6K,YAAY,GAAG,IAAI,CAACzL,KAAK;QAC3B0L,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxCP,QAAQ,GAAGM,YAAY,CAACN,QAAQ;MAClC,IAAIC,WAAW,GAAGpH,eAAe,CAACmH,QAAQ,EAAExH,OAAO,CAAC;MACpD,IAAIyH,WAAW,EAAE;QACf,IAAI,CAACC,QAAQ,CAAC,UAAUC,IAAI,EAAE;UAC5B,IAAIF,WAAW,CAACpL,KAAK,CAACuL,OAAO,KAAK,OAAO,EAAE;YACzC,OAAOhJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cAChDT,aAAa,EAAE/C,SAAS;cACxBgD,iBAAiB,EAAEhD,SAAS;cAC5BiD,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ;UACA,OAAOO,IAAI;QACb,CAAC,EAAE,YAAY;UACb,IAAII,YAAY,EAAE;YAChBA,YAAY,CAACjC,EAAE,EAAEC,IAAI,EAAE9I,CAAC,CAAC;UAC3B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI8K,YAAY,EAAE;QACvBA,YAAY,CAACjC,EAAE,EAAEC,IAAI,EAAE9I,CAAC,CAAC;MAC3B;IACF;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,aAAa;IAClBkD,KAAK,EAAE,SAAS6J,WAAWA,CAAClC,EAAE,EAAEC,IAAI,EAAE9I,CAAC,EAAE;MACvC,IAAIgL,YAAY,GAAG,IAAI,CAAC5L,KAAK;QAC3B6L,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BV,QAAQ,GAAGS,YAAY,CAACT,QAAQ;MAClC,IAAIC,WAAW,GAAGpH,eAAe,CAACmH,QAAQ,EAAExH,OAAO,CAAC;MACpD,IAAIyH,WAAW,IAAIA,WAAW,CAACpL,KAAK,CAACuL,OAAO,KAAK,OAAO,EAAE;QACxD,IAAI,IAAI,CAACO,KAAK,CAACf,eAAe,EAAE;UAC9B,IAAI,CAACM,QAAQ,CAAC,UAAUC,IAAI,EAAE;YAC5B,OAAO/I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cAChDT,aAAa,EAAE/C,SAAS;cACxBgD,iBAAiB,EAAEhD,SAAS;cAC5BiD,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACM,QAAQ,CAAC,UAAUC,IAAI,EAAE;YAC5B,OAAO/I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cAChDT,aAAa,EAAEpB,EAAE;cACjBqB,iBAAiB,EAAEpB,IAAI;cACvBqB,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;MACF;MACA,IAAIc,OAAO,EAAEA,OAAO,CAACpC,EAAE,EAAEC,IAAI,EAAE9I,CAAC,CAAC;IACnC;EACF,CAAC,EAAE;IACDhC,GAAG,EAAE,aAAa;IAClBkD,KAAK,EAAE,SAASiK,WAAWA,CAAC9G,KAAK,EAAE2B,KAAK,EAAE;MACxC,IAAIoF,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACjM,KAAK;QAC3BkM,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CC,WAAW,GAAGF,YAAY,CAACzG,IAAI;QAC/B4G,MAAM,GAAGH,YAAY,CAACG,MAAM;MAC9B,IAAIC,GAAG,GAAGhJ,GAAG,CAAC+I,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC;MACjC,IAAIE,IAAI,GAAGjJ,GAAG,CAAC+I,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;MACnC,OAAO,aAAanJ,KAAK,CAACsJ,aAAa,CAAC7I,KAAK,EAAE;QAC7C8I,SAAS,EAAE,uBAAuB;QAClC5N,GAAG,EAAE;MACP,CAAC,EAAEqG,KAAK,CAAC4B,GAAG,CAAC,UAAUrB,IAAI,EAAE3G,CAAC,EAAE;QAC9B,IAAI4N,eAAe,GAAGjH,IAAI,CAACoD,EAAE;UAC3B8D,eAAe,GAAGlH,IAAI,CAACqD,EAAE;UACzB8D,SAAS,GAAGnH,IAAI,CAACX,EAAE;QACrB,IAAIrG,MAAM,GAAGoI,KAAK,CAACpB,IAAI,CAAChH,MAAM,CAAC;QAC/B,IAAIE,MAAM,GAAGkI,KAAK,CAACpB,IAAI,CAAC9G,MAAM,CAAC;QAC/B,IAAIiL,OAAO,GAAGnL,MAAM,CAAC4F,CAAC,GAAG5F,MAAM,CAAC4I,EAAE,GAAGkF,IAAI;QACzC,IAAI1C,OAAO,GAAGlL,MAAM,CAAC0F,CAAC,GAAGkI,IAAI;QAC7B,IAAIM,iBAAiB,GAAGtI,sBAAsB,CAACqF,OAAO,EAAEC,OAAO,CAAC;QAChE,IAAIiD,cAAc,GAAGD,iBAAiB,CAACV,aAAa,CAAC;QACrD,IAAIY,cAAc,GAAGF,iBAAiB,CAAC,CAAC,GAAGV,aAAa,CAAC;QACzD,IAAIrC,OAAO,GAAGrL,MAAM,CAAC6F,CAAC,GAAGoI,eAAe,GAAGE,SAAS,GAAG,CAAC,GAAGN,GAAG;QAC9D,IAAIvC,OAAO,GAAGpL,MAAM,CAAC2F,CAAC,GAAGqI,eAAe,GAAGC,SAAS,GAAG,CAAC,GAAGN,GAAG;QAC9D,IAAIU,SAAS,GAAGxK,aAAa,CAAC;UAC5BoH,OAAO,EAAEA,OAAO;UAChBC,OAAO,EAAEA,OAAO;UAChBC,OAAO,EAAEA,OAAO;UAChBC,OAAO,EAAEA,OAAO;UAChB+C,cAAc,EAAEA,cAAc;UAC9BC,cAAc,EAAEA,cAAc;UAC9BL,eAAe,EAAEA,eAAe;UAChCC,eAAe,EAAEA,eAAe;UAChCC,SAAS,EAAEA,SAAS;UACpB7F,KAAK,EAAEjI,CAAC;UACRoL,OAAO,EAAE1H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;YAClDhH,MAAM,EAAEA,MAAM;YACdE,MAAM,EAAEA;UACV,CAAC;QACH,CAAC,EAAEuF,WAAW,CAACkI,WAAW,EAAE,KAAK,CAAC,CAAC;QACnC,IAAIa,MAAM,GAAG;UACX9B,YAAY,EAAEc,MAAM,CAAChB,gBAAgB,CAACxL,IAAI,CAACwM,MAAM,EAAEe,SAAS,EAAE,MAAM,CAAC;UACrErB,YAAY,EAAEM,MAAM,CAACR,gBAAgB,CAAChM,IAAI,CAACwM,MAAM,EAAEe,SAAS,EAAE,MAAM,CAAC;UACrElB,OAAO,EAAEG,MAAM,CAACL,WAAW,CAACnM,IAAI,CAACwM,MAAM,EAAEe,SAAS,EAAE,MAAM;QAC5D,CAAC;QACD,OAAO,aAAa9J,KAAK,CAACsJ,aAAa,CAAC7I,KAAK,EAAEpE,QAAQ,CAAC;UACtDV,GAAG,EAAE,OAAO,CAACyL,MAAM,CAAC7E,IAAI,CAAChH,MAAM,EAAE,GAAG,CAAC,CAAC6L,MAAM,CAAC7E,IAAI,CAAC9G,MAAM,EAAE,GAAG,CAAC,CAAC2L,MAAM,CAAC7E,IAAI,CAAC1D,KAAK;QAClF,CAAC,EAAEkL,MAAM,CAAC,EAAEhB,MAAM,CAAC3N,WAAW,CAAC4O,cAAc,CAACd,WAAW,EAAEY,SAAS,CAAC,CAAC;MACxE,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDnO,GAAG,EAAE,aAAa;IAClBkD,KAAK,EAAE,SAASoL,WAAWA,CAACtG,KAAK,EAAE;MACjC,IAAIuG,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACpN,KAAK;QAC3BqN,WAAW,GAAGD,YAAY,CAACxI,IAAI;QAC/BwH,MAAM,GAAGgB,YAAY,CAAChB,MAAM;MAC9B,IAAIC,GAAG,GAAGhJ,GAAG,CAAC+I,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC;MACjC,IAAIE,IAAI,GAAGjJ,GAAG,CAAC+I,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;MACnC,OAAO,aAAanJ,KAAK,CAACsJ,aAAa,CAAC7I,KAAK,EAAE;QAC7C8I,SAAS,EAAE,uBAAuB;QAClC5N,GAAG,EAAE;MACP,CAAC,EAAEgI,KAAK,CAACC,GAAG,CAAC,UAAUjC,IAAI,EAAE/F,CAAC,EAAE;QAC9B,IAAIuF,CAAC,GAAGQ,IAAI,CAACR,CAAC;UACZC,CAAC,GAAGO,IAAI,CAACP,CAAC;UACV+C,EAAE,GAAGxC,IAAI,CAACwC,EAAE;UACZvC,EAAE,GAAGD,IAAI,CAACC,EAAE;QACd,IAAIyI,SAAS,GAAG/K,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,WAAW,CAACoJ,WAAW,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACpFjJ,CAAC,EAAEA,CAAC,GAAGkI,IAAI;UACXjI,CAAC,EAAEA,CAAC,GAAGgI,GAAG;UACV3F,KAAK,EAAEU,EAAE;UACTI,MAAM,EAAE3C,EAAE;UACViC,KAAK,EAAEjI,CAAC;UACRoL,OAAO,EAAErF;QACX,CAAC,CAAC;QACF,IAAIoI,MAAM,GAAG;UACX9B,YAAY,EAAEiC,MAAM,CAACnC,gBAAgB,CAACxL,IAAI,CAAC2N,MAAM,EAAEG,SAAS,EAAE,MAAM,CAAC;UACrE5B,YAAY,EAAEyB,MAAM,CAAC3B,gBAAgB,CAAChM,IAAI,CAAC2N,MAAM,EAAEG,SAAS,EAAE,MAAM,CAAC;UACrEzB,OAAO,EAAEsB,MAAM,CAACxB,WAAW,CAACnM,IAAI,CAAC2N,MAAM,EAAEG,SAAS,EAAE,MAAM;QAC5D,CAAC;QACD,OAAO,aAAarK,KAAK,CAACsJ,aAAa,CAAC7I,KAAK,EAAEpE,QAAQ,CAAC;UACtDV,GAAG,EAAE,OAAO,CAACyL,MAAM,CAACzF,IAAI,CAACR,CAAC,EAAE,GAAG,CAAC,CAACiG,MAAM,CAACzF,IAAI,CAACP,CAAC,EAAE,GAAG,CAAC,CAACgG,MAAM,CAACzF,IAAI,CAAC9C,KAAK;QACxE,CAAC,EAAEkL,MAAM,CAAC,EAAEG,MAAM,CAAC9O,WAAW,CAACkP,cAAc,CAACF,WAAW,EAAEC,SAAS,CAAC,CAAC;MACxE,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACD1O,GAAG,EAAE,eAAe;IACpBkD,KAAK,EAAE,SAAS0L,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAACzN,KAAK;QAC3BmL,QAAQ,GAAGsC,YAAY,CAACtC,QAAQ;QAChCzE,KAAK,GAAG+G,YAAY,CAAC/G,KAAK;QAC1Bc,MAAM,GAAGiG,YAAY,CAACjG,MAAM;QAC5BwC,OAAO,GAAGyD,YAAY,CAACzD,OAAO;MAChC,IAAIoB,WAAW,GAAGpH,eAAe,CAACmH,QAAQ,EAAExH,OAAO,CAAC;MACpD,IAAI,CAACyH,WAAW,EAAE;QAChB,OAAO,IAAI;MACb;MACA,IAAIsC,WAAW,GAAG,IAAI,CAAC5B,KAAK;QAC1Bf,eAAe,GAAG2C,WAAW,CAAC3C,eAAe;QAC7CF,aAAa,GAAG6C,WAAW,CAAC7C,aAAa;QACzCC,iBAAiB,GAAG4C,WAAW,CAAC5C,iBAAiB;MACnD,IAAI6C,OAAO,GAAG;QACZvJ,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJqC,KAAK,EAAEA,KAAK;QACZc,MAAM,EAAEA;MACV,CAAC;MACD,IAAIoG,UAAU,GAAG/C,aAAa,GAAGrB,sBAAsB,CAACqB,aAAa,EAAEC,iBAAiB,CAAC,GAAG3G,0BAA0B;MACtH,IAAI8F,OAAO,GAAGY,aAAa,GAAGd,mBAAmB,CAACc,aAAa,EAAEC,iBAAiB,EAAEd,OAAO,CAAC,GAAG,EAAE;MACjG,OAAO,aAAa/G,KAAK,CAAC4K,YAAY,CAACzC,WAAW,EAAE;QAClDuC,OAAO,EAAEA,OAAO;QAChBG,MAAM,EAAE/C,eAAe;QACvB6C,UAAU,EAAEA,UAAU;QACtBG,KAAK,EAAE,EAAE;QACT9D,OAAO,EAAEA;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrL,GAAG,EAAE,QAAQ;IACbkD,KAAK,EAAE,SAASkM,MAAMA,CAAA,EAAG;MACvB,IAAI,CAACjK,mBAAmB,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,IAAI;MACb;MACA,IAAIkK,YAAY,GAAG,IAAI,CAACjO,KAAK;QAC3B0G,KAAK,GAAGuH,YAAY,CAACvH,KAAK;QAC1Bc,MAAM,GAAGyG,YAAY,CAACzG,MAAM;QAC5BgF,SAAS,GAAGyB,YAAY,CAACzB,SAAS;QAClC0B,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1B/C,QAAQ,GAAG8C,YAAY,CAAC9C,QAAQ;QAChCgD,MAAM,GAAG5P,wBAAwB,CAAC0P,YAAY,EAAElQ,SAAS,CAAC;MAC5D,IAAIqQ,YAAY,GAAG,IAAI,CAACtC,KAAK;QAC3B7G,KAAK,GAAGmJ,YAAY,CAACnJ,KAAK;QAC1B2B,KAAK,GAAGwH,YAAY,CAACxH,KAAK;MAC5B,IAAIyH,KAAK,GAAGpK,WAAW,CAACkK,MAAM,EAAE,KAAK,CAAC;MACtC,OAAO,aAAalL,KAAK,CAACsJ,aAAa,CAAC,KAAK,EAAE;QAC7CC,SAAS,EAAEhJ,IAAI,CAAC,kBAAkB,EAAEgJ,SAAS,CAAC;QAC9C0B,KAAK,EAAE3L,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2L,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjDI,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAE,SAAS;UACjB7H,KAAK,EAAEA,KAAK;UACZc,MAAM,EAAEA;QACV,CAAC,CAAC;QACFgH,IAAI,EAAE;MACR,CAAC,EAAE,aAAavL,KAAK,CAACsJ,aAAa,CAAC9I,OAAO,EAAEnE,QAAQ,CAAC,CAAC,CAAC,EAAE+O,KAAK,EAAE;QAC/D3H,KAAK,EAAEA,KAAK;QACZc,MAAM,EAAEA;MACV,CAAC,CAAC,EAAE1D,iBAAiB,CAACqH,QAAQ,CAAC,EAAE,IAAI,CAACY,WAAW,CAAC9G,KAAK,EAAE2B,KAAK,CAAC,EAAE,IAAI,CAACsG,WAAW,CAACtG,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC4G,aAAa,CAAC,CAAC,CAAC;IAClH;EACF,CAAC,CAAC,EAAE,CAAC;IACH5O,GAAG,EAAE,0BAA0B;IAC/BkD,KAAK,EAAE,SAAS2M,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAIvF,IAAI,GAAGsF,SAAS,CAACtF,IAAI;QACvB1C,KAAK,GAAGgI,SAAS,CAAChI,KAAK;QACvBc,MAAM,GAAGkH,SAAS,CAAClH,MAAM;QACzB4E,MAAM,GAAGsC,SAAS,CAACtC,MAAM;QACzB/C,UAAU,GAAGqF,SAAS,CAACrF,UAAU;QACjC1C,SAAS,GAAG+H,SAAS,CAAC/H,SAAS;QAC/Bc,WAAW,GAAGiH,SAAS,CAACjH,WAAW;QACnCI,IAAI,GAAG6G,SAAS,CAAC7G,IAAI;MACvB,IAAIuB,IAAI,KAAKuF,SAAS,CAACC,QAAQ,IAAIlI,KAAK,KAAKiI,SAAS,CAACE,SAAS,IAAIrH,MAAM,KAAKmH,SAAS,CAACG,UAAU,IAAI,CAACjL,YAAY,CAACuI,MAAM,EAAEuC,SAAS,CAACI,UAAU,CAAC,IAAI1F,UAAU,KAAKsF,SAAS,CAACK,cAAc,IAAIrI,SAAS,KAAKgI,SAAS,CAACM,aAAa,IAAIxH,WAAW,KAAKkH,SAAS,CAACO,eAAe,IAAIrH,IAAI,KAAK8G,SAAS,CAAC9G,IAAI,EAAE;QAC9S,IAAIsH,YAAY,GAAGzI,KAAK,IAAI0F,MAAM,IAAIA,MAAM,CAACE,IAAI,IAAI,CAAC,CAAC,IAAIF,MAAM,IAAIA,MAAM,CAACgD,KAAK,IAAI,CAAC,CAAC;QACvF,IAAIC,aAAa,GAAG7H,MAAM,IAAI4E,MAAM,IAAIA,MAAM,CAACC,GAAG,IAAI,CAAC,CAAC,IAAID,MAAM,IAAIA,MAAM,CAACkD,MAAM,IAAI,CAAC,CAAC;QACzF,IAAIC,YAAY,GAAGrG,WAAW,CAAC;YAC3BE,IAAI,EAAEA,IAAI;YACV1C,KAAK,EAAEyI,YAAY;YACnB3H,MAAM,EAAE6H,aAAa;YACrBhG,UAAU,EAAEA,UAAU;YACtB1C,SAAS,EAAEA,SAAS;YACpBc,WAAW,EAAEA,WAAW;YACxBI,IAAI,EAAEA;UACR,CAAC,CAAC;UACF5C,KAAK,GAAGsK,YAAY,CAACtK,KAAK;UAC1B2B,KAAK,GAAG2I,YAAY,CAAC3I,KAAK;QAC5B,OAAOrE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoM,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACrD/H,KAAK,EAAEA,KAAK;UACZ3B,KAAK,EAAEA,KAAK;UACZ2J,QAAQ,EAAExF,IAAI;UACdyF,SAAS,EAAExF,UAAU;UACrByF,UAAU,EAAEtH,MAAM;UAClBuH,UAAU,EAAE3C,MAAM;UAClB8C,eAAe,EAAEzH,WAAW;UAC5BwH,aAAa,EAAEtI,SAAS;UACxBqI,cAAc,EAAE3F,UAAU;UAC1BmG,QAAQ,EAAE3H;QACZ,CAAC,CAAC;MACJ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDjJ,GAAG,EAAE,gBAAgB;IACrBkD,KAAK,EAAE,SAASmL,cAAcA,CAACwC,MAAM,EAAEzP,KAAK,EAAE;MAC5C,IAAK,aAAaiD,KAAK,CAACyM,cAAc,CAACD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAaxM,KAAK,CAAC4K,YAAY,CAAC4B,MAAM,EAAEzP,KAAK,CAAC;MACvD;MACA,IAAIuD,UAAU,CAACkM,MAAM,CAAC,EAAE;QACtB,OAAOA,MAAM,CAACzP,KAAK,CAAC;MACtB;MACA,IAAI2J,OAAO,GAAG3J,KAAK,CAAC2J,OAAO;QACzBE,OAAO,GAAG7J,KAAK,CAAC6J,OAAO;QACvBgD,cAAc,GAAG7M,KAAK,CAAC6M,cAAc;QACrCjD,OAAO,GAAG5J,KAAK,CAAC4J,OAAO;QACvBE,OAAO,GAAG9J,KAAK,CAAC8J,OAAO;QACvBgD,cAAc,GAAG9M,KAAK,CAAC8M,cAAc;QACrCH,SAAS,GAAG3M,KAAK,CAAC2M,SAAS;QAC3BwB,MAAM,GAAG5P,wBAAwB,CAACyB,KAAK,EAAEhC,UAAU,CAAC;MACtD,OAAO,aAAaiF,KAAK,CAACsJ,aAAa,CAAC,MAAM,EAAEjN,QAAQ,CAAC;QACvDkN,SAAS,EAAE,sBAAsB;QACjC7E,CAAC,EAAE,eAAe,CAAC0C,MAAM,CAACV,OAAO,EAAE,GAAG,CAAC,CAACU,MAAM,CAACR,OAAO,EAAE,eAAe,CAAC,CAACQ,MAAM,CAACwC,cAAc,EAAE,GAAG,CAAC,CAACxC,MAAM,CAACR,OAAO,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACyC,cAAc,EAAE,GAAG,CAAC,CAACzC,MAAM,CAACP,OAAO,EAAE,GAAG,CAAC,CAACO,MAAM,CAACT,OAAO,EAAE,GAAG,CAAC,CAACS,MAAM,CAACP,OAAO,EAAE,YAAY,CAAC;QAC7N6F,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,MAAM;QACdC,WAAW,EAAElD,SAAS;QACtBmD,aAAa,EAAE;MACjB,CAAC,EAAE7L,WAAW,CAACkK,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;IACjC;EACF,CAAC,EAAE;IACDvP,GAAG,EAAE,gBAAgB;IACrBkD,KAAK,EAAE,SAASyL,cAAcA,CAACkC,MAAM,EAAEzP,KAAK,EAAE;MAC5C,IAAK,aAAaiD,KAAK,CAACyM,cAAc,CAACD,MAAM,CAAC,EAAE;QAC9C,OAAO,aAAaxM,KAAK,CAAC4K,YAAY,CAAC4B,MAAM,EAAEzP,KAAK,CAAC;MACvD;MACA,IAAIuD,UAAU,CAACkM,MAAM,CAAC,EAAE;QACtB,OAAOA,MAAM,CAACzP,KAAK,CAAC;MACtB;MACA,OAAO,aAAaiD,KAAK,CAACsJ,aAAa,CAAC3I,SAAS,EAAEtE,QAAQ,CAAC;QAC1DkN,SAAS,EAAE,sBAAsB;QACjCmD,IAAI,EAAE,SAAS;QACfI,WAAW,EAAE;MACf,CAAC,EAAE9L,WAAW,CAACjE,KAAK,EAAE,KAAK,CAAC,EAAE;QAC5BwO,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAACtL,aAAa,CAAC;AAChBT,eAAe,CAAC6H,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;AAChD7H,eAAe,CAAC6H,MAAM,EAAE,cAAc,EAAE;EACtCN,OAAO,EAAE,MAAM;EACfgG,OAAO,EAAE,OAAO;EAChBvI,WAAW,EAAE,EAAE;EACfd,SAAS,EAAE,EAAE;EACbuF,aAAa,EAAE,GAAG;EAClB7C,UAAU,EAAE,EAAE;EACd+C,MAAM,EAAE;IACNC,GAAG,EAAE,CAAC;IACN+C,KAAK,EAAE,CAAC;IACRE,MAAM,EAAE,CAAC;IACThD,IAAI,EAAE;EACR,CAAC;EACDzE,IAAI,EAAE;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}