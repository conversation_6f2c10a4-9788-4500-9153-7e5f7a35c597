{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nvar _excluded = [\"ref\"];\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\n/**\n * @fileOverview Legend\n */\nimport React, { PureComponent } from 'react';\nimport { DefaultLegendContent } from './DefaultLegendContent';\nimport { isNumber } from '../util/DataUtils';\nimport { getUniqPayload } from '../util/payload/getUniqPayload';\nfunction defaultUniqBy(entry) {\n  return entry.value;\n}\nfunction renderContent(content, props) {\n  if (/*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.cloneElement(content, props);\n  }\n  if (typeof content === 'function') {\n    return /*#__PURE__*/React.createElement(content, props);\n  }\n  var ref = props.ref,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(DefaultLegendContent, otherProps);\n}\nvar EPS = 1;\nexport var Legend = /*#__PURE__*/function (_PureComponent) {\n  function Legend() {\n    var _this;\n    _classCallCheck(this, Legend);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Legend, [].concat(args));\n    _defineProperty(_this, \"lastBoundingBox\", {\n      width: -1,\n      height: -1\n    });\n    return _this;\n  }\n  _inherits(Legend, _PureComponent);\n  return _createClass(Legend, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"getBBox\",\n    value: function getBBox() {\n      if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n        var box = this.wrapperNode.getBoundingClientRect();\n        box.height = this.wrapperNode.offsetHeight;\n        box.width = this.wrapperNode.offsetWidth;\n        return box;\n      }\n      return null;\n    }\n  }, {\n    key: \"updateBBox\",\n    value: function updateBBox() {\n      var onBBoxUpdate = this.props.onBBoxUpdate;\n      var box = this.getBBox();\n      if (box) {\n        if (Math.abs(box.width - this.lastBoundingBox.width) > EPS || Math.abs(box.height - this.lastBoundingBox.height) > EPS) {\n          this.lastBoundingBox.width = box.width;\n          this.lastBoundingBox.height = box.height;\n          if (onBBoxUpdate) {\n            onBBoxUpdate(box);\n          }\n        }\n      } else if (this.lastBoundingBox.width !== -1 || this.lastBoundingBox.height !== -1) {\n        this.lastBoundingBox.width = -1;\n        this.lastBoundingBox.height = -1;\n        if (onBBoxUpdate) {\n          onBBoxUpdate(null);\n        }\n      }\n    }\n  }, {\n    key: \"getBBoxSnapshot\",\n    value: function getBBoxSnapshot() {\n      if (this.lastBoundingBox.width >= 0 && this.lastBoundingBox.height >= 0) {\n        return _objectSpread({}, this.lastBoundingBox);\n      }\n      return {\n        width: 0,\n        height: 0\n      };\n    }\n  }, {\n    key: \"getDefaultPosition\",\n    value: function getDefaultPosition(style) {\n      var _this$props = this.props,\n        layout = _this$props.layout,\n        align = _this$props.align,\n        verticalAlign = _this$props.verticalAlign,\n        margin = _this$props.margin,\n        chartWidth = _this$props.chartWidth,\n        chartHeight = _this$props.chartHeight;\n      var hPos, vPos;\n      if (!style || (style.left === undefined || style.left === null) && (style.right === undefined || style.right === null)) {\n        if (align === 'center' && layout === 'vertical') {\n          var box = this.getBBoxSnapshot();\n          hPos = {\n            left: ((chartWidth || 0) - box.width) / 2\n          };\n        } else {\n          hPos = align === 'right' ? {\n            right: margin && margin.right || 0\n          } : {\n            left: margin && margin.left || 0\n          };\n        }\n      }\n      if (!style || (style.top === undefined || style.top === null) && (style.bottom === undefined || style.bottom === null)) {\n        if (verticalAlign === 'middle') {\n          var _box = this.getBBoxSnapshot();\n          vPos = {\n            top: ((chartHeight || 0) - _box.height) / 2\n          };\n        } else {\n          vPos = verticalAlign === 'bottom' ? {\n            bottom: margin && margin.bottom || 0\n          } : {\n            top: margin && margin.top || 0\n          };\n        }\n      }\n      return _objectSpread(_objectSpread({}, hPos), vPos);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        content = _this$props2.content,\n        width = _this$props2.width,\n        height = _this$props2.height,\n        wrapperStyle = _this$props2.wrapperStyle,\n        payloadUniqBy = _this$props2.payloadUniqBy,\n        payload = _this$props2.payload;\n      var outerStyle = _objectSpread(_objectSpread({\n        position: 'absolute',\n        width: width || 'auto',\n        height: height || 'auto'\n      }, this.getDefaultPosition(wrapperStyle)), wrapperStyle);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"recharts-legend-wrapper\",\n        style: outerStyle,\n        ref: function ref(node) {\n          _this2.wrapperNode = node;\n        }\n      }, renderContent(content, _objectSpread(_objectSpread({}, this.props), {}, {\n        payload: getUniqPayload(payload, payloadUniqBy, defaultUniqBy)\n      })));\n    }\n  }], [{\n    key: \"getWithHeight\",\n    value: function getWithHeight(item, chartWidth) {\n      var _this$defaultProps$it = _objectSpread(_objectSpread({}, this.defaultProps), item.props),\n        layout = _this$defaultProps$it.layout;\n      if (layout === 'vertical' && isNumber(item.props.height)) {\n        return {\n          height: item.props.height\n        };\n      }\n      if (layout === 'horizontal') {\n        return {\n          width: item.props.width || chartWidth\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Legend, \"displayName\", 'Legend');\n_defineProperty(Legend, \"defaultProps\", {\n  iconSize: 14,\n  layout: 'horizontal',\n  align: 'center',\n  verticalAlign: 'bottom'\n});", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_excluded", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "call", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "bind", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "_objectWithoutProperties", "source", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "hasOwnProperty", "React", "PureComponent", "DefaultLegendContent", "isNumber", "getUniqPayload", "defaultUniqBy", "entry", "renderContent", "content", "isValidElement", "cloneElement", "createElement", "ref", "otherProps", "EPS", "Legend", "_PureComponent", "_this", "_len", "args", "Array", "_key", "concat", "width", "height", "componentDidMount", "updateBBox", "componentDidUpdate", "getBBox", "wrapperNode", "getBoundingClientRect", "box", "offsetHeight", "offsetWidth", "onBBoxUpdate", "Math", "abs", "lastBoundingBox", "getBBoxSnapshot", "getDefaultPosition", "style", "_this$props", "layout", "align", "verticalAlign", "margin", "chartWidth", "chartHeight", "hPos", "vPos", "left", "undefined", "right", "top", "bottom", "_box", "render", "_this2", "_this$props2", "wrapperStyle", "payloadUniqBy", "payload", "outerStyle", "position", "className", "node", "getWithHeight", "item", "_this$defaultProps$it", "defaultProps", "iconSize"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/component/Legend.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar _excluded = [\"ref\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\n/**\n * @fileOverview Legend\n */\nimport React, { PureComponent } from 'react';\nimport { DefaultLegendContent } from './DefaultLegendContent';\nimport { isNumber } from '../util/DataUtils';\nimport { getUniqPayload } from '../util/payload/getUniqPayload';\nfunction defaultUniqBy(entry) {\n  return entry.value;\n}\nfunction renderContent(content, props) {\n  if ( /*#__PURE__*/React.isValidElement(content)) {\n    return /*#__PURE__*/React.cloneElement(content, props);\n  }\n  if (typeof content === 'function') {\n    return /*#__PURE__*/React.createElement(content, props);\n  }\n  var ref = props.ref,\n    otherProps = _objectWithoutProperties(props, _excluded);\n  return /*#__PURE__*/React.createElement(DefaultLegendContent, otherProps);\n}\nvar EPS = 1;\nexport var Legend = /*#__PURE__*/function (_PureComponent) {\n  function Legend() {\n    var _this;\n    _classCallCheck(this, Legend);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Legend, [].concat(args));\n    _defineProperty(_this, \"lastBoundingBox\", {\n      width: -1,\n      height: -1\n    });\n    return _this;\n  }\n  _inherits(Legend, _PureComponent);\n  return _createClass(Legend, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.updateBBox();\n    }\n  }, {\n    key: \"getBBox\",\n    value: function getBBox() {\n      if (this.wrapperNode && this.wrapperNode.getBoundingClientRect) {\n        var box = this.wrapperNode.getBoundingClientRect();\n        box.height = this.wrapperNode.offsetHeight;\n        box.width = this.wrapperNode.offsetWidth;\n        return box;\n      }\n      return null;\n    }\n  }, {\n    key: \"updateBBox\",\n    value: function updateBBox() {\n      var onBBoxUpdate = this.props.onBBoxUpdate;\n      var box = this.getBBox();\n      if (box) {\n        if (Math.abs(box.width - this.lastBoundingBox.width) > EPS || Math.abs(box.height - this.lastBoundingBox.height) > EPS) {\n          this.lastBoundingBox.width = box.width;\n          this.lastBoundingBox.height = box.height;\n          if (onBBoxUpdate) {\n            onBBoxUpdate(box);\n          }\n        }\n      } else if (this.lastBoundingBox.width !== -1 || this.lastBoundingBox.height !== -1) {\n        this.lastBoundingBox.width = -1;\n        this.lastBoundingBox.height = -1;\n        if (onBBoxUpdate) {\n          onBBoxUpdate(null);\n        }\n      }\n    }\n  }, {\n    key: \"getBBoxSnapshot\",\n    value: function getBBoxSnapshot() {\n      if (this.lastBoundingBox.width >= 0 && this.lastBoundingBox.height >= 0) {\n        return _objectSpread({}, this.lastBoundingBox);\n      }\n      return {\n        width: 0,\n        height: 0\n      };\n    }\n  }, {\n    key: \"getDefaultPosition\",\n    value: function getDefaultPosition(style) {\n      var _this$props = this.props,\n        layout = _this$props.layout,\n        align = _this$props.align,\n        verticalAlign = _this$props.verticalAlign,\n        margin = _this$props.margin,\n        chartWidth = _this$props.chartWidth,\n        chartHeight = _this$props.chartHeight;\n      var hPos, vPos;\n      if (!style || (style.left === undefined || style.left === null) && (style.right === undefined || style.right === null)) {\n        if (align === 'center' && layout === 'vertical') {\n          var box = this.getBBoxSnapshot();\n          hPos = {\n            left: ((chartWidth || 0) - box.width) / 2\n          };\n        } else {\n          hPos = align === 'right' ? {\n            right: margin && margin.right || 0\n          } : {\n            left: margin && margin.left || 0\n          };\n        }\n      }\n      if (!style || (style.top === undefined || style.top === null) && (style.bottom === undefined || style.bottom === null)) {\n        if (verticalAlign === 'middle') {\n          var _box = this.getBBoxSnapshot();\n          vPos = {\n            top: ((chartHeight || 0) - _box.height) / 2\n          };\n        } else {\n          vPos = verticalAlign === 'bottom' ? {\n            bottom: margin && margin.bottom || 0\n          } : {\n            top: margin && margin.top || 0\n          };\n        }\n      }\n      return _objectSpread(_objectSpread({}, hPos), vPos);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props2 = this.props,\n        content = _this$props2.content,\n        width = _this$props2.width,\n        height = _this$props2.height,\n        wrapperStyle = _this$props2.wrapperStyle,\n        payloadUniqBy = _this$props2.payloadUniqBy,\n        payload = _this$props2.payload;\n      var outerStyle = _objectSpread(_objectSpread({\n        position: 'absolute',\n        width: width || 'auto',\n        height: height || 'auto'\n      }, this.getDefaultPosition(wrapperStyle)), wrapperStyle);\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: \"recharts-legend-wrapper\",\n        style: outerStyle,\n        ref: function ref(node) {\n          _this2.wrapperNode = node;\n        }\n      }, renderContent(content, _objectSpread(_objectSpread({}, this.props), {}, {\n        payload: getUniqPayload(payload, payloadUniqBy, defaultUniqBy)\n      })));\n    }\n  }], [{\n    key: \"getWithHeight\",\n    value: function getWithHeight(item, chartWidth) {\n      var _this$defaultProps$it = _objectSpread(_objectSpread({}, this.defaultProps), item.props),\n        layout = _this$defaultProps$it.layout;\n      if (layout === 'vertical' && isNumber(item.props.height)) {\n        return {\n          height: item.props.height\n        };\n      }\n      if (layout === 'horizontal') {\n        return {\n          width: item.props.width || chartWidth\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_defineProperty(Legend, \"displayName\", 'Legend');\n_defineProperty(Legend, \"defaultProps\", {\n  iconSize: 14,\n  layout: 'horizontal',\n  align: 'center',\n  verticalAlign: 'bottom'\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,IAAIK,SAAS,GAAG,CAAC,KAAK,CAAC;AACvB,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIZ,CAAC,GAAGU,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKR,CAAC,GAAGA,CAAC,CAACa,MAAM,CAAC,UAAUL,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACO,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAET,CAAC,CAAC;EAAE;EAAE,OAAOS,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACC,MAAM,EAAEZ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACX,CAAC,CAAC,GAAGW,SAAS,CAACX,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASmB,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACZ,MAAM,EAAEa,CAAC,EAAE,EAAE;IAAE,IAAIC,UAAU,GAAGF,KAAK,CAACC,CAAC,CAAC;IAAEC,UAAU,CAACnB,UAAU,GAAGmB,UAAU,CAACnB,UAAU,IAAI,KAAK;IAAEmB,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAE1B,MAAM,CAACe,cAAc,CAACM,MAAM,EAAEM,cAAc,CAACH,UAAU,CAACI,GAAG,CAAC,EAAEJ,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASK,YAAYA,CAACX,WAAW,EAAEY,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEV,iBAAiB,CAACF,WAAW,CAACxB,SAAS,EAAEoC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEX,iBAAiB,CAACF,WAAW,EAAEa,WAAW,CAAC;EAAE/B,MAAM,CAACe,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEQ,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOR,WAAW;AAAE;AAC5R,SAASc,UAAUA,CAACjC,CAAC,EAAET,CAAC,EAAEO,CAAC,EAAE;EAAE,OAAOP,CAAC,GAAG2C,eAAe,CAAC3C,CAAC,CAAC,EAAE4C,0BAA0B,CAACnC,CAAC,EAAEoC,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAAC/C,CAAC,EAAEO,CAAC,IAAI,EAAE,EAAEoC,eAAe,CAAClC,CAAC,CAAC,CAACN,WAAW,CAAC,GAAGH,CAAC,CAACiB,KAAK,CAACR,CAAC,EAAEF,CAAC,CAAC,CAAC;AAAE;AAC1M,SAASqC,0BAA0BA,CAACI,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKlD,OAAO,CAACkD,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIpB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOqB,sBAAsB,CAACF,IAAI,CAAC;AAAE;AAC/R,SAASE,sBAAsBA,CAACF,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIG,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAIpC,CAAC,GAAG,CAAC2C,OAAO,CAAChD,SAAS,CAACiD,OAAO,CAACJ,IAAI,CAACH,OAAO,CAACC,SAAS,CAACK,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAO3C,CAAC,EAAE,CAAC;EAAE,OAAO,CAACoC,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAACpC,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAASkC,eAAeA,CAAC3C,CAAC,EAAE;EAAE2C,eAAe,GAAGjC,MAAM,CAAC4C,cAAc,GAAG5C,MAAM,CAAC6C,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,SAASb,eAAeA,CAAC3C,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACyD,SAAS,IAAI/C,MAAM,CAAC6C,cAAc,CAACvD,CAAC,CAAC;EAAE,CAAC;EAAE,OAAO2C,eAAe,CAAC3C,CAAC,CAAC;AAAE;AACnN,SAAS0D,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI/B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAE8B,QAAQ,CAACvD,SAAS,GAAGM,MAAM,CAACmD,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACxD,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE2D,KAAK,EAAEH,QAAQ;MAAEvB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEzB,MAAM,CAACe,cAAc,CAACkC,QAAQ,EAAE,WAAW,EAAE;IAAEvB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIwB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAAC/D,CAAC,EAAEgE,CAAC,EAAE;EAAED,eAAe,GAAGrD,MAAM,CAAC4C,cAAc,GAAG5C,MAAM,CAAC4C,cAAc,CAACE,IAAI,CAAC,CAAC,GAAG,SAASO,eAAeA,CAAC/D,CAAC,EAAEgE,CAAC,EAAE;IAAEhE,CAAC,CAACyD,SAAS,GAAGO,CAAC;IAAE,OAAOhE,CAAC;EAAE,CAAC;EAAE,OAAO+D,eAAe,CAAC/D,CAAC,EAAEgE,CAAC,CAAC;AAAE;AACvM,SAAS1C,eAAeA,CAAC2C,GAAG,EAAE3B,GAAG,EAAEwB,KAAK,EAAE;EAAExB,GAAG,GAAGD,cAAc,CAACC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAI2B,GAAG,EAAE;IAAEvD,MAAM,CAACe,cAAc,CAACwC,GAAG,EAAE3B,GAAG,EAAE;MAAEwB,KAAK,EAAEA,KAAK;MAAE/C,UAAU,EAAE,IAAI;MAAEoB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE6B,GAAG,CAAC3B,GAAG,CAAC,GAAGwB,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;AAC3O,SAAS5B,cAAcA,CAAC5B,CAAC,EAAE;EAAE,IAAIwB,CAAC,GAAGiC,YAAY,CAACzD,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIV,OAAO,CAACkC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASiC,YAAYA,CAACzD,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIT,OAAO,CAACU,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACR,MAAM,CAACkE,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK5D,CAAC,EAAE;IAAE,IAAI0B,CAAC,GAAG1B,CAAC,CAAC0C,IAAI,CAACxC,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIT,OAAO,CAACkC,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIJ,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKrB,CAAC,GAAG4D,MAAM,GAAGC,MAAM,EAAE5D,CAAC,CAAC;AAAE;AAC3T,SAAS6D,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIxC,MAAM,GAAG0C,6BAA6B,CAACF,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIlC,GAAG,EAAEL,CAAC;EAAE,IAAIvB,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAI8D,gBAAgB,GAAGhE,MAAM,CAACE,qBAAqB,CAAC2D,MAAM,CAAC;IAAE,KAAKtC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,gBAAgB,CAACtD,MAAM,EAAEa,CAAC,EAAE,EAAE;MAAEK,GAAG,GAAGoC,gBAAgB,CAACzC,CAAC,CAAC;MAAE,IAAIuC,QAAQ,CAACG,OAAO,CAACrC,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAAC5B,MAAM,CAACN,SAAS,CAACwE,oBAAoB,CAAC3B,IAAI,CAACsB,MAAM,EAAEjC,GAAG,CAAC,EAAE;MAAUP,MAAM,CAACO,GAAG,CAAC,GAAGiC,MAAM,CAACjC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOP,MAAM;AAAE;AAC3e,SAAS0C,6BAA6BA,CAACF,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIxC,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,GAAG,IAAIiC,MAAM,EAAE;IAAE,IAAI7D,MAAM,CAACN,SAAS,CAACyE,cAAc,CAAC5B,IAAI,CAACsB,MAAM,EAAEjC,GAAG,CAAC,EAAE;MAAE,IAAIkC,QAAQ,CAACG,OAAO,CAACrC,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUP,MAAM,CAACO,GAAG,CAAC,GAAGiC,MAAM,CAACjC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOP,MAAM;AAAE;AACtR;AACA;AACA;AACA,OAAO+C,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACtB,KAAK;AACpB;AACA,SAASuB,aAAaA,CAACC,OAAO,EAAEtD,KAAK,EAAE;EACrC,IAAK,aAAa8C,KAAK,CAACS,cAAc,CAACD,OAAO,CAAC,EAAE;IAC/C,OAAO,aAAaR,KAAK,CAACU,YAAY,CAACF,OAAO,EAAEtD,KAAK,CAAC;EACxD;EACA,IAAI,OAAOsD,OAAO,KAAK,UAAU,EAAE;IACjC,OAAO,aAAaR,KAAK,CAACW,aAAa,CAACH,OAAO,EAAEtD,KAAK,CAAC;EACzD;EACA,IAAI0D,GAAG,GAAG1D,KAAK,CAAC0D,GAAG;IACjBC,UAAU,GAAGrB,wBAAwB,CAACtC,KAAK,EAAE3B,SAAS,CAAC;EACzD,OAAO,aAAayE,KAAK,CAACW,aAAa,CAACT,oBAAoB,EAAEW,UAAU,CAAC;AAC3E;AACA,IAAIC,GAAG,GAAG,CAAC;AACX,OAAO,IAAIC,MAAM,GAAG,aAAa,UAAUC,cAAc,EAAE;EACzD,SAASD,MAAMA,CAAA,EAAG;IAChB,IAAIE,KAAK;IACTrE,eAAe,CAAC,IAAI,EAAEmE,MAAM,CAAC;IAC7B,KAAK,IAAIG,IAAI,GAAG7E,SAAS,CAACC,MAAM,EAAE6E,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGhF,SAAS,CAACgF,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGrD,UAAU,CAAC,IAAI,EAAEmD,MAAM,EAAE,EAAE,CAACO,MAAM,CAACH,IAAI,CAAC,CAAC;IACjD3E,eAAe,CAACyE,KAAK,EAAE,iBAAiB,EAAE;MACxCM,KAAK,EAAE,CAAC,CAAC;MACTC,MAAM,EAAE,CAAC;IACX,CAAC,CAAC;IACF,OAAOP,KAAK;EACd;EACArC,SAAS,CAACmC,MAAM,EAAEC,cAAc,CAAC;EACjC,OAAOvD,YAAY,CAACsD,MAAM,EAAE,CAAC;IAC3BvD,GAAG,EAAE,mBAAmB;IACxBwB,KAAK,EAAE,SAASyC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDlE,GAAG,EAAE,oBAAoB;IACzBwB,KAAK,EAAE,SAAS2C,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACD,UAAU,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDlE,GAAG,EAAE,SAAS;IACdwB,KAAK,EAAE,SAAS4C,OAAOA,CAAA,EAAG;MACxB,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,qBAAqB,EAAE;QAC9D,IAAIC,GAAG,GAAG,IAAI,CAACF,WAAW,CAACC,qBAAqB,CAAC,CAAC;QAClDC,GAAG,CAACP,MAAM,GAAG,IAAI,CAACK,WAAW,CAACG,YAAY;QAC1CD,GAAG,CAACR,KAAK,GAAG,IAAI,CAACM,WAAW,CAACI,WAAW;QACxC,OAAOF,GAAG;MACZ;MACA,OAAO,IAAI;IACb;EACF,CAAC,EAAE;IACDvE,GAAG,EAAE,YAAY;IACjBwB,KAAK,EAAE,SAAS0C,UAAUA,CAAA,EAAG;MAC3B,IAAIQ,YAAY,GAAG,IAAI,CAAChF,KAAK,CAACgF,YAAY;MAC1C,IAAIH,GAAG,GAAG,IAAI,CAACH,OAAO,CAAC,CAAC;MACxB,IAAIG,GAAG,EAAE;QACP,IAAII,IAAI,CAACC,GAAG,CAACL,GAAG,CAACR,KAAK,GAAG,IAAI,CAACc,eAAe,CAACd,KAAK,CAAC,GAAGT,GAAG,IAAIqB,IAAI,CAACC,GAAG,CAACL,GAAG,CAACP,MAAM,GAAG,IAAI,CAACa,eAAe,CAACb,MAAM,CAAC,GAAGV,GAAG,EAAE;UACtH,IAAI,CAACuB,eAAe,CAACd,KAAK,GAAGQ,GAAG,CAACR,KAAK;UACtC,IAAI,CAACc,eAAe,CAACb,MAAM,GAAGO,GAAG,CAACP,MAAM;UACxC,IAAIU,YAAY,EAAE;YAChBA,YAAY,CAACH,GAAG,CAAC;UACnB;QACF;MACF,CAAC,MAAM,IAAI,IAAI,CAACM,eAAe,CAACd,KAAK,KAAK,CAAC,CAAC,IAAI,IAAI,CAACc,eAAe,CAACb,MAAM,KAAK,CAAC,CAAC,EAAE;QAClF,IAAI,CAACa,eAAe,CAACd,KAAK,GAAG,CAAC,CAAC;QAC/B,IAAI,CAACc,eAAe,CAACb,MAAM,GAAG,CAAC,CAAC;QAChC,IAAIU,YAAY,EAAE;UAChBA,YAAY,CAAC,IAAI,CAAC;QACpB;MACF;IACF;EACF,CAAC,EAAE;IACD1E,GAAG,EAAE,iBAAiB;IACtBwB,KAAK,EAAE,SAASsD,eAAeA,CAAA,EAAG;MAChC,IAAI,IAAI,CAACD,eAAe,CAACd,KAAK,IAAI,CAAC,IAAI,IAAI,CAACc,eAAe,CAACb,MAAM,IAAI,CAAC,EAAE;QACvE,OAAOpF,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiG,eAAe,CAAC;MAChD;MACA,OAAO;QACLd,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE;MACV,CAAC;IACH;EACF,CAAC,EAAE;IACDhE,GAAG,EAAE,oBAAoB;IACzBwB,KAAK,EAAE,SAASuD,kBAAkBA,CAACC,KAAK,EAAE;MACxC,IAAIC,WAAW,GAAG,IAAI,CAACvF,KAAK;QAC1BwF,MAAM,GAAGD,WAAW,CAACC,MAAM;QAC3BC,KAAK,GAAGF,WAAW,CAACE,KAAK;QACzBC,aAAa,GAAGH,WAAW,CAACG,aAAa;QACzCC,MAAM,GAAGJ,WAAW,CAACI,MAAM;QAC3BC,UAAU,GAAGL,WAAW,CAACK,UAAU;QACnCC,WAAW,GAAGN,WAAW,CAACM,WAAW;MACvC,IAAIC,IAAI,EAAEC,IAAI;MACd,IAAI,CAACT,KAAK,IAAI,CAACA,KAAK,CAACU,IAAI,KAAKC,SAAS,IAAIX,KAAK,CAACU,IAAI,KAAK,IAAI,MAAMV,KAAK,CAACY,KAAK,KAAKD,SAAS,IAAIX,KAAK,CAACY,KAAK,KAAK,IAAI,CAAC,EAAE;QACtH,IAAIT,KAAK,KAAK,QAAQ,IAAID,MAAM,KAAK,UAAU,EAAE;UAC/C,IAAIX,GAAG,GAAG,IAAI,CAACO,eAAe,CAAC,CAAC;UAChCU,IAAI,GAAG;YACLE,IAAI,EAAE,CAAC,CAACJ,UAAU,IAAI,CAAC,IAAIf,GAAG,CAACR,KAAK,IAAI;UAC1C,CAAC;QACH,CAAC,MAAM;UACLyB,IAAI,GAAGL,KAAK,KAAK,OAAO,GAAG;YACzBS,KAAK,EAAEP,MAAM,IAAIA,MAAM,CAACO,KAAK,IAAI;UACnC,CAAC,GAAG;YACFF,IAAI,EAAEL,MAAM,IAAIA,MAAM,CAACK,IAAI,IAAI;UACjC,CAAC;QACH;MACF;MACA,IAAI,CAACV,KAAK,IAAI,CAACA,KAAK,CAACa,GAAG,KAAKF,SAAS,IAAIX,KAAK,CAACa,GAAG,KAAK,IAAI,MAAMb,KAAK,CAACc,MAAM,KAAKH,SAAS,IAAIX,KAAK,CAACc,MAAM,KAAK,IAAI,CAAC,EAAE;QACtH,IAAIV,aAAa,KAAK,QAAQ,EAAE;UAC9B,IAAIW,IAAI,GAAG,IAAI,CAACjB,eAAe,CAAC,CAAC;UACjCW,IAAI,GAAG;YACLI,GAAG,EAAE,CAAC,CAACN,WAAW,IAAI,CAAC,IAAIQ,IAAI,CAAC/B,MAAM,IAAI;UAC5C,CAAC;QACH,CAAC,MAAM;UACLyB,IAAI,GAAGL,aAAa,KAAK,QAAQ,GAAG;YAClCU,MAAM,EAAET,MAAM,IAAIA,MAAM,CAACS,MAAM,IAAI;UACrC,CAAC,GAAG;YACFD,GAAG,EAAER,MAAM,IAAIA,MAAM,CAACQ,GAAG,IAAI;UAC/B,CAAC;QACH;MACF;MACA,OAAOjH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4G,IAAI,CAAC,EAAEC,IAAI,CAAC;IACrD;EACF,CAAC,EAAE;IACDzF,GAAG,EAAE,QAAQ;IACbwB,KAAK,EAAE,SAASwE,MAAMA,CAAA,EAAG;MACvB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACxG,KAAK;QAC3BsD,OAAO,GAAGkD,YAAY,CAAClD,OAAO;QAC9Be,KAAK,GAAGmC,YAAY,CAACnC,KAAK;QAC1BC,MAAM,GAAGkC,YAAY,CAAClC,MAAM;QAC5BmC,YAAY,GAAGD,YAAY,CAACC,YAAY;QACxCC,aAAa,GAAGF,YAAY,CAACE,aAAa;QAC1CC,OAAO,GAAGH,YAAY,CAACG,OAAO;MAChC,IAAIC,UAAU,GAAG1H,aAAa,CAACA,aAAa,CAAC;QAC3C2H,QAAQ,EAAE,UAAU;QACpBxC,KAAK,EAAEA,KAAK,IAAI,MAAM;QACtBC,MAAM,EAAEA,MAAM,IAAI;MACpB,CAAC,EAAE,IAAI,CAACe,kBAAkB,CAACoB,YAAY,CAAC,CAAC,EAAEA,YAAY,CAAC;MACxD,OAAO,aAAa3D,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;QAC7CqD,SAAS,EAAE,yBAAyB;QACpCxB,KAAK,EAAEsB,UAAU;QACjBlD,GAAG,EAAE,SAASA,GAAGA,CAACqD,IAAI,EAAE;UACtBR,MAAM,CAAC5B,WAAW,GAAGoC,IAAI;QAC3B;MACF,CAAC,EAAE1D,aAAa,CAACC,OAAO,EAAEpE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACc,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACzE2G,OAAO,EAAEzD,cAAc,CAACyD,OAAO,EAAED,aAAa,EAAEvD,aAAa;MAC/D,CAAC,CAAC,CAAC,CAAC;IACN;EACF,CAAC,CAAC,EAAE,CAAC;IACH7C,GAAG,EAAE,eAAe;IACpBwB,KAAK,EAAE,SAASkF,aAAaA,CAACC,IAAI,EAAErB,UAAU,EAAE;MAC9C,IAAIsB,qBAAqB,GAAGhI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiI,YAAY,CAAC,EAAEF,IAAI,CAACjH,KAAK,CAAC;QACzFwF,MAAM,GAAG0B,qBAAqB,CAAC1B,MAAM;MACvC,IAAIA,MAAM,KAAK,UAAU,IAAIvC,QAAQ,CAACgE,IAAI,CAACjH,KAAK,CAACsE,MAAM,CAAC,EAAE;QACxD,OAAO;UACLA,MAAM,EAAE2C,IAAI,CAACjH,KAAK,CAACsE;QACrB,CAAC;MACH;MACA,IAAIkB,MAAM,KAAK,YAAY,EAAE;QAC3B,OAAO;UACLnB,KAAK,EAAE4C,IAAI,CAACjH,KAAK,CAACqE,KAAK,IAAIuB;QAC7B,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC7C,aAAa,CAAC;AAChBzD,eAAe,CAACuE,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAC;AAChDvE,eAAe,CAACuE,MAAM,EAAE,cAAc,EAAE;EACtCuD,QAAQ,EAAE,EAAE;EACZ5B,MAAM,EAAE,YAAY;EACpBC,KAAK,EAAE,QAAQ;EACfC,aAAa,EAAE;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}