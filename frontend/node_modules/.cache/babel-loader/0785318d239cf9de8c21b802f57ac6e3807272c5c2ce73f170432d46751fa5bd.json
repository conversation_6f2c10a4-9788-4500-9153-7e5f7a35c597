{"ast": null, "code": "/**\n * The base implementation of `_.lt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than `other`,\n *  else `false`.\n */\nfunction baseLt(value, other) {\n  return value < other;\n}\nmodule.exports = baseLt;", "map": {"version": 3, "names": ["baseLt", "value", "other", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_baseLt.js"], "sourcesContent": ["/**\n * The base implementation of `_.lt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than `other`,\n *  else `false`.\n */\nfunction baseLt(value, other) {\n  return value < other;\n}\n\nmodule.exports = baseLt;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC5B,OAAOD,KAAK,GAAGC,KAAK;AACtB;AAEAC,MAAM,CAACC,OAAO,GAAGJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}