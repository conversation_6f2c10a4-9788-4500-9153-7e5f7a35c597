{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name parseJSON\n * @category Common Helpers\n * @summary Parse a JSON date string\n *\n * @description\n * Converts a complete ISO date string in UTC time, the typical format for transmitting\n * a date in JSON, to a JavaScript `Date` instance.\n *\n * This is a minimal implementation for converting dates retrieved from a JSON API to\n * a `Date` instance which can be used with other functions in the `date-fns` library.\n * The following formats are supported:\n *\n * - `2000-03-15T05:20:10.123Z`: The output of `.toISOString()` and `JSON.stringify(new Date())`\n * - `2000-03-15T05:20:10Z`: Without milliseconds\n * - `2000-03-15T05:20:10+00:00`: With a zero offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+05:45`: With a positive or negative offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+0000`: With a zero offset without a colon\n * - `2000-03-15T05:20:10`: Without a trailing 'Z' symbol\n * - `2000-03-15T05:20:10.1234567`: Up to 7 digits in milliseconds field. Only first 3 are taken into account since JS does not allow fractional milliseconds\n * - `2000-03-15 05:20:10`: With a space instead of a 'T' separator for APIs returning a SQL date without reformatting\n *\n * For convenience and ease of use these other input types are also supported\n * via [toDate]{@link https://date-fns.org/docs/toDate}:\n *\n * - A `Date` instance will be cloned\n * - A `number` will be treated as a timestamp\n *\n * Any other input type or invalid date strings will return an `Invalid Date`.\n *\n * @param {String|Number|Date} argument A fully formed ISO8601 date string to convert\n * @returns {Date} the parsed date in the local time zone\n * @throws {TypeError} 1 argument required\n */\nexport default function parseJSON(argument) {\n  requiredArgs(1, arguments);\n  if (typeof argument === 'string') {\n    var parts = argument.match(/(\\d{4})-(\\d{2})-(\\d{2})[T ](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{0,7}))?(?:Z|(.)(\\d{2}):?(\\d{2})?)?/);\n    if (parts) {\n      // Group 8 matches the sign\n      return new Date(Date.UTC(+parts[1], +parts[2] - 1, +parts[3], +parts[4] - (+parts[9] || 0) * (parts[8] == '-' ? -1 : 1), +parts[5] - (+parts[10] || 0) * (parts[8] == '-' ? -1 : 1), +parts[6], +((parts[7] || '0') + '00').substring(0, 3)));\n    }\n    return new Date(NaN);\n  }\n  return toDate(argument);\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "parseJSON", "argument", "arguments", "parts", "match", "Date", "UTC", "substring", "NaN"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/parseJSON/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name parseJSON\n * @category Common Helpers\n * @summary Parse a JSON date string\n *\n * @description\n * Converts a complete ISO date string in UTC time, the typical format for transmitting\n * a date in JSON, to a JavaScript `Date` instance.\n *\n * This is a minimal implementation for converting dates retrieved from a JSON API to\n * a `Date` instance which can be used with other functions in the `date-fns` library.\n * The following formats are supported:\n *\n * - `2000-03-15T05:20:10.123Z`: The output of `.toISOString()` and `JSON.stringify(new Date())`\n * - `2000-03-15T05:20:10Z`: Without milliseconds\n * - `2000-03-15T05:20:10+00:00`: With a zero offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+05:45`: With a positive or negative offset, the default JSON encoded format in some other languages\n * - `2000-03-15T05:20:10+0000`: With a zero offset without a colon\n * - `2000-03-15T05:20:10`: Without a trailing 'Z' symbol\n * - `2000-03-15T05:20:10.1234567`: Up to 7 digits in milliseconds field. Only first 3 are taken into account since JS does not allow fractional milliseconds\n * - `2000-03-15 05:20:10`: With a space instead of a 'T' separator for APIs returning a SQL date without reformatting\n *\n * For convenience and ease of use these other input types are also supported\n * via [toDate]{@link https://date-fns.org/docs/toDate}:\n *\n * - A `Date` instance will be cloned\n * - A `number` will be treated as a timestamp\n *\n * Any other input type or invalid date strings will return an `Invalid Date`.\n *\n * @param {String|Number|Date} argument A fully formed ISO8601 date string to convert\n * @returns {Date} the parsed date in the local time zone\n * @throws {TypeError} 1 argument required\n */\nexport default function parseJSON(argument) {\n  requiredArgs(1, arguments);\n  if (typeof argument === 'string') {\n    var parts = argument.match(/(\\d{4})-(\\d{2})-(\\d{2})[T ](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d{0,7}))?(?:Z|(.)(\\d{2}):?(\\d{2})?)?/);\n    if (parts) {\n      // Group 8 matches the sign\n      return new Date(Date.UTC(+parts[1], +parts[2] - 1, +parts[3], +parts[4] - (+parts[9] || 0) * (parts[8] == '-' ? -1 : 1), +parts[5] - (+parts[10] || 0) * (parts[8] == '-' ? -1 : 1), +parts[6], +((parts[7] || '0') + '00').substring(0, 3)));\n    }\n    return new Date(NaN);\n  }\n  return toDate(argument);\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,QAAQ,EAAE;EAC1CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;IAChC,IAAIE,KAAK,GAAGF,QAAQ,CAACG,KAAK,CAAC,+FAA+F,CAAC;IAC3H,IAAID,KAAK,EAAE;MACT;MACA,OAAO,IAAIE,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAACH,KAAK,CAAC,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAACA,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,EAAEI,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/O;IACA,OAAO,IAAIF,IAAI,CAACG,GAAG,CAAC;EACtB;EACA,OAAOV,MAAM,CAACG,QAAQ,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}