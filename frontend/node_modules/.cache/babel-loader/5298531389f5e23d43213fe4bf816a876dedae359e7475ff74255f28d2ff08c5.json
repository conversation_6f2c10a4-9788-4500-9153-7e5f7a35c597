{"ast": null, "code": "export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](),\n    set = new Set();\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n    while ({\n      value,\n      done\n    } = iterator.next()) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n  return true;\n}\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}", "map": {"version": 3, "names": ["superset", "values", "other", "iterator", "Symbol", "set", "Set", "o", "io", "intern", "has", "value", "done", "next", "ivalue", "add", "Object", "is", "valueOf"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-array/src/superset.js"], "sourcesContent": ["export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n  return true;\n}\n\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC9C,MAAMC,QAAQ,GAAGF,MAAM,CAACG,MAAM,CAACD,QAAQ,CAAC,CAAC,CAAC;IAAEE,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3D,KAAK,MAAMC,CAAC,IAAIL,KAAK,EAAE;IACrB,MAAMM,EAAE,GAAGC,MAAM,CAACF,CAAC,CAAC;IACpB,IAAIF,GAAG,CAACK,GAAG,CAACF,EAAE,CAAC,EAAE;IACjB,IAAIG,KAAK,EAAEC,IAAI;IACf,OAAQ;MAACD,KAAK;MAAEC;IAAI,CAAC,GAAGT,QAAQ,CAACU,IAAI,CAAC,CAAC,EAAG;MACxC,IAAID,IAAI,EAAE,OAAO,KAAK;MACtB,MAAME,MAAM,GAAGL,MAAM,CAACE,KAAK,CAAC;MAC5BN,GAAG,CAACU,GAAG,CAACD,MAAM,CAAC;MACf,IAAIE,MAAM,CAACC,EAAE,CAACT,EAAE,EAAEM,MAAM,CAAC,EAAE;IAC7B;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASL,MAAMA,CAACE,KAAK,EAAE;EACrB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACO,OAAO,CAAC,CAAC,GAAGP,KAAK;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}