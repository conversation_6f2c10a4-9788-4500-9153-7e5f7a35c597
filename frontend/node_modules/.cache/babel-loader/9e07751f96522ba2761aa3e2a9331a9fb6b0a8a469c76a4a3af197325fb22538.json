{"ast": null, "code": "var apply = require('./_apply');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * A specialized version of `baseRest` which transforms the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @param {Function} transform The rest array transform.\n * @returns {Function} Returns the new function.\n */\nfunction overRest(func, start, transform) {\n  start = nativeMax(start === undefined ? func.length - 1 : start, 0);\n  return function () {\n    var args = arguments,\n      index = -1,\n      length = nativeMax(args.length - start, 0),\n      array = Array(length);\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n    index = -1;\n    var otherArgs = Array(start + 1);\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n    otherArgs[start] = transform(array);\n    return apply(func, this, otherArgs);\n  };\n}\nmodule.exports = overRest;", "map": {"version": 3, "names": ["apply", "require", "nativeMax", "Math", "max", "overRest", "func", "start", "transform", "undefined", "length", "args", "arguments", "index", "array", "Array", "otherArgs", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_overRest.js"], "sourcesContent": ["var apply = require('./_apply');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * A specialized version of `baseRest` which transforms the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @param {Function} transform The rest array transform.\n * @returns {Function} Returns the new function.\n */\nfunction overRest(func, start, transform) {\n  start = nativeMax(start === undefined ? (func.length - 1) : start, 0);\n  return function() {\n    var args = arguments,\n        index = -1,\n        length = nativeMax(args.length - start, 0),\n        array = Array(length);\n\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n    index = -1;\n    var otherArgs = Array(start + 1);\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n    otherArgs[start] = transform(array);\n    return apply(func, this, otherArgs);\n  };\n}\n\nmodule.exports = overRest;\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACxCD,KAAK,GAAGL,SAAS,CAACK,KAAK,KAAKE,SAAS,GAAIH,IAAI,CAACI,MAAM,GAAG,CAAC,GAAIH,KAAK,EAAE,CAAC,CAAC;EACrE,OAAO,YAAW;IAChB,IAAII,IAAI,GAAGC,SAAS;MAChBC,KAAK,GAAG,CAAC,CAAC;MACVH,MAAM,GAAGR,SAAS,CAACS,IAAI,CAACD,MAAM,GAAGH,KAAK,EAAE,CAAC,CAAC;MAC1CO,KAAK,GAAGC,KAAK,CAACL,MAAM,CAAC;IAEzB,OAAO,EAAEG,KAAK,GAAGH,MAAM,EAAE;MACvBI,KAAK,CAACD,KAAK,CAAC,GAAGF,IAAI,CAACJ,KAAK,GAAGM,KAAK,CAAC;IACpC;IACAA,KAAK,GAAG,CAAC,CAAC;IACV,IAAIG,SAAS,GAAGD,KAAK,CAACR,KAAK,GAAG,CAAC,CAAC;IAChC,OAAO,EAAEM,KAAK,GAAGN,KAAK,EAAE;MACtBS,SAAS,CAACH,KAAK,CAAC,GAAGF,IAAI,CAACE,KAAK,CAAC;IAChC;IACAG,SAAS,CAACT,KAAK,CAAC,GAAGC,SAAS,CAACM,KAAK,CAAC;IACnC,OAAOd,KAAK,CAACM,IAAI,EAAE,IAAI,EAAEU,SAAS,CAAC;EACrC,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGb,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}