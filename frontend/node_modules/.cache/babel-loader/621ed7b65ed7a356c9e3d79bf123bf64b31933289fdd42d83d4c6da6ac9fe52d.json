{"ast": null, "code": "import startOfQuarter from \"../startOfQuarter/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same quarter (and year)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport default function isSameQuarter(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfQuarter = startOfQuarter(dirtyDateLeft);\n  var dateRightStartOfQuarter = startOfQuarter(dirtyDateRight);\n  return dateLeftStartOfQuarter.getTime() === dateRightStartOfQuarter.getTime();\n}", "map": {"version": 3, "names": ["startOfQuarter", "requiredArgs", "isSameQuarter", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfQuarter", "dateRightStartOfQuarter", "getTime"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/isSameQuarter/index.js"], "sourcesContent": ["import startOfQuarter from \"../startOfQuarter/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same quarter (and year)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport default function isSameQuarter(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfQuarter = startOfQuarter(dirtyDateLeft);\n  var dateRightStartOfQuarter = startOfQuarter(dirtyDateRight);\n  return dateLeftStartOfQuarter.getTime() === dateRightStartOfQuarter.getTime();\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,aAAa,EAAEC,cAAc,EAAE;EACnEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,sBAAsB,GAAGN,cAAc,CAACG,aAAa,CAAC;EAC1D,IAAII,uBAAuB,GAAGP,cAAc,CAACI,cAAc,CAAC;EAC5D,OAAOE,sBAAsB,CAACE,OAAO,CAAC,CAAC,KAAKD,uBAAuB,CAACC,OAAO,CAAC,CAAC;AAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}