{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { quartersInYear } from \"../constants/index.js\";\n/**\n * @name yearsToQuarters\n * @category Conversion Helpers\n * @summary Convert years to quarters.\n *\n * @description\n * Convert a number of years to a full number of quarters.\n *\n * @param {number} years - number of years to be converted\n *\n * @returns {number} the number of years converted in quarters\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 years to quarters\n * const result = yearsToQuarters(2)\n * //=> 8\n */\nexport default function yearsToQuarters(years) {\n  requiredArgs(1, arguments);\n  return Math.floor(years * quartersInYear);\n}", "map": {"version": 3, "names": ["requiredArgs", "quartersInYear", "yearsToQuarters", "years", "arguments", "Math", "floor"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/yearsToQuarters/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { quartersInYear } from \"../constants/index.js\";\n/**\n * @name yearsToQuarters\n * @category Conversion Helpers\n * @summary Convert years to quarters.\n *\n * @description\n * Convert a number of years to a full number of quarters.\n *\n * @param {number} years - number of years to be converted\n *\n * @returns {number} the number of years converted in quarters\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 years to quarters\n * const result = yearsToQuarters(2)\n * //=> 8\n */\nexport default function yearsToQuarters(years) {\n  requiredArgs(1, arguments);\n  return Math.floor(years * quartersInYear);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,cAAc,QAAQ,uBAAuB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC7CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAGF,cAAc,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}