{"ast": null, "code": "import _objectSpread from\"/Volumes/2tb/github/test-scrap/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{CogIcon,ServerIcon,ClockIcon,ShieldCheckIcon,CheckCircleIcon,XCircleIcon,ExclamationTriangleIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SystemConfiguration=_ref=>{let{onSaveConfig}=_ref;const[config,setConfig]=useState({database:{connectionString:'mongodb://localhost:27017/supermarket_scraper',status:'connected'},api:{rateLimitPerMinute:60,timeout:30},scheduler:{cronExpression:'0 2 * * *',// Daily at 2 AM\ntimezone:'America/Argentina/Buenos_Aires'},markets:{coto:{maxPages:10,delayBetweenRequests:2000,userAgent:'Mozilla/5.0 (compatible; SupermarketScraper/1.0)'},carrefour:{maxPages:15,delayBetweenRequests:1500,userAgent:'Mozilla/5.0 (compatible; SupermarketScraper/1.0)'},jumbo:{maxPages:12,delayBetweenRequests:2000,userAgent:'Mozilla/5.0 (compatible; SupermarketScraper/1.0)'},disco:{maxPages:8,delayBetweenRequests:2500,userAgent:'Mozilla/5.0 (compatible; SupermarketScraper/1.0)'},vea:{maxPages:10,delayBetweenRequests:2000,userAgent:'Mozilla/5.0 (compatible; SupermarketScraper/1.0)'}}});const[hasChanges,setHasChanges]=useState(false);const updateConfig=(path,value)=>{const newConfig=_objectSpread({},config);let current=newConfig;for(let i=0;i<path.length-1;i++){current=current[path[i]];}current[path[path.length-1]]=value;setConfig(newConfig);setHasChanges(true);};const handleSave=()=>{onSaveConfig(config);setHasChanges(false);};const getStatusIcon=status=>{switch(status){case'connected':return/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-5 w-5 text-green-500\"});case'error':return/*#__PURE__*/_jsx(XCircleIcon,{className:\"h-5 w-5 text-red-500\"});default:return/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"h-5 w-5 text-yellow-500\"});}};const marketDisplayNames={coto:'Coto Digital',carrefour:'Carrefour',jumbo:'Jumbo',disco:'Disco',vea:'Vea'};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row sm:items-center sm:justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-medium text-gray-900\",children:\"System Configuration\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"Configure database, API settings, and scraper parameters\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 sm:mt-0\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:handleSave,disabled:!hasChanges,className:\"btn \".concat(hasChanges?'btn-primary':'btn-outline'),children:[/*#__PURE__*/_jsx(CogIcon,{className:\"h-4 w-4 mr-2\"}),\"Save Configuration\"]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ServerIcon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Database Configuration\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body space-y-4\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Connection String\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[getStatusIcon(config.database.status),/*#__PURE__*/_jsx(\"span\",{className:\"ml-1 text-sm text-gray-600 capitalize\",children:config.database.status})]})]}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:config.database.connectionString,onChange:e=>updateConfig(['database','connectionString'],e.target.value),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",placeholder:\"mongodb://localhost:27017/supermarket_scraper\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"API Configuration\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body space-y-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Rate Limit (requests/minute)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:\"10\",max:\"200\",value:config.api.rateLimitPerMinute,onChange:e=>updateConfig(['api','rateLimitPerMinute'],parseInt(e.target.value)),className:\"w-full\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-xs text-gray-500 mt-1\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"10\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:config.api.rateLimitPerMinute}),/*#__PURE__*/_jsx(\"span\",{children:\"200\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Timeout (seconds)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:config.api.timeout,onChange:e=>updateConfig(['api','timeout'],parseInt(e.target.value)),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",min:\"5\",max:\"300\"})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Scheduler Configuration\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body space-y-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Cron Expression\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:config.scheduler.cronExpression,onChange:e=>updateConfig(['scheduler','cronExpression'],e.target.value),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",placeholder:\"0 2 * * *\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"Current: Daily at 2:00 AM\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Timezone\"}),/*#__PURE__*/_jsxs(\"select\",{value:config.scheduler.timezone,onChange:e=>updateConfig(['scheduler','timezone'],e.target.value),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"America/Argentina/Buenos_Aires\",children:\"Buenos Aires\"}),/*#__PURE__*/_jsx(\"option\",{value:\"UTC\",children:\"UTC\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/New_York\",children:\"New York\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Europe/London\",children:\"London\"})]})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Market-Specific Parameters\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:Object.entries(config.markets).map(_ref2=>{let[market,marketConfig]=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:\"border border-gray-200 rounded-lg p-4\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-gray-900 mb-3\",children:marketDisplayNames[market]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-gray-700 mb-1\",children:\"Max Pages\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:marketConfig.maxPages,onChange:e=>updateConfig(['markets',market,'maxPages'],parseInt(e.target.value)),className:\"w-full text-sm border border-gray-300 rounded-md px-2 py-1\",min:\"1\",max:\"100\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-gray-700 mb-1\",children:\"Delay (ms)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:marketConfig.delayBetweenRequests,onChange:e=>updateConfig(['markets',market,'delayBetweenRequests'],parseInt(e.target.value)),className:\"w-full text-sm border border-gray-300 rounded-md px-2 py-1\",min:\"500\",max:\"10000\",step:\"500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-gray-700 mb-1\",children:\"User Agent\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:marketConfig.userAgent,onChange:e=>updateConfig(['markets',market,'userAgent'],e.target.value),className:\"w-full text-sm border border-gray-300 rounded-md px-2 py-1\"})]})]})]},market);})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"h-4 w-4 mr-1\"}),\"Configuration Persistence Coming Soon - v2.0\"]})})]});};export default SystemConfiguration;", "map": {"version": 3, "names": ["React", "useState", "CogIcon", "ServerIcon", "ClockIcon", "ShieldCheckIcon", "CheckCircleIcon", "XCircleIcon", "ExclamationTriangleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "SystemConfiguration", "_ref", "onSaveConfig", "config", "setConfig", "database", "connectionString", "status", "api", "rateLimitPerMinute", "timeout", "scheduler", "cronExpression", "timezone", "markets", "coto", "maxPages", "delayBetweenRequests", "userAgent", "carrefour", "jumbo", "disco", "vea", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "updateConfig", "path", "value", "newConfig", "_objectSpread", "current", "i", "length", "handleSave", "getStatusIcon", "className", "marketDisplayNames", "children", "onClick", "disabled", "concat", "type", "onChange", "e", "target", "placeholder", "min", "max", "parseInt", "Object", "entries", "map", "_ref2", "market", "marketConfig", "step"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/SystemConfiguration.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  CogIcon,\n  ServerIcon,\n  ClockIcon,\n  ShieldCheckIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface SystemConfigurationProps {\n  onSaveConfig: (config: any) => void;\n}\n\nconst SystemConfiguration: React.FC<SystemConfigurationProps> = ({\n  onSaveConfig,\n}) => {\n  const [config, setConfig] = useState({\n    database: {\n      connectionString: 'mongodb://localhost:27017/supermarket_scraper',\n      status: 'connected',\n    },\n    api: {\n      rateLimitPerMinute: 60,\n      timeout: 30,\n    },\n    scheduler: {\n      cronExpression: '0 2 * * *', // Daily at 2 AM\n      timezone: 'America/Argentina/Buenos_Aires',\n    },\n    markets: {\n      coto: {\n        maxPages: 10,\n        delayBetweenRequests: 2000,\n        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',\n      },\n      carrefour: {\n        maxPages: 15,\n        delayBetweenRequests: 1500,\n        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',\n      },\n      jumbo: {\n        maxPages: 12,\n        delayBetweenRequests: 2000,\n        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',\n      },\n      disco: {\n        maxPages: 8,\n        delayBetweenRequests: 2500,\n        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',\n      },\n      vea: {\n        maxPages: 10,\n        delayBetweenRequests: 2000,\n        userAgent: 'Mozilla/5.0 (compatible; SupermarketScraper/1.0)',\n      },\n    },\n  });\n\n  const [hasChanges, setHasChanges] = useState(false);\n\n  const updateConfig = (path: string[], value: any) => {\n    const newConfig = { ...config };\n    let current: any = newConfig;\n\n    for (let i = 0; i < path.length - 1; i++) {\n      current = current[path[i]];\n    }\n    current[path[path.length - 1]] = value;\n\n    setConfig(newConfig);\n    setHasChanges(true);\n  };\n\n  const handleSave = () => {\n    onSaveConfig(config);\n    setHasChanges(false);\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'connected':\n        return <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />;\n      case 'error':\n        return <XCircleIcon className=\"h-5 w-5 text-red-500\" />;\n      default:\n        return <ExclamationTriangleIcon className=\"h-5 w-5 text-yellow-500\" />;\n    }\n  };\n\n  const marketDisplayNames = {\n    coto: 'Coto Digital',\n    carrefour: 'Carrefour',\n    jumbo: 'Jumbo',\n    disco: 'Disco',\n    vea: 'Vea',\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <h2 className=\"text-lg font-medium text-gray-900\">System Configuration</h2>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Configure database, API settings, and scraper parameters\n          </p>\n        </div>\n        <div className=\"mt-4 sm:mt-0\">\n          <button\n            onClick={handleSave}\n            disabled={!hasChanges}\n            className={`btn ${hasChanges ? 'btn-primary' : 'btn-outline'}`}\n          >\n            <CogIcon className=\"h-4 w-4 mr-2\" />\n            Save Configuration\n          </button>\n        </div>\n      </div>\n\n      {/* Database Configuration */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div className=\"flex items-center\">\n            <ServerIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">Database Configuration</h3>\n          </div>\n        </div>\n        <div className=\"card-body space-y-4\">\n          <div>\n            <div className=\"flex items-center justify-between mb-2\">\n              <label className=\"block text-sm font-medium text-gray-700\">\n                Connection String\n              </label>\n              <div className=\"flex items-center\">\n                {getStatusIcon(config.database.status)}\n                <span className=\"ml-1 text-sm text-gray-600 capitalize\">\n                  {config.database.status}\n                </span>\n              </div>\n            </div>\n            <input\n              type=\"text\"\n              value={config.database.connectionString}\n              onChange={(e) => updateConfig(['database', 'connectionString'], e.target.value)}\n              className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n              placeholder=\"mongodb://localhost:27017/supermarket_scraper\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* API Configuration */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div className=\"flex items-center\">\n            <ShieldCheckIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">API Configuration</h3>\n          </div>\n        </div>\n        <div className=\"card-body space-y-4\">\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Rate Limit (requests/minute)\n              </label>\n              <input\n                type=\"range\"\n                min=\"10\"\n                max=\"200\"\n                value={config.api.rateLimitPerMinute}\n                onChange={(e) => updateConfig(['api', 'rateLimitPerMinute'], parseInt(e.target.value))}\n                className=\"w-full\"\n              />\n              <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                <span>10</span>\n                <span className=\"font-medium\">{config.api.rateLimitPerMinute}</span>\n                <span>200</span>\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Timeout (seconds)\n              </label>\n              <input\n                type=\"number\"\n                value={config.api.timeout}\n                onChange={(e) => updateConfig(['api', 'timeout'], parseInt(e.target.value))}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                min=\"5\"\n                max=\"300\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scheduler Configuration */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div className=\"flex items-center\">\n            <ClockIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">Scheduler Configuration</h3>\n          </div>\n        </div>\n        <div className=\"card-body space-y-4\">\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Cron Expression\n              </label>\n              <input\n                type=\"text\"\n                value={config.scheduler.cronExpression}\n                onChange={(e) => updateConfig(['scheduler', 'cronExpression'], e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                placeholder=\"0 2 * * *\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Current: Daily at 2:00 AM\n              </p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Timezone\n              </label>\n              <select\n                value={config.scheduler.timezone}\n                onChange={(e) => updateConfig(['scheduler', 'timezone'], e.target.value)}\n                className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n              >\n                <option value=\"America/Argentina/Buenos_Aires\">Buenos Aires</option>\n                <option value=\"UTC\">UTC</option>\n                <option value=\"America/New_York\">New York</option>\n                <option value=\"Europe/London\">London</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Market-Specific Configuration */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Market-Specific Parameters</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"space-y-6\">\n            {Object.entries(config.markets).map(([market, marketConfig]) => (\n              <div key={market} className=\"border border-gray-200 rounded-lg p-4\">\n                <h4 className=\"text-sm font-medium text-gray-900 mb-3\">\n                  {marketDisplayNames[market]}\n                </h4>\n                <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n                  <div>\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                      Max Pages\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={marketConfig.maxPages}\n                      onChange={(e) => updateConfig(['markets', market, 'maxPages'], parseInt(e.target.value))}\n                      className=\"w-full text-sm border border-gray-300 rounded-md px-2 py-1\"\n                      min=\"1\"\n                      max=\"100\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                      Delay (ms)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={marketConfig.delayBetweenRequests}\n                      onChange={(e) => updateConfig(['markets', market, 'delayBetweenRequests'], parseInt(e.target.value))}\n                      className=\"w-full text-sm border border-gray-300 rounded-md px-2 py-1\"\n                      min=\"500\"\n                      max=\"10000\"\n                      step=\"500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                      User Agent\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={marketConfig.userAgent}\n                      onChange={(e) => updateConfig(['markets', market, 'userAgent'], e.target.value)}\n                      className=\"w-full text-sm border border-gray-300 rounded-md px-2 py-1\"\n                    />\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Coming Soon Badge */}\n      <div className=\"text-center\">\n        <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n          <ClockIcon className=\"h-4 w-4 mr-1\" />\n          Configuration Persistence Coming Soon - v2.0\n        </span>\n      </div>\n    </div>\n  );\n};\n\nexport default SystemConfiguration;\n"], "mappings": "4HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,OAAO,CACPC,UAAU,CACVC,SAAS,CACTC,eAAe,CACfC,eAAe,CACfC,WAAW,CACXC,uBAAuB,KAClB,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMrC,KAAM,CAAAC,mBAAuD,CAAGC,IAAA,EAE1D,IAF2D,CAC/DC,YACF,CAAC,CAAAD,IAAA,CACC,KAAM,CAACE,MAAM,CAAEC,SAAS,CAAC,CAAGhB,QAAQ,CAAC,CACnCiB,QAAQ,CAAE,CACRC,gBAAgB,CAAE,+CAA+C,CACjEC,MAAM,CAAE,WACV,CAAC,CACDC,GAAG,CAAE,CACHC,kBAAkB,CAAE,EAAE,CACtBC,OAAO,CAAE,EACX,CAAC,CACDC,SAAS,CAAE,CACTC,cAAc,CAAE,WAAW,CAAE;AAC7BC,QAAQ,CAAE,gCACZ,CAAC,CACDC,OAAO,CAAE,CACPC,IAAI,CAAE,CACJC,QAAQ,CAAE,EAAE,CACZC,oBAAoB,CAAE,IAAI,CAC1BC,SAAS,CAAE,kDACb,CAAC,CACDC,SAAS,CAAE,CACTH,QAAQ,CAAE,EAAE,CACZC,oBAAoB,CAAE,IAAI,CAC1BC,SAAS,CAAE,kDACb,CAAC,CACDE,KAAK,CAAE,CACLJ,QAAQ,CAAE,EAAE,CACZC,oBAAoB,CAAE,IAAI,CAC1BC,SAAS,CAAE,kDACb,CAAC,CACDG,KAAK,CAAE,CACLL,QAAQ,CAAE,CAAC,CACXC,oBAAoB,CAAE,IAAI,CAC1BC,SAAS,CAAE,kDACb,CAAC,CACDI,GAAG,CAAE,CACHN,QAAQ,CAAE,EAAE,CACZC,oBAAoB,CAAE,IAAI,CAC1BC,SAAS,CAAE,kDACb,CACF,CACF,CAAC,CAAC,CAEF,KAAM,CAACK,UAAU,CAAEC,aAAa,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAEnD,KAAM,CAAAqC,YAAY,CAAGA,CAACC,IAAc,CAAEC,KAAU,GAAK,CACnD,KAAM,CAAAC,SAAS,CAAAC,aAAA,IAAQ1B,MAAM,CAAE,CAC/B,GAAI,CAAA2B,OAAY,CAAGF,SAAS,CAE5B,IAAK,GAAI,CAAAG,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGL,IAAI,CAACM,MAAM,CAAG,CAAC,CAAED,CAAC,EAAE,CAAE,CACxCD,OAAO,CAAGA,OAAO,CAACJ,IAAI,CAACK,CAAC,CAAC,CAAC,CAC5B,CACAD,OAAO,CAACJ,IAAI,CAACA,IAAI,CAACM,MAAM,CAAG,CAAC,CAAC,CAAC,CAAGL,KAAK,CAEtCvB,SAAS,CAACwB,SAAS,CAAC,CACpBJ,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,CAED,KAAM,CAAAS,UAAU,CAAGA,CAAA,GAAM,CACvB/B,YAAY,CAACC,MAAM,CAAC,CACpBqB,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAU,aAAa,CAAI3B,MAAc,EAAK,CACxC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,mBAAOV,IAAA,CAACJ,eAAe,EAAC0C,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC/D,IAAK,OAAO,CACV,mBAAOtC,IAAA,CAACH,WAAW,EAACyC,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACzD,QACE,mBAAOtC,IAAA,CAACF,uBAAuB,EAACwC,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC1E,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAG,CACzBrB,IAAI,CAAE,cAAc,CACpBI,SAAS,CAAE,WAAW,CACtBC,KAAK,CAAE,OAAO,CACdC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,KACP,CAAC,CAED,mBACEvB,KAAA,QAAKoC,SAAS,CAAC,WAAW,CAAAE,QAAA,eAExBtC,KAAA,QAAKoC,SAAS,CAAC,8DAA8D,CAAAE,QAAA,eAC3EtC,KAAA,QAAAsC,QAAA,eACExC,IAAA,OAAIsC,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAC3ExC,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAE,QAAA,CAAC,0DAE1C,CAAG,CAAC,EACD,CAAC,cACNxC,IAAA,QAAKsC,SAAS,CAAC,cAAc,CAAAE,QAAA,cAC3BtC,KAAA,WACEuC,OAAO,CAAEL,UAAW,CACpBM,QAAQ,CAAE,CAAChB,UAAW,CACtBY,SAAS,QAAAK,MAAA,CAASjB,UAAU,CAAG,aAAa,CAAG,aAAa,CAAG,CAAAc,QAAA,eAE/DxC,IAAA,CAACR,OAAO,EAAC8C,SAAS,CAAC,cAAc,CAAE,CAAC,qBAEtC,EAAQ,CAAC,CACN,CAAC,EACH,CAAC,cAGNpC,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAE,QAAA,eACnBxC,IAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1BtC,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChCxC,IAAA,CAACP,UAAU,EAAC6C,SAAS,CAAC,4BAA4B,CAAE,CAAC,cACrDtC,IAAA,OAAIsC,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAC,wBAAsB,CAAI,CAAC,EAC1E,CAAC,CACH,CAAC,cACNxC,IAAA,QAAKsC,SAAS,CAAC,qBAAqB,CAAAE,QAAA,cAClCtC,KAAA,QAAAsC,QAAA,eACEtC,KAAA,QAAKoC,SAAS,CAAC,wCAAwC,CAAAE,QAAA,eACrDxC,IAAA,UAAOsC,SAAS,CAAC,yCAAyC,CAAAE,QAAA,CAAC,mBAE3D,CAAO,CAAC,cACRtC,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAE,QAAA,EAC/BH,aAAa,CAAC/B,MAAM,CAACE,QAAQ,CAACE,MAAM,CAAC,cACtCV,IAAA,SAAMsC,SAAS,CAAC,uCAAuC,CAAAE,QAAA,CACpDlC,MAAM,CAACE,QAAQ,CAACE,MAAM,CACnB,CAAC,EACJ,CAAC,EACH,CAAC,cACNV,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXd,KAAK,CAAExB,MAAM,CAACE,QAAQ,CAACC,gBAAiB,CACxCoC,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,CAAC,UAAU,CAAE,kBAAkB,CAAC,CAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE,CAChFQ,SAAS,CAAC,oDAAoD,CAC9DU,WAAW,CAAC,+CAA+C,CAC5D,CAAC,EACC,CAAC,CACH,CAAC,EACH,CAAC,cAGN9C,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAE,QAAA,eACnBxC,IAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1BtC,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChCxC,IAAA,CAACL,eAAe,EAAC2C,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC1DtC,IAAA,OAAIsC,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAC,mBAAiB,CAAI,CAAC,EACrE,CAAC,CACH,CAAC,cACNxC,IAAA,QAAKsC,SAAS,CAAC,qBAAqB,CAAAE,QAAA,cAClCtC,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAE,QAAA,eACpDtC,KAAA,QAAAsC,QAAA,eACExC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAE,QAAA,CAAC,8BAEhE,CAAO,CAAC,cACRxC,IAAA,UACE4C,IAAI,CAAC,OAAO,CACZK,GAAG,CAAC,IAAI,CACRC,GAAG,CAAC,KAAK,CACTpB,KAAK,CAAExB,MAAM,CAACK,GAAG,CAACC,kBAAmB,CACrCiC,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,CAAC,KAAK,CAAE,oBAAoB,CAAC,CAAEuB,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAC,CAAE,CACvFQ,SAAS,CAAC,QAAQ,CACnB,CAAC,cACFpC,KAAA,QAAKoC,SAAS,CAAC,iDAAiD,CAAAE,QAAA,eAC9DxC,IAAA,SAAAwC,QAAA,CAAM,IAAE,CAAM,CAAC,cACfxC,IAAA,SAAMsC,SAAS,CAAC,aAAa,CAAAE,QAAA,CAAElC,MAAM,CAACK,GAAG,CAACC,kBAAkB,CAAO,CAAC,cACpEZ,IAAA,SAAAwC,QAAA,CAAM,KAAG,CAAM,CAAC,EACb,CAAC,EACH,CAAC,cACNtC,KAAA,QAAAsC,QAAA,eACExC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAE,QAAA,CAAC,mBAEhE,CAAO,CAAC,cACRxC,IAAA,UACE4C,IAAI,CAAC,QAAQ,CACbd,KAAK,CAAExB,MAAM,CAACK,GAAG,CAACE,OAAQ,CAC1BgC,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,CAAC,KAAK,CAAE,SAAS,CAAC,CAAEuB,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAC,CAAE,CAC5EQ,SAAS,CAAC,oDAAoD,CAC9DW,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACV,CAAC,EACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNhD,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAE,QAAA,eACnBxC,IAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1BtC,KAAA,QAAKoC,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChCxC,IAAA,CAACN,SAAS,EAAC4C,SAAS,CAAC,4BAA4B,CAAE,CAAC,cACpDtC,IAAA,OAAIsC,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAC,yBAAuB,CAAI,CAAC,EAC3E,CAAC,CACH,CAAC,cACNxC,IAAA,QAAKsC,SAAS,CAAC,qBAAqB,CAAAE,QAAA,cAClCtC,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAE,QAAA,eACpDtC,KAAA,QAAAsC,QAAA,eACExC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAE,QAAA,CAAC,iBAEhE,CAAO,CAAC,cACRxC,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXd,KAAK,CAAExB,MAAM,CAACQ,SAAS,CAACC,cAAe,CACvC8B,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,CAAC,WAAW,CAAE,gBAAgB,CAAC,CAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE,CAC/EQ,SAAS,CAAC,oDAAoD,CAC9DU,WAAW,CAAC,WAAW,CACxB,CAAC,cACFhD,IAAA,MAAGsC,SAAS,CAAC,4BAA4B,CAAAE,QAAA,CAAC,2BAE1C,CAAG,CAAC,EACD,CAAC,cACNtC,KAAA,QAAAsC,QAAA,eACExC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAE,QAAA,CAAC,UAEhE,CAAO,CAAC,cACRtC,KAAA,WACE4B,KAAK,CAAExB,MAAM,CAACQ,SAAS,CAACE,QAAS,CACjC6B,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,CAAC,WAAW,CAAE,UAAU,CAAC,CAAEkB,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE,CACzEQ,SAAS,CAAC,oDAAoD,CAAAE,QAAA,eAE9DxC,IAAA,WAAQ8B,KAAK,CAAC,gCAAgC,CAAAU,QAAA,CAAC,cAAY,CAAQ,CAAC,cACpExC,IAAA,WAAQ8B,KAAK,CAAC,KAAK,CAAAU,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChCxC,IAAA,WAAQ8B,KAAK,CAAC,kBAAkB,CAAAU,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAClDxC,IAAA,WAAQ8B,KAAK,CAAC,eAAe,CAAAU,QAAA,CAAC,QAAM,CAAQ,CAAC,EACvC,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNtC,KAAA,QAAKoC,SAAS,CAAC,MAAM,CAAAE,QAAA,eACnBxC,IAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1BxC,IAAA,OAAIsC,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAC,4BAA0B,CAAI,CAAC,CAC9E,CAAC,cACNxC,IAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,cACxBxC,IAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAE,QAAA,CACvBY,MAAM,CAACC,OAAO,CAAC/C,MAAM,CAACW,OAAO,CAAC,CAACqC,GAAG,CAACC,KAAA,MAAC,CAACC,MAAM,CAAEC,YAAY,CAAC,CAAAF,KAAA,oBACzDrD,KAAA,QAAkBoC,SAAS,CAAC,uCAAuC,CAAAE,QAAA,eACjExC,IAAA,OAAIsC,SAAS,CAAC,wCAAwC,CAAAE,QAAA,CACnDD,kBAAkB,CAACiB,MAAM,CAAC,CACzB,CAAC,cACLtD,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAAE,QAAA,eACpDtC,KAAA,QAAAsC,QAAA,eACExC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAE,QAAA,CAAC,WAEhE,CAAO,CAAC,cACRxC,IAAA,UACE4C,IAAI,CAAC,QAAQ,CACbd,KAAK,CAAE2B,YAAY,CAACtC,QAAS,CAC7B0B,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,CAAC,SAAS,CAAE4B,MAAM,CAAE,UAAU,CAAC,CAAEL,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAC,CAAE,CACzFQ,SAAS,CAAC,4DAA4D,CACtEW,GAAG,CAAC,GAAG,CACPC,GAAG,CAAC,KAAK,CACV,CAAC,EACC,CAAC,cACNhD,KAAA,QAAAsC,QAAA,eACExC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAE,QAAA,CAAC,YAEhE,CAAO,CAAC,cACRxC,IAAA,UACE4C,IAAI,CAAC,QAAQ,CACbd,KAAK,CAAE2B,YAAY,CAACrC,oBAAqB,CACzCyB,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,CAAC,SAAS,CAAE4B,MAAM,CAAE,sBAAsB,CAAC,CAAEL,QAAQ,CAACL,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAC,CAAE,CACrGQ,SAAS,CAAC,4DAA4D,CACtEW,GAAG,CAAC,KAAK,CACTC,GAAG,CAAC,OAAO,CACXQ,IAAI,CAAC,KAAK,CACX,CAAC,EACC,CAAC,cACNxD,KAAA,QAAAsC,QAAA,eACExC,IAAA,UAAOsC,SAAS,CAAC,8CAA8C,CAAAE,QAAA,CAAC,YAEhE,CAAO,CAAC,cACRxC,IAAA,UACE4C,IAAI,CAAC,MAAM,CACXd,KAAK,CAAE2B,YAAY,CAACpC,SAAU,CAC9BwB,QAAQ,CAAGC,CAAC,EAAKlB,YAAY,CAAC,CAAC,SAAS,CAAE4B,MAAM,CAAE,WAAW,CAAC,CAAEV,CAAC,CAACC,MAAM,CAACjB,KAAK,CAAE,CAChFQ,SAAS,CAAC,4DAA4D,CACvE,CAAC,EACC,CAAC,EACH,CAAC,GA3CEkB,MA4CL,CAAC,EACP,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,cAGNxD,IAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1BtC,KAAA,SAAMoC,SAAS,CAAC,+FAA+F,CAAAE,QAAA,eAC7GxC,IAAA,CAACN,SAAS,EAAC4C,SAAS,CAAC,cAAc,CAAE,CAAC,+CAExC,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}