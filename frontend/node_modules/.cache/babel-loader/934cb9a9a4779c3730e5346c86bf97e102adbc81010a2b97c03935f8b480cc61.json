{"ast": null, "code": "import quantile, { quantileIndex } from \"./quantile.js\";\nexport default function median(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\nexport function medianIndex(values, valueof) {\n  return quantileIndex(values, 0.5, valueof);\n}", "map": {"version": 3, "names": ["quantile", "quantileIndex", "median", "values", "valueof", "medianIndex"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-array/src/median.js"], "sourcesContent": ["import quantile, {quantileIndex} from \"./quantile.js\";\n\nexport default function median(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\n\nexport function medianIndex(values, valueof) {\n  return quantileIndex(values, 0.5, valueof);\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,IAAGC,aAAa,QAAO,eAAe;AAErD,eAAe,SAASC,MAAMA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAOJ,QAAQ,CAACG,MAAM,EAAE,GAAG,EAAEC,OAAO,CAAC;AACvC;AAEA,OAAO,SAASC,WAAWA,CAACF,MAAM,EAAEC,OAAO,EAAE;EAC3C,OAAOH,aAAa,CAACE,MAAM,EAAE,GAAG,EAAEC,OAAO,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}