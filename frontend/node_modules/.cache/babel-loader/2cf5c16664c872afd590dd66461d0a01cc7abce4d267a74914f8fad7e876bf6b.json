{"ast": null, "code": "import { interpolate, interpolateRound, piecewise } from \"d3-interpolate\";\nimport { identity } from \"./continuous.js\";\nimport { initInterpolator } from \"./init.js\";\nimport { linearish } from \"./linear.js\";\nimport { loggish } from \"./log.js\";\nimport { copy } from \"./sequential.js\";\nimport { symlogish } from \"./symlog.js\";\nimport { powish } from \"./pow.js\";\nfunction transformer() {\n  var x0 = 0,\n    x1 = 0.5,\n    x2 = 1,\n    s = 1,\n    t0,\n    t1,\n    t2,\n    k10,\n    k21,\n    interpolator = identity,\n    transform,\n    clamp = false,\n    unknown;\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n  scale.clamp = function (_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n  function range(interpolate) {\n    return function (_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = piecewise(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n  scale.range = range(interpolate);\n  scale.rangeRound = range(interpolateRound);\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n  return function (t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\nexport default function diverging() {\n  var scale = linearish(transformer()(identity));\n  scale.copy = function () {\n    return copy(scale, diverging());\n  };\n  return initInterpolator.apply(scale, arguments);\n}\nexport function divergingLog() {\n  var scale = loggish(transformer()).domain([0.1, 1, 10]);\n  scale.copy = function () {\n    return copy(scale, divergingLog()).base(scale.base());\n  };\n  return initInterpolator.apply(scale, arguments);\n}\nexport function divergingSymlog() {\n  var scale = symlogish(transformer());\n  scale.copy = function () {\n    return copy(scale, divergingSymlog()).constant(scale.constant());\n  };\n  return initInterpolator.apply(scale, arguments);\n}\nexport function divergingPow() {\n  var scale = powish(transformer());\n  scale.copy = function () {\n    return copy(scale, divergingPow()).exponent(scale.exponent());\n  };\n  return initInterpolator.apply(scale, arguments);\n}\nexport function divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}", "map": {"version": 3, "names": ["interpolate", "interpolateRound", "piecewise", "identity", "initInterpolator", "linearish", "loggish", "copy", "symlogish", "powish", "transformer", "x0", "x1", "x2", "s", "t0", "t1", "t2", "k10", "k21", "interpolator", "transform", "clamp", "unknown", "scale", "x", "isNaN", "Math", "max", "min", "domain", "_", "arguments", "length", "range", "r0", "r1", "r2", "rangeRound", "t", "diverging", "apply", "divergingLog", "base", "divergingSymlog", "constant", "divergingPow", "exponent", "divergingSqrt"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-scale/src/diverging.js"], "sourcesContent": ["import {interpolate, interpolateRound, piecewise} from \"d3-interpolate\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\nimport {linearish} from \"./linear.js\";\nimport {loggish} from \"./log.js\";\nimport {copy} from \"./sequential.js\";\nimport {symlogish} from \"./symlog.js\";\nimport {powish} from \"./pow.js\";\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 0.5,\n      x2 = 1,\n      s = 1,\n      t0,\n      t1,\n      t2,\n      k10,\n      k21,\n      interpolator = identity,\n      transform,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = piecewise(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n\n  scale.range = range(interpolate);\n\n  scale.rangeRound = range(interpolateRound);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\n\nexport default function diverging() {\n  var scale = linearish(transformer()(identity));\n\n  scale.copy = function() {\n    return copy(scale, diverging());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingLog() {\n  var scale = loggish(transformer()).domain([0.1, 1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, divergingLog()).base(scale.base());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingSymlog() {\n  var scale = symlogish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, divergingSymlog()).constant(scale.constant());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingPow() {\n  var scale = powish(transformer());\n\n  scale.copy = function() {\n    return copy(scale, divergingPow()).exponent(scale.exponent());\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n\nexport function divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}\n"], "mappings": "AAAA,SAAQA,WAAW,EAAEC,gBAAgB,EAAEC,SAAS,QAAO,gBAAgB;AACvE,SAAQC,QAAQ,QAAO,iBAAiB;AACxC,SAAQC,gBAAgB,QAAO,WAAW;AAC1C,SAAQC,SAAS,QAAO,aAAa;AACrC,SAAQC,OAAO,QAAO,UAAU;AAChC,SAAQC,IAAI,QAAO,iBAAiB;AACpC,SAAQC,SAAS,QAAO,aAAa;AACrC,SAAQC,MAAM,QAAO,UAAU;AAE/B,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAIC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,GAAG;IACRC,EAAE,GAAG,CAAC;IACNC,CAAC,GAAG,CAAC;IACLC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,GAAG;IACHC,GAAG;IACHC,YAAY,GAAGjB,QAAQ;IACvBkB,SAAS;IACTC,KAAK,GAAG,KAAK;IACbC,OAAO;EAEX,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGF,OAAO,IAAIE,CAAC,GAAG,GAAG,GAAG,CAAC,CAACA,CAAC,GAAG,CAACJ,SAAS,CAACI,CAAC,CAAC,IAAIT,EAAE,KAAKF,CAAC,GAAGW,CAAC,GAAGX,CAAC,GAAGE,EAAE,GAAGE,GAAG,GAAGC,GAAG,CAAC,EAAEC,YAAY,CAACE,KAAK,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC;EAC/J;EAEAD,KAAK,CAACM,MAAM,GAAG,UAASC,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAI,CAACtB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGkB,CAAC,EAAEhB,EAAE,GAAGM,SAAS,CAACV,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEK,EAAE,GAAGK,SAAS,CAACT,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEK,EAAE,GAAGI,SAAS,CAACR,EAAE,GAAG,CAACA,EAAE,CAAC,EAAEK,GAAG,GAAGH,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEI,GAAG,GAAGH,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEF,CAAC,GAAGE,EAAE,GAAGD,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAES,KAAK,IAAI,CAACb,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACtP,CAAC;EAEDW,KAAK,CAACF,KAAK,GAAG,UAASS,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACC,MAAM,IAAIX,KAAK,GAAG,CAAC,CAACS,CAAC,EAAEP,KAAK,IAAIF,KAAK;EACxD,CAAC;EAEDE,KAAK,CAACJ,YAAY,GAAG,UAASW,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACC,MAAM,IAAIb,YAAY,GAAGW,CAAC,EAAEP,KAAK,IAAIJ,YAAY;EACpE,CAAC;EAED,SAASc,KAAKA,CAAClC,WAAW,EAAE;IAC1B,OAAO,UAAS+B,CAAC,EAAE;MACjB,IAAII,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACd,OAAOL,SAAS,CAACC,MAAM,IAAI,CAACE,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGN,CAAC,EAAEX,YAAY,GAAGlB,SAAS,CAACF,WAAW,EAAE,CAACmC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC,EAAEb,KAAK,IAAI,CAACJ,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,GAAG,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IAClK,CAAC;EACH;EAEAI,KAAK,CAACU,KAAK,GAAGA,KAAK,CAAClC,WAAW,CAAC;EAEhCwB,KAAK,CAACc,UAAU,GAAGJ,KAAK,CAACjC,gBAAgB,CAAC;EAE1CuB,KAAK,CAACD,OAAO,GAAG,UAASQ,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAIV,OAAO,GAAGQ,CAAC,EAAEP,KAAK,IAAID,OAAO;EAC1D,CAAC;EAED,OAAO,UAASgB,CAAC,EAAE;IACjBlB,SAAS,GAAGkB,CAAC,EAAExB,EAAE,GAAGwB,CAAC,CAAC5B,EAAE,CAAC,EAAEK,EAAE,GAAGuB,CAAC,CAAC3B,EAAE,CAAC,EAAEK,EAAE,GAAGsB,CAAC,CAAC1B,EAAE,CAAC,EAAEK,GAAG,GAAGH,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEI,GAAG,GAAGH,EAAE,KAAKC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAIA,EAAE,GAAGD,EAAE,CAAC,EAAEF,CAAC,GAAGE,EAAE,GAAGD,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;IACrJ,OAAOS,KAAK;EACd,CAAC;AACH;AAEA,eAAe,SAASgB,SAASA,CAAA,EAAG;EAClC,IAAIhB,KAAK,GAAGnB,SAAS,CAACK,WAAW,CAAC,CAAC,CAACP,QAAQ,CAAC,CAAC;EAE9CqB,KAAK,CAACjB,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACiB,KAAK,EAAEgB,SAAS,CAAC,CAAC,CAAC;EACjC,CAAC;EAED,OAAOpC,gBAAgB,CAACqC,KAAK,CAACjB,KAAK,EAAEQ,SAAS,CAAC;AACjD;AAEA,OAAO,SAASU,YAAYA,CAAA,EAAG;EAC7B,IAAIlB,KAAK,GAAGlB,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,CAACoB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;EAEvDN,KAAK,CAACjB,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACiB,KAAK,EAAEkB,YAAY,CAAC,CAAC,CAAC,CAACC,IAAI,CAACnB,KAAK,CAACmB,IAAI,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,OAAOvC,gBAAgB,CAACqC,KAAK,CAACjB,KAAK,EAAEQ,SAAS,CAAC;AACjD;AAEA,OAAO,SAASY,eAAeA,CAAA,EAAG;EAChC,IAAIpB,KAAK,GAAGhB,SAAS,CAACE,WAAW,CAAC,CAAC,CAAC;EAEpCc,KAAK,CAACjB,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACiB,KAAK,EAAEoB,eAAe,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACrB,KAAK,CAACqB,QAAQ,CAAC,CAAC,CAAC;EAClE,CAAC;EAED,OAAOzC,gBAAgB,CAACqC,KAAK,CAACjB,KAAK,EAAEQ,SAAS,CAAC;AACjD;AAEA,OAAO,SAASc,YAAYA,CAAA,EAAG;EAC7B,IAAItB,KAAK,GAAGf,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC;EAEjCc,KAAK,CAACjB,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACiB,KAAK,EAAEsB,YAAY,CAAC,CAAC,CAAC,CAACC,QAAQ,CAACvB,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED,OAAO3C,gBAAgB,CAACqC,KAAK,CAACjB,KAAK,EAAEQ,SAAS,CAAC;AACjD;AAEA,OAAO,SAASgB,aAAaA,CAAA,EAAG;EAC9B,OAAOF,YAAY,CAACL,KAAK,CAAC,IAAI,EAAET,SAAS,CAAC,CAACe,QAAQ,CAAC,GAAG,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}