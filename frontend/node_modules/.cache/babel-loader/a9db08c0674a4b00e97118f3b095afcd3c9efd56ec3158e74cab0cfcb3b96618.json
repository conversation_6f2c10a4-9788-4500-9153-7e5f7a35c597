{"ast": null, "code": "import _objectSpread from\"/Volumes/2tb/github/test-scrap/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{CircleStackIcon,ArrowDownTrayIcon,ArrowUpTrayIcon,TrashIcon,WrenchScrewdriverIcon,ClockIcon,ExclamationTriangleIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DataManagement=_ref=>{let{stats,onExportData,onImportData,onCleanupData,onOptimizeDatabase}=_ref;const[showCleanupModal,setShowCleanupModal]=useState(false);const[cleanupOptions,setCleanupOptions]=useState({olderThanDays:30,markets:[],duplicates:true,invalidData:true});const marketDisplayNames={coto:'Coto Digital',carrefour:'Carrefour',jumbo:'Jumbo',disco:'Disco',vea:'Vea'};const handleFileImport=event=>{var _event$target$files;const file=(_event$target$files=event.target.files)===null||_event$target$files===void 0?void 0:_event$target$files[0];if(file){onImportData(file);}};const handleCleanup=()=>{onCleanupData(cleanupOptions);setShowCleanupModal(false);};const formatFileSize=bytes=>{const sizes=['Bytes','KB','MB','GB'];if(bytes===0)return'0 Bytes';const i=Math.floor(Math.log(bytes)/Math.log(1024));return Math.round(bytes/Math.pow(1024,i)*100)/100+' '+sizes[i];};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-medium text-gray-900\",children:\"Data Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"Manage product database, exports, imports, and cleanup operations\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(CircleStackIcon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Database Statistics\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-blue-600\",children:stats.totalProducts.toLocaleString()}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:\"Total Products\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-purple-600\",children:stats.databaseSize}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:\"Database Size\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg font-bold text-green-600\",children:new Date(stats.lastUpdated).toLocaleDateString()}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:\"Last Updated\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-gray-900 mb-3\",children:\"Products by Market\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:Object.entries(stats.productsByMarket).map(_ref2=>{let[market,count]=_ref2;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-700\",children:marketDisplayNames[market]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:count.toLocaleString()}),/*#__PURE__*/_jsx(\"div\",{className:\"w-24 bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-500 h-2 rounded-full\",style:{width:\"\".concat(count/Math.max(...Object.values(stats.productsByMarket))*100,\"%\")}})})]})]},market);})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ArrowDownTrayIcon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Export Data\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-body space-y-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"Export product data in JSON or CSV format for backup or analysis.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onExportData(),className:\"w-full btn btn-outline\",children:[/*#__PURE__*/_jsx(ArrowDownTrayIcon,{className:\"h-4 w-4 mr-2\"}),\"Export All Products\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 gap-2\",children:Object.keys(marketDisplayNames).map(market=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>onExportData(market),className:\"btn btn-outline btn-sm\",children:marketDisplayNames[market]},market))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"h-3 w-3 mr-1\"}),\"Coming Soon\"]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(ArrowUpTrayIcon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Import Data\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-body space-y-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"Import product data from JSON or CSV files. Existing products will be updated.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\",children:[/*#__PURE__*/_jsx(ArrowUpTrayIcon,{className:\"mx-auto h-8 w-8 text-gray-400 mb-2\"}),/*#__PURE__*/_jsxs(\"label\",{className:\"cursor-pointer\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-blue-600 hover:text-blue-500\",children:\"Choose file to upload\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",className:\"hidden\",accept:\".json,.csv\",onChange:handleFileImport})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"JSON or CSV files up to 100MB\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"h-3 w-3 mr-1\"}),\"Coming Soon\"]})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(WrenchScrewdriverIcon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Database Operations\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-body\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-gray-900\",children:\"Cleanup Operations\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"Remove old or invalid data to optimize database performance.\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowCleanupModal(true),className:\"w-full btn btn-outline text-red-600 hover:text-red-700\",children:[/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4 mr-2\"}),\"Configure Cleanup\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-gray-900\",children:\"Database Optimization\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:\"Optimize database indexes and performance for faster queries.\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:onOptimizeDatabase,className:\"w-full btn btn-primary\",children:[/*#__PURE__*/_jsx(WrenchScrewdriverIcon,{className:\"h-4 w-4 mr-2\"}),\"Optimize Database\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-6 text-center\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"h-4 w-4 mr-1\"}),\"Database Operations Coming Soon - v2.0\"]})})]})]}),showCleanupModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"h-6 w-6 text-red-600 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Configure Data Cleanup\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-1\",children:\"Delete data older than (days)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:cleanupOptions.olderThanDays,onChange:e=>setCleanupOptions(_objectSpread(_objectSpread({},cleanupOptions),{},{olderThanDays:parseInt(e.target.value)})),className:\"w-full border border-gray-300 rounded-md px-3 py-2\",min:\"1\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700 mb-2\",children:\"Markets to clean\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:Object.entries(marketDisplayNames).map(_ref3=>{let[market,name]=_ref3;return/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:cleanupOptions.markets.includes(market),onChange:e=>{if(e.target.checked){setCleanupOptions(_objectSpread(_objectSpread({},cleanupOptions),{},{markets:[...cleanupOptions.markets,market]}));}else{setCleanupOptions(_objectSpread(_objectSpread({},cleanupOptions),{},{markets:cleanupOptions.markets.filter(m=>m!==market)}));}},className:\"mr-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-700\",children:name})]},market);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:cleanupOptions.duplicates,onChange:e=>setCleanupOptions(_objectSpread(_objectSpread({},cleanupOptions),{},{duplicates:e.target.checked})),className:\"mr-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-700\",children:\"Remove duplicate products\"})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:cleanupOptions.invalidData,onChange:e=>setCleanupOptions(_objectSpread(_objectSpread({},cleanupOptions),{},{invalidData:e.target.checked})),className:\"mr-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-700\",children:\"Remove invalid data\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 mt-6\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowCleanupModal(false),className:\"btn btn-outline\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleCleanup,className:\"btn btn-primary bg-red-600 hover:bg-red-700\",children:\"Start Cleanup\"})]})]})})})]});};export default DataManagement;", "map": {"version": 3, "names": ["React", "useState", "CircleStackIcon", "ArrowDownTrayIcon", "ArrowUpTrayIcon", "TrashIcon", "WrenchScrewdriverIcon", "ClockIcon", "ExclamationTriangleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "DataManagement", "_ref", "stats", "onExportData", "onImportData", "onCleanupData", "onOptimizeDatabase", "showCleanupModal", "setShowCleanupModal", "cleanupOptions", "setCleanupOptions", "older<PERSON><PERSON><PERSON><PERSON>", "markets", "duplicates", "invalidData", "marketDisplayNames", "coto", "carrefour", "jumbo", "disco", "vea", "handleFileImport", "event", "_event$target$files", "file", "target", "files", "handleCleanup", "formatFileSize", "bytes", "sizes", "i", "Math", "floor", "log", "round", "pow", "className", "children", "totalProducts", "toLocaleString", "databaseSize", "Date", "lastUpdated", "toLocaleDateString", "Object", "entries", "productsByMarket", "map", "_ref2", "market", "count", "style", "width", "concat", "max", "values", "onClick", "keys", "type", "accept", "onChange", "value", "e", "_objectSpread", "parseInt", "min", "_ref3", "name", "checked", "includes", "filter", "m"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/DataManagement.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  CircleStackIcon,\n  ArrowDownTrayIcon,\n  ArrowUpTrayIcon,\n  TrashIcon,\n  WrenchScrewdriverIcon,\n  ClockIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\nimport { DatabaseStats, MarketName } from '../../types';\n\ninterface DataManagementProps {\n  stats: DatabaseStats;\n  onExportData: (market?: MarketName) => void;\n  onImportData: (file: File) => void;\n  onCleanupData: (options: any) => void;\n  onOptimizeDatabase: () => void;\n}\n\nconst DataManagement: React.FC<DataManagementProps> = ({\n  stats,\n  onExportData,\n  onImportData,\n  onCleanupData,\n  onOptimizeDatabase,\n}) => {\n  const [showCleanupModal, setShowCleanupModal] = useState(false);\n  const [cleanupOptions, setCleanupOptions] = useState({\n    olderThanDays: 30,\n    markets: [] as MarketName[],\n    duplicates: true,\n    invalidData: true,\n  });\n\n  const marketDisplayNames: Record<string, string> = {\n    coto: 'Coto Digital',\n    carrefour: 'Carrefour',\n    jumbo: 'Jumbo',\n    disco: 'Disco',\n    vea: 'Vea',\n  };\n\n  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      onImportData(file);\n    }\n  };\n\n  const handleCleanup = () => {\n    onCleanupData(cleanupOptions);\n    setShowCleanupModal(false);\n  };\n\n  const formatFileSize = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h2 className=\"text-lg font-medium text-gray-900\">Data Management</h2>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          Manage product database, exports, imports, and cleanup operations\n        </p>\n      </div>\n\n      {/* Database Statistics */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div className=\"flex items-center\">\n            <CircleStackIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">Database Statistics</h3>\n          </div>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {/* Total Products */}\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-600\">\n                {stats.totalProducts.toLocaleString()}\n              </div>\n              <div className=\"text-sm text-gray-500\">Total Products</div>\n            </div>\n\n            {/* Database Size */}\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-purple-600\">\n                {stats.databaseSize}\n              </div>\n              <div className=\"text-sm text-gray-500\">Database Size</div>\n            </div>\n\n            {/* Last Updated */}\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-green-600\">\n                {new Date(stats.lastUpdated).toLocaleDateString()}\n              </div>\n              <div className=\"text-sm text-gray-500\">Last Updated</div>\n            </div>\n          </div>\n\n          {/* Products by Market */}\n          <div className=\"mt-6\">\n            <h4 className=\"text-sm font-medium text-gray-900 mb-3\">Products by Market</h4>\n            <div className=\"space-y-3\">\n              {Object.entries(stats.productsByMarket).map(([market, count]) => (\n                <div key={market} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-700\">\n                    {marketDisplayNames[market as MarketName]}\n                  </span>\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-sm font-medium text-gray-900\">\n                      {count.toLocaleString()}\n                    </span>\n                    <div className=\"w-24 bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-blue-500 h-2 rounded-full\"\n                        style={{\n                          width: `${(count / Math.max(...Object.values(stats.productsByMarket))) * 100}%`\n                        }}\n                      />\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Export/Import Operations */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Export Data */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <div className=\"flex items-center\">\n              <ArrowDownTrayIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n              <h3 className=\"text-lg font-medium text-gray-900\">Export Data</h3>\n            </div>\n          </div>\n          <div className=\"card-body space-y-4\">\n            <p className=\"text-sm text-gray-600\">\n              Export product data in JSON or CSV format for backup or analysis.\n            </p>\n            \n            <div className=\"space-y-3\">\n              <button\n                onClick={() => onExportData()}\n                className=\"w-full btn btn-outline\"\n              >\n                <ArrowDownTrayIcon className=\"h-4 w-4 mr-2\" />\n                Export All Products\n              </button>\n              \n              <div className=\"grid grid-cols-2 gap-2\">\n                {Object.keys(marketDisplayNames).map((market) => (\n                  <button\n                    key={market}\n                    onClick={() => onExportData(market as MarketName)}\n                    className=\"btn btn-outline btn-sm\"\n                  >\n                    {marketDisplayNames[market as MarketName]}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                <ClockIcon className=\"h-3 w-3 mr-1\" />\n                Coming Soon\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Import Data */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <div className=\"flex items-center\">\n              <ArrowUpTrayIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n              <h3 className=\"text-lg font-medium text-gray-900\">Import Data</h3>\n            </div>\n          </div>\n          <div className=\"card-body space-y-4\">\n            <p className=\"text-sm text-gray-600\">\n              Import product data from JSON or CSV files. Existing products will be updated.\n            </p>\n            \n            <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center\">\n              <ArrowUpTrayIcon className=\"mx-auto h-8 w-8 text-gray-400 mb-2\" />\n              <label className=\"cursor-pointer\">\n                <span className=\"text-sm font-medium text-blue-600 hover:text-blue-500\">\n                  Choose file to upload\n                </span>\n                <input\n                  type=\"file\"\n                  className=\"hidden\"\n                  accept=\".json,.csv\"\n                  onChange={handleFileImport}\n                />\n              </label>\n              <p className=\"text-xs text-gray-500 mt-1\">\n                JSON or CSV files up to 100MB\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                <ClockIcon className=\"h-3 w-3 mr-1\" />\n                Coming Soon\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Database Operations */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div className=\"flex items-center\">\n            <WrenchScrewdriverIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">Database Operations</h3>\n          </div>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* Cleanup Operations */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-sm font-medium text-gray-900\">Cleanup Operations</h4>\n              <p className=\"text-sm text-gray-600\">\n                Remove old or invalid data to optimize database performance.\n              </p>\n              <button\n                onClick={() => setShowCleanupModal(true)}\n                className=\"w-full btn btn-outline text-red-600 hover:text-red-700\"\n              >\n                <TrashIcon className=\"h-4 w-4 mr-2\" />\n                Configure Cleanup\n              </button>\n            </div>\n\n            {/* Optimization */}\n            <div className=\"space-y-4\">\n              <h4 className=\"text-sm font-medium text-gray-900\">Database Optimization</h4>\n              <p className=\"text-sm text-gray-600\">\n                Optimize database indexes and performance for faster queries.\n              </p>\n              <button\n                onClick={onOptimizeDatabase}\n                className=\"w-full btn btn-primary\"\n              >\n                <WrenchScrewdriverIcon className=\"h-4 w-4 mr-2\" />\n                Optimize Database\n              </button>\n            </div>\n          </div>\n\n          <div className=\"mt-6 text-center\">\n            <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n              <ClockIcon className=\"h-4 w-4 mr-1\" />\n              Database Operations Coming Soon - v2.0\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Cleanup Modal */}\n      {showCleanupModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <div className=\"flex items-center mb-4\">\n                <ExclamationTriangleIcon className=\"h-6 w-6 text-red-600 mr-2\" />\n                <h3 className=\"text-lg font-medium text-gray-900\">Configure Data Cleanup</h3>\n              </div>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Delete data older than (days)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={cleanupOptions.olderThanDays}\n                    onChange={(e) => setCleanupOptions({\n                      ...cleanupOptions,\n                      olderThanDays: parseInt(e.target.value)\n                    })}\n                    className=\"w-full border border-gray-300 rounded-md px-3 py-2\"\n                    min=\"1\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Markets to clean\n                  </label>\n                  <div className=\"space-y-2\">\n                    {Object.entries(marketDisplayNames).map(([market, name]) => (\n                      <label key={market} className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={cleanupOptions.markets.includes(market as MarketName)}\n                          onChange={(e) => {\n                            if (e.target.checked) {\n                              setCleanupOptions({\n                                ...cleanupOptions,\n                                markets: [...cleanupOptions.markets, market as MarketName]\n                              });\n                            } else {\n                              setCleanupOptions({\n                                ...cleanupOptions,\n                                markets: cleanupOptions.markets.filter(m => m !== market)\n                              });\n                            }\n                          }}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm text-gray-700\">{name}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={cleanupOptions.duplicates}\n                      onChange={(e) => setCleanupOptions({\n                        ...cleanupOptions,\n                        duplicates: e.target.checked\n                      })}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm text-gray-700\">Remove duplicate products</span>\n                  </label>\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={cleanupOptions.invalidData}\n                      onChange={(e) => setCleanupOptions({\n                        ...cleanupOptions,\n                        invalidData: e.target.checked\n                      })}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm text-gray-700\">Remove invalid data</span>\n                  </label>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={() => setShowCleanupModal(false)}\n                  className=\"btn btn-outline\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleCleanup}\n                  className=\"btn btn-primary bg-red-600 hover:bg-red-700\"\n                >\n                  Start Cleanup\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DataManagement;\n"], "mappings": "4HAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,eAAe,CACfC,iBAAiB,CACjBC,eAAe,CACfC,SAAS,CACTC,qBAAqB,CACrBC,SAAS,CACTC,uBAAuB,KAClB,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWrC,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAMhD,IANiD,CACrDC,KAAK,CACLC,YAAY,CACZC,YAAY,CACZC,aAAa,CACbC,kBACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAACM,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACqB,cAAc,CAAEC,iBAAiB,CAAC,CAAGtB,QAAQ,CAAC,CACnDuB,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,EAAkB,CAC3BC,UAAU,CAAE,IAAI,CAChBC,WAAW,CAAE,IACf,CAAC,CAAC,CAEF,KAAM,CAAAC,kBAA0C,CAAG,CACjDC,IAAI,CAAE,cAAc,CACpBC,SAAS,CAAE,WAAW,CACtBC,KAAK,CAAE,OAAO,CACdC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,KACP,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,KAA0C,EAAK,KAAAC,mBAAA,CACvE,KAAM,CAAAC,IAAI,EAAAD,mBAAA,CAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,UAAAH,mBAAA,iBAAlBA,mBAAA,CAAqB,CAAC,CAAC,CACpC,GAAIC,IAAI,CAAE,CACRpB,YAAY,CAACoB,IAAI,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAG,aAAa,CAAGA,CAAA,GAAM,CAC1BtB,aAAa,CAACI,cAAc,CAAC,CAC7BD,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAoB,cAAc,CAAIC,KAAa,EAAK,CACxC,KAAM,CAAAC,KAAK,CAAG,CAAC,OAAO,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACzC,GAAID,KAAK,GAAK,CAAC,CAAE,MAAO,SAAS,CACjC,KAAM,CAAAE,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACL,KAAK,CAAC,CAAGG,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC,CAAC,CACtD,MAAO,CAAAF,IAAI,CAACG,KAAK,CAACN,KAAK,CAAGG,IAAI,CAACI,GAAG,CAAC,IAAI,CAAEL,CAAC,CAAC,CAAG,GAAG,CAAC,CAAG,GAAG,CAAG,GAAG,CAAGD,KAAK,CAACC,CAAC,CAAC,CAC3E,CAAC,CAED,mBACEhC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBvC,KAAA,QAAAuC,QAAA,eACEzC,IAAA,OAAIwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACtEzC,IAAA,MAAGwC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mEAE1C,CAAG,CAAC,EACD,CAAC,cAGNvC,KAAA,QAAKsC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzC,IAAA,QAAKwC,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BvC,KAAA,QAAKsC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCzC,IAAA,CAACR,eAAe,EAACgD,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC1DxC,IAAA,OAAIwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,EACvE,CAAC,CACH,CAAC,cACNvC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvC,KAAA,QAAKsC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAEnEvC,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzC,IAAA,QAAKwC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC9CpC,KAAK,CAACqC,aAAa,CAACC,cAAc,CAAC,CAAC,CAClC,CAAC,cACN3C,IAAA,QAAKwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gBAAc,CAAK,CAAC,EACxD,CAAC,cAGNvC,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzC,IAAA,QAAKwC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChDpC,KAAK,CAACuC,YAAY,CAChB,CAAC,cACN5C,IAAA,QAAKwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,eAAa,CAAK,CAAC,EACvD,CAAC,cAGNvC,KAAA,QAAKsC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzC,IAAA,QAAKwC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC9C,GAAI,CAAAI,IAAI,CAACxC,KAAK,CAACyC,WAAW,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAC9C,CAAC,cACN/C,IAAA,QAAKwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,cAAY,CAAK,CAAC,EACtD,CAAC,EACH,CAAC,cAGNvC,KAAA,QAAKsC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzC,IAAA,OAAIwC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC9EzC,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBO,MAAM,CAACC,OAAO,CAAC5C,KAAK,CAAC6C,gBAAgB,CAAC,CAACC,GAAG,CAACC,KAAA,MAAC,CAACC,MAAM,CAAEC,KAAK,CAAC,CAAAF,KAAA,oBAC1DlD,KAAA,QAAkBsC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC7DzC,IAAA,SAAMwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACpCvB,kBAAkB,CAACmC,MAAM,CAAe,CACrC,CAAC,cACPnD,KAAA,QAAKsC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CzC,IAAA,SAAMwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAChDa,KAAK,CAACX,cAAc,CAAC,CAAC,CACnB,CAAC,cACP3C,IAAA,QAAKwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChDzC,IAAA,QACEwC,SAAS,CAAC,8BAA8B,CACxCe,KAAK,CAAE,CACLC,KAAK,IAAAC,MAAA,CAAMH,KAAK,CAAGnB,IAAI,CAACuB,GAAG,CAAC,GAAGV,MAAM,CAACW,MAAM,CAACtD,KAAK,CAAC6C,gBAAgB,CAAC,CAAC,CAAI,GAAG,KAC9E,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,GAhBEG,MAiBL,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNnD,KAAA,QAAKsC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDvC,KAAA,QAAKsC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzC,IAAA,QAAKwC,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BvC,KAAA,QAAKsC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCzC,IAAA,CAACP,iBAAiB,EAAC+C,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC5DxC,IAAA,OAAIwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,EAC/D,CAAC,CACH,CAAC,cACNvC,KAAA,QAAKsC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCzC,IAAA,MAAGwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,mEAErC,CAAG,CAAC,cAEJvC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvC,KAAA,WACE0D,OAAO,CAAEA,CAAA,GAAMtD,YAAY,CAAC,CAAE,CAC9BkC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAElCzC,IAAA,CAACP,iBAAiB,EAAC+C,SAAS,CAAC,cAAc,CAAE,CAAC,sBAEhD,EAAQ,CAAC,cAETxC,IAAA,QAAKwC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CACpCO,MAAM,CAACa,IAAI,CAAC3C,kBAAkB,CAAC,CAACiC,GAAG,CAAEE,MAAM,eAC1CrD,IAAA,WAEE4D,OAAO,CAAEA,CAAA,GAAMtD,YAAY,CAAC+C,MAAoB,CAAE,CAClDb,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAEjCvB,kBAAkB,CAACmC,MAAM,CAAe,EAJpCA,MAKC,CACT,CAAC,CACC,CAAC,EACH,CAAC,cAENrD,IAAA,QAAKwC,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BvC,KAAA,SAAMsC,SAAS,CAAC,+FAA+F,CAAAC,QAAA,eAC7GzC,IAAA,CAACH,SAAS,EAAC2C,SAAS,CAAC,cAAc,CAAE,CAAC,cAExC,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,cAGNtC,KAAA,QAAKsC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzC,IAAA,QAAKwC,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BvC,KAAA,QAAKsC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCzC,IAAA,CAACN,eAAe,EAAC8C,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAC1DxC,IAAA,OAAIwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,EAC/D,CAAC,CACH,CAAC,cACNvC,KAAA,QAAKsC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCzC,IAAA,MAAGwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gFAErC,CAAG,CAAC,cAEJvC,KAAA,QAAKsC,SAAS,CAAC,mEAAmE,CAAAC,QAAA,eAChFzC,IAAA,CAACN,eAAe,EAAC8C,SAAS,CAAC,oCAAoC,CAAE,CAAC,cAClEtC,KAAA,UAAOsC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC/BzC,IAAA,SAAMwC,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,uBAExE,CAAM,CAAC,cACPzC,IAAA,UACE8D,IAAI,CAAC,MAAM,CACXtB,SAAS,CAAC,QAAQ,CAClBuB,MAAM,CAAC,YAAY,CACnBC,QAAQ,CAAExC,gBAAiB,CAC5B,CAAC,EACG,CAAC,cACRxB,IAAA,MAAGwC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,+BAE1C,CAAG,CAAC,EACD,CAAC,cAENzC,IAAA,QAAKwC,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BvC,KAAA,SAAMsC,SAAS,CAAC,+FAA+F,CAAAC,QAAA,eAC7GzC,IAAA,CAACH,SAAS,EAAC2C,SAAS,CAAC,cAAc,CAAE,CAAC,cAExC,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNtC,KAAA,QAAKsC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzC,IAAA,QAAKwC,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BvC,KAAA,QAAKsC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCzC,IAAA,CAACJ,qBAAqB,EAAC4C,SAAS,CAAC,4BAA4B,CAAE,CAAC,cAChExC,IAAA,OAAIwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,EACvE,CAAC,CACH,CAAC,cACNvC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvC,KAAA,QAAKsC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDvC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBzC,IAAA,OAAIwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cACzEzC,IAAA,MAAGwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,8DAErC,CAAG,CAAC,cACJvC,KAAA,WACE0D,OAAO,CAAEA,CAAA,GAAMjD,mBAAmB,CAAC,IAAI,CAAE,CACzC6B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eAElEzC,IAAA,CAACL,SAAS,EAAC6C,SAAS,CAAC,cAAc,CAAE,CAAC,oBAExC,EAAQ,CAAC,EACN,CAAC,cAGNtC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBzC,IAAA,OAAIwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAC5EzC,IAAA,MAAGwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,+DAErC,CAAG,CAAC,cACJvC,KAAA,WACE0D,OAAO,CAAEnD,kBAAmB,CAC5B+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAElCzC,IAAA,CAACJ,qBAAqB,EAAC4C,SAAS,CAAC,cAAc,CAAE,CAAC,oBAEpD,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAENxC,IAAA,QAAKwC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BvC,KAAA,SAAMsC,SAAS,CAAC,+FAA+F,CAAAC,QAAA,eAC7GzC,IAAA,CAACH,SAAS,EAAC2C,SAAS,CAAC,cAAc,CAAE,CAAC,yCAExC,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,CAGL9B,gBAAgB,eACfV,IAAA,QAAKwC,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFzC,IAAA,QAAKwC,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFvC,KAAA,QAAKsC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBvC,KAAA,QAAKsC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCzC,IAAA,CAACF,uBAAuB,EAAC0C,SAAS,CAAC,2BAA2B,CAAE,CAAC,cACjExC,IAAA,OAAIwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,EAC1E,CAAC,cAENvC,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvC,KAAA,QAAAuC,QAAA,eACEzC,IAAA,UAAOwC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,+BAEhE,CAAO,CAAC,cACRzC,IAAA,UACE8D,IAAI,CAAC,QAAQ,CACbG,KAAK,CAAErD,cAAc,CAACE,aAAc,CACpCkD,QAAQ,CAAGE,CAAC,EAAKrD,iBAAiB,CAAAsD,aAAA,CAAAA,aAAA,IAC7BvD,cAAc,MACjBE,aAAa,CAAEsD,QAAQ,CAACF,CAAC,CAACtC,MAAM,CAACqC,KAAK,CAAC,EACxC,CAAE,CACHzB,SAAS,CAAC,oDAAoD,CAC9D6B,GAAG,CAAC,GAAG,CACR,CAAC,EACC,CAAC,cAENnE,KAAA,QAAAuC,QAAA,eACEzC,IAAA,UAAOwC,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kBAEhE,CAAO,CAAC,cACRzC,IAAA,QAAKwC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBO,MAAM,CAACC,OAAO,CAAC/B,kBAAkB,CAAC,CAACiC,GAAG,CAACmB,KAAA,MAAC,CAACjB,MAAM,CAAEkB,IAAI,CAAC,CAAAD,KAAA,oBACrDpE,KAAA,UAAoBsC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/CzC,IAAA,UACE8D,IAAI,CAAC,UAAU,CACfU,OAAO,CAAE5D,cAAc,CAACG,OAAO,CAAC0D,QAAQ,CAACpB,MAAoB,CAAE,CAC/DW,QAAQ,CAAGE,CAAC,EAAK,CACf,GAAIA,CAAC,CAACtC,MAAM,CAAC4C,OAAO,CAAE,CACpB3D,iBAAiB,CAAAsD,aAAA,CAAAA,aAAA,IACZvD,cAAc,MACjBG,OAAO,CAAE,CAAC,GAAGH,cAAc,CAACG,OAAO,CAAEsC,MAAM,CAAe,EAC3D,CAAC,CACJ,CAAC,IAAM,CACLxC,iBAAiB,CAAAsD,aAAA,CAAAA,aAAA,IACZvD,cAAc,MACjBG,OAAO,CAAEH,cAAc,CAACG,OAAO,CAAC2D,MAAM,CAACC,CAAC,EAAIA,CAAC,GAAKtB,MAAM,CAAC,EAC1D,CAAC,CACJ,CACF,CAAE,CACFb,SAAS,CAAC,MAAM,CACjB,CAAC,cACFxC,IAAA,SAAMwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE8B,IAAI,CAAO,CAAC,GAnB3ClB,MAoBL,CAAC,EACT,CAAC,CACC,CAAC,EACH,CAAC,cAENnD,KAAA,QAAKsC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvC,KAAA,UAAOsC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAClCzC,IAAA,UACE8D,IAAI,CAAC,UAAU,CACfU,OAAO,CAAE5D,cAAc,CAACI,UAAW,CACnCgD,QAAQ,CAAGE,CAAC,EAAKrD,iBAAiB,CAAAsD,aAAA,CAAAA,aAAA,IAC7BvD,cAAc,MACjBI,UAAU,CAAEkD,CAAC,CAACtC,MAAM,CAAC4C,OAAO,EAC7B,CAAE,CACHhC,SAAS,CAAC,MAAM,CACjB,CAAC,cACFxC,IAAA,SAAMwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,2BAAyB,CAAM,CAAC,EACnE,CAAC,cACRvC,KAAA,UAAOsC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAClCzC,IAAA,UACE8D,IAAI,CAAC,UAAU,CACfU,OAAO,CAAE5D,cAAc,CAACK,WAAY,CACpC+C,QAAQ,CAAGE,CAAC,EAAKrD,iBAAiB,CAAAsD,aAAA,CAAAA,aAAA,IAC7BvD,cAAc,MACjBK,WAAW,CAAEiD,CAAC,CAACtC,MAAM,CAAC4C,OAAO,EAC9B,CAAE,CACHhC,SAAS,CAAC,MAAM,CACjB,CAAC,cACFxC,IAAA,SAAMwC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,qBAAmB,CAAM,CAAC,EAC7D,CAAC,EACL,CAAC,EACH,CAAC,cAENvC,KAAA,QAAKsC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CzC,IAAA,WACE4D,OAAO,CAAEA,CAAA,GAAMjD,mBAAmB,CAAC,KAAK,CAAE,CAC1C6B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC5B,QAED,CAAQ,CAAC,cACTzC,IAAA,WACE4D,OAAO,CAAE9B,aAAc,CACvBU,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CACxD,eAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}