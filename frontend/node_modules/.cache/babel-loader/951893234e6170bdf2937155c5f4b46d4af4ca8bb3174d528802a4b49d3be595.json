{"ast": null, "code": "var cloneArrayBuffer = require('./_cloneArrayBuffer'),\n  cloneDataView = require('./_cloneDataView'),\n  cloneRegExp = require('./_cloneRegExp'),\n  cloneSymbol = require('./_cloneSymbol'),\n  cloneTypedArray = require('./_cloneTypedArray');\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n  dateTag = '[object Date]',\n  mapTag = '[object Map]',\n  numberTag = '[object Number]',\n  regexpTag = '[object RegExp]',\n  setTag = '[object Set]',\n  stringTag = '[object String]',\n  symbolTag = '[object Symbol]';\nvar arrayBufferTag = '[object ArrayBuffer]',\n  dataViewTag = '[object DataView]',\n  float32Tag = '[object Float32Array]',\n  float64Tag = '[object Float64Array]',\n  int8Tag = '[object Int8Array]',\n  int16Tag = '[object Int16Array]',\n  int32Tag = '[object Int32Array]',\n  uint8Tag = '[object Uint8Array]',\n  uint8ClampedTag = '[object Uint8ClampedArray]',\n  uint16Tag = '[object Uint16Array]',\n  uint32Tag = '[object Uint32Array]';\n\n/**\n * Initializes an object clone based on its `toStringTag`.\n *\n * **Note:** This function only supports cloning values with tags of\n * `Boolean`, `Date`, `Error`, `Map`, `Number`, `RegExp`, `Set`, or `String`.\n *\n * @private\n * @param {Object} object The object to clone.\n * @param {string} tag The `toStringTag` of the object to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneByTag(object, tag, isDeep) {\n  var Ctor = object.constructor;\n  switch (tag) {\n    case arrayBufferTag:\n      return cloneArrayBuffer(object);\n    case boolTag:\n    case dateTag:\n      return new Ctor(+object);\n    case dataViewTag:\n      return cloneDataView(object, isDeep);\n    case float32Tag:\n    case float64Tag:\n    case int8Tag:\n    case int16Tag:\n    case int32Tag:\n    case uint8Tag:\n    case uint8ClampedTag:\n    case uint16Tag:\n    case uint32Tag:\n      return cloneTypedArray(object, isDeep);\n    case mapTag:\n      return new Ctor();\n    case numberTag:\n    case stringTag:\n      return new Ctor(object);\n    case regexpTag:\n      return cloneRegExp(object);\n    case setTag:\n      return new Ctor();\n    case symbolTag:\n      return cloneSymbol(object);\n  }\n}\nmodule.exports = initCloneByTag;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "cloneDataView", "cloneRegExp", "cloneSymbol", "cloneTypedArray", "boolTag", "dateTag", "mapTag", "numberTag", "regexpTag", "setTag", "stringTag", "symbolTag", "arrayBufferTag", "dataViewTag", "float32Tag", "float64Tag", "int8Tag", "int16Tag", "int32Tag", "uint8Tag", "uint8ClampedTag", "uint16Tag", "uint32Tag", "initCloneByTag", "object", "tag", "isDeep", "Ctor", "constructor", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_initCloneByTag.js"], "sourcesContent": ["var cloneArrayBuffer = require('./_cloneArrayBuffer'),\n    cloneDataView = require('./_cloneDataView'),\n    cloneRegExp = require('./_cloneRegExp'),\n    cloneSymbol = require('./_cloneSymbol'),\n    cloneTypedArray = require('./_cloneTypedArray');\n\n/** `Object#toString` result references. */\nvar boolTag = '[object Boolean]',\n    dateTag = '[object Date]',\n    mapTag = '[object Map]',\n    numberTag = '[object Number]',\n    regexpTag = '[object RegExp]',\n    setTag = '[object Set]',\n    stringTag = '[object String]',\n    symbolTag = '[object Symbol]';\n\nvar arrayBufferTag = '[object ArrayBuffer]',\n    dataViewTag = '[object DataView]',\n    float32Tag = '[object Float32Array]',\n    float64Tag = '[object Float64Array]',\n    int8Tag = '[object Int8Array]',\n    int16Tag = '[object Int16Array]',\n    int32Tag = '[object Int32Array]',\n    uint8Tag = '[object Uint8Array]',\n    uint8ClampedTag = '[object Uint8ClampedArray]',\n    uint16Tag = '[object Uint16Array]',\n    uint32Tag = '[object Uint32Array]';\n\n/**\n * Initializes an object clone based on its `toStringTag`.\n *\n * **Note:** This function only supports cloning values with tags of\n * `Boolean`, `Date`, `Error`, `Map`, `Number`, `RegExp`, `Set`, or `String`.\n *\n * @private\n * @param {Object} object The object to clone.\n * @param {string} tag The `toStringTag` of the object to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneByTag(object, tag, isDeep) {\n  var Ctor = object.constructor;\n  switch (tag) {\n    case arrayBufferTag:\n      return cloneArrayBuffer(object);\n\n    case boolTag:\n    case dateTag:\n      return new Ctor(+object);\n\n    case dataViewTag:\n      return cloneDataView(object, isDeep);\n\n    case float32Tag: case float64Tag:\n    case int8Tag: case int16Tag: case int32Tag:\n    case uint8Tag: case uint8ClampedTag: case uint16Tag: case uint32Tag:\n      return cloneTypedArray(object, isDeep);\n\n    case mapTag:\n      return new Ctor;\n\n    case numberTag:\n    case stringTag:\n      return new Ctor(object);\n\n    case regexpTag:\n      return cloneRegExp(object);\n\n    case setTag:\n      return new Ctor;\n\n    case symbolTag:\n      return cloneSymbol(object);\n  }\n}\n\nmodule.exports = initCloneByTag;\n"], "mappings": "AAAA,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,qBAAqB,CAAC;EACjDC,aAAa,GAAGD,OAAO,CAAC,kBAAkB,CAAC;EAC3CE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;EACvCG,WAAW,GAAGH,OAAO,CAAC,gBAAgB,CAAC;EACvCI,eAAe,GAAGJ,OAAO,CAAC,oBAAoB,CAAC;;AAEnD;AACA,IAAIK,OAAO,GAAG,kBAAkB;EAC5BC,OAAO,GAAG,eAAe;EACzBC,MAAM,GAAG,cAAc;EACvBC,SAAS,GAAG,iBAAiB;EAC7BC,SAAS,GAAG,iBAAiB;EAC7BC,MAAM,GAAG,cAAc;EACvBC,SAAS,GAAG,iBAAiB;EAC7BC,SAAS,GAAG,iBAAiB;AAEjC,IAAIC,cAAc,GAAG,sBAAsB;EACvCC,WAAW,GAAG,mBAAmB;EACjCC,UAAU,GAAG,uBAAuB;EACpCC,UAAU,GAAG,uBAAuB;EACpCC,OAAO,GAAG,oBAAoB;EAC9BC,QAAQ,GAAG,qBAAqB;EAChCC,QAAQ,GAAG,qBAAqB;EAChCC,QAAQ,GAAG,qBAAqB;EAChCC,eAAe,GAAG,4BAA4B;EAC9CC,SAAS,GAAG,sBAAsB;EAClCC,SAAS,GAAG,sBAAsB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC3C,IAAIC,IAAI,GAAGH,MAAM,CAACI,WAAW;EAC7B,QAAQH,GAAG;IACT,KAAKb,cAAc;MACjB,OAAOd,gBAAgB,CAAC0B,MAAM,CAAC;IAEjC,KAAKpB,OAAO;IACZ,KAAKC,OAAO;MACV,OAAO,IAAIsB,IAAI,CAAC,CAACH,MAAM,CAAC;IAE1B,KAAKX,WAAW;MACd,OAAOb,aAAa,CAACwB,MAAM,EAAEE,MAAM,CAAC;IAEtC,KAAKZ,UAAU;IAAE,KAAKC,UAAU;IAChC,KAAKC,OAAO;IAAE,KAAKC,QAAQ;IAAE,KAAKC,QAAQ;IAC1C,KAAKC,QAAQ;IAAE,KAAKC,eAAe;IAAE,KAAKC,SAAS;IAAE,KAAKC,SAAS;MACjE,OAAOnB,eAAe,CAACqB,MAAM,EAAEE,MAAM,CAAC;IAExC,KAAKpB,MAAM;MACT,OAAO,IAAIqB,IAAI,CAAD,CAAC;IAEjB,KAAKpB,SAAS;IACd,KAAKG,SAAS;MACZ,OAAO,IAAIiB,IAAI,CAACH,MAAM,CAAC;IAEzB,KAAKhB,SAAS;MACZ,OAAOP,WAAW,CAACuB,MAAM,CAAC;IAE5B,KAAKf,MAAM;MACT,OAAO,IAAIkB,IAAI,CAAD,CAAC;IAEjB,KAAKhB,SAAS;MACZ,OAAOT,WAAW,CAACsB,MAAM,CAAC;EAC9B;AACF;AAEAK,MAAM,CAACC,OAAO,GAAGP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}