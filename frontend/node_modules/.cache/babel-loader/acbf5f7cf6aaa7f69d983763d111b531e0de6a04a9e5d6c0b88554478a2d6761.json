{"ast": null, "code": "/**\n * @fileOverview Area Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Area } from '../cartesian/Area';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var AreaChart = generateCategoricalChart({\n  chartName: 'AreaChart',\n  GraphicalChild: Area,\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }],\n  formatAxisMap: formatAxisMap\n});", "map": {"version": 3, "names": ["generateCategoricalChart", "Area", "XAxis", "YA<PERSON>s", "formatAxisMap", "AreaChart", "chartName", "GraphicalChild", "axisComponents", "axisType", "AxisComp"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/chart/AreaChart.js"], "sourcesContent": ["/**\n * @fileOverview Area Chart\n */\nimport { generateCategoricalChart } from './generateCategoricalChart';\nimport { Area } from '../cartesian/Area';\nimport { XAxis } from '../cartesian/XAxis';\nimport { YAxis } from '../cartesian/YAxis';\nimport { formatAxisMap } from '../util/CartesianUtils';\nexport var AreaChart = generateCategoricalChart({\n  chartName: 'AreaChart',\n  GraphicalChild: Area,\n  axisComponents: [{\n    axisType: 'xAxis',\n    AxisComp: XAxis\n  }, {\n    axisType: 'yAxis',\n    AxisComp: YAxis\n  }],\n  formatAxisMap: formatAxisMap\n});"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAO,IAAIC,SAAS,GAAGL,wBAAwB,CAAC;EAC9CM,SAAS,EAAE,WAAW;EACtBC,cAAc,EAAEN,IAAI;EACpBO,cAAc,EAAE,CAAC;IACfC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAER;EACZ,CAAC,EAAE;IACDO,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAEP;EACZ,CAAC,CAAC;EACFC,aAAa,EAAEA;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}