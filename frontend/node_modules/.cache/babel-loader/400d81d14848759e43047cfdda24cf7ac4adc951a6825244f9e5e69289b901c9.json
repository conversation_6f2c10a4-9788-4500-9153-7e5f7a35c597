{"ast": null, "code": "var _excluded = [\"option\", \"isActive\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nimport * as React from 'react';\nimport { Symbols } from '../shape/Symbols';\nimport { Shape } from './ActiveShapeUtils';\nexport function ScatterSymbol(_ref) {\n  var option = _ref.option,\n    isActive = _ref.isActive,\n    props = _objectWithoutProperties(_ref, _excluded);\n  if (typeof option === 'string') {\n    return /*#__PURE__*/React.createElement(Shape, _extends({\n      option: /*#__PURE__*/React.createElement(Symbols, _extends({\n        type: option\n      }, props)),\n      isActive: isActive,\n      shapeType: \"symbols\"\n    }, props));\n  }\n  return /*#__PURE__*/React.createElement(Shape, _extends({\n    option: option,\n    isActive: isActive,\n    shapeType: \"symbols\"\n  }, props));\n}", "map": {"version": 3, "names": ["_excluded", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "_objectWithoutProperties", "excluded", "_objectWithoutPropertiesLoose", "getOwnPropertySymbols", "sourceSymbolKeys", "indexOf", "propertyIsEnumerable", "React", "Symbols", "<PERSON><PERSON><PERSON>", "ScatterSymbol", "_ref", "option", "isActive", "props", "createElement", "type", "shapeType"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/util/ScatterUtils.js"], "sourcesContent": ["var _excluded = [\"option\", \"isActive\"];\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nimport * as React from 'react';\nimport { Symbols } from '../shape/Symbols';\nimport { Shape } from './ActiveShapeUtils';\nexport function ScatterSymbol(_ref) {\n  var option = _ref.option,\n    isActive = _ref.isActive,\n    props = _objectWithoutProperties(_ref, _excluded);\n  if (typeof option === 'string') {\n    return /*#__PURE__*/React.createElement(Shape, _extends({\n      option: /*#__PURE__*/React.createElement(Symbols, _extends({\n        type: option\n      }, props)),\n      isActive: isActive,\n      shapeType: \"symbols\"\n    }, props));\n  }\n  return /*#__PURE__*/React.createElement(Shape, _extends({\n    option: option,\n    isActive: isActive,\n    shapeType: \"symbols\"\n  }, props));\n}"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;AACtC,SAASC,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AAAE;AAClV,SAASQ,wBAAwBA,CAACN,MAAM,EAAEO,QAAQ,EAAE;EAAE,IAAIP,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAGY,6BAA6B,CAACR,MAAM,EAAEO,QAAQ,CAAC;EAAE,IAAIN,GAAG,EAAEJ,CAAC;EAAE,IAAIJ,MAAM,CAACgB,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGjB,MAAM,CAACgB,qBAAqB,CAACT,MAAM,CAAC;IAAE,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,gBAAgB,CAACX,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAEI,GAAG,GAAGS,gBAAgB,CAACb,CAAC,CAAC;MAAE,IAAIU,QAAQ,CAACI,OAAO,CAACV,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACR,MAAM,CAACS,SAAS,CAACU,oBAAoB,CAACR,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AAC3e,SAASY,6BAA6BA,CAACR,MAAM,EAAEO,QAAQ,EAAE;EAAE,IAAIP,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIJ,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIK,GAAG,IAAID,MAAM,EAAE;IAAE,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;MAAE,IAAIM,QAAQ,CAACI,OAAO,CAACV,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE;AACtR,OAAO,KAAKiB,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACtBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,KAAK,GAAGd,wBAAwB,CAACW,IAAI,EAAE1B,SAAS,CAAC;EACnD,IAAI,OAAO2B,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO,aAAaL,KAAK,CAACQ,aAAa,CAACN,KAAK,EAAEvB,QAAQ,CAAC;MACtD0B,MAAM,EAAE,aAAaL,KAAK,CAACQ,aAAa,CAACP,OAAO,EAAEtB,QAAQ,CAAC;QACzD8B,IAAI,EAAEJ;MACR,CAAC,EAAEE,KAAK,CAAC,CAAC;MACVD,QAAQ,EAAEA,QAAQ;MAClBI,SAAS,EAAE;IACb,CAAC,EAAEH,KAAK,CAAC,CAAC;EACZ;EACA,OAAO,aAAaP,KAAK,CAACQ,aAAa,CAACN,KAAK,EAAEvB,QAAQ,CAAC;IACtD0B,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClBI,SAAS,EAAE;EACb,CAAC,EAAEH,KAAK,CAAC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}