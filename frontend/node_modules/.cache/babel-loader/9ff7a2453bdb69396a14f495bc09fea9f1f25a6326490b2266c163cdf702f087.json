{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n// From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns\n// | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n// |----------|-------|----|-------|-------|-------|\n// | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n// | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n// | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n// | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n// | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\nexport var YearParser = /*#__PURE__*/function (_Parser) {\n  _inherits(YearParser, _Parser);\n  var _super = _createSuper(YearParser);\n  function YearParser() {\n    var _this;\n    _classCallCheck(this, YearParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 130);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['Y', 'R', 'u', 'w', 'I', 'i', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(YearParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      var valueCallback = function valueCallback(year) {\n        return {\n          year: year,\n          isTwoDigitYear: token === 'yy'\n        };\n      };\n      switch (token) {\n        case 'y':\n          return mapValue(parseNDigits(4, dateString), valueCallback);\n        case 'yo':\n          return mapValue(match.ordinalNumber(dateString, {\n            unit: 'year'\n          }), valueCallback);\n        default:\n          return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value.isTwoDigitYear || value.year > 0;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, flags, value) {\n      var currentYear = date.getUTCFullYear();\n      if (value.isTwoDigitYear) {\n        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);\n        date.setUTCFullYear(normalizedTwoDigitYear, 0, 1);\n        date.setUTCHours(0, 0, 0, 0);\n        return date;\n      }\n      var year = !('era' in flags) || flags.era === 1 ? value.year : 1 - value.year;\n      date.setUTCFullYear(year, 0, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return YearParser;\n}(Parser);", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "<PERSON><PERSON><PERSON>", "mapValue", "normalizeTwoDigitYear", "parseNDigits", "<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "key", "value", "parse", "dateString", "token", "match", "valueCallback", "year", "isTwoDigitYear", "ordinalNumber", "unit", "validate", "_date", "set", "date", "flags", "currentYear", "getUTCFullYear", "normalizedTwoDigitYear", "setUTCFullYear", "setUTCHours", "era"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/parse/_lib/parsers/YearParser.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n// From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns\n// | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n// |----------|-------|----|-------|-------|-------|\n// | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n// | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n// | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n// | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n// | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\nexport var YearParser = /*#__PURE__*/function (_Parser) {\n  _inherits(YearParser, _Parser);\n  var _super = _createSuper(YearParser);\n  function YearParser() {\n    var _this;\n    _classCallCheck(this, YearParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 130);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['Y', 'R', 'u', 'w', 'I', 'i', 'e', 'c', 't', 'T']);\n    return _this;\n  }\n  _createClass(YearParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token, match) {\n      var valueCallback = function valueCallback(year) {\n        return {\n          year: year,\n          isTwoDigitYear: token === 'yy'\n        };\n      };\n      switch (token) {\n        case 'y':\n          return mapValue(parseNDigits(4, dateString), valueCallback);\n        case 'yo':\n          return mapValue(match.ordinalNumber(dateString, {\n            unit: 'year'\n          }), valueCallback);\n        default:\n          return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      }\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(_date, value) {\n      return value.isTwoDigitYear || value.year > 0;\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, flags, value) {\n      var currentYear = date.getUTCFullYear();\n      if (value.isTwoDigitYear) {\n        var normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);\n        date.setUTCFullYear(normalizedTwoDigitYear, 0, 1);\n        date.setUTCHours(0, 0, 0, 0);\n        return date;\n      }\n      var year = !('era' in flags) || flags.era === 1 ? value.year : 1 - value.year;\n      date.setUTCFullYear(year, 0, 1);\n      date.setUTCHours(0, 0, 0, 0);\n      return date;\n    }\n  }]);\n  return YearParser;\n}(Parser);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,QAAQ,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,aAAa;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,aAAa,UAAUC,OAAO,EAAE;EACtDR,SAAS,CAACO,UAAU,EAAEC,OAAO,CAAC;EAC9B,IAAIC,MAAM,GAAGR,YAAY,CAACM,UAAU,CAAC;EACrC,SAASA,UAAUA,CAAA,EAAG;IACpB,IAAIG,KAAK;IACTb,eAAe,CAAC,IAAI,EAAEU,UAAU,CAAC;IACjC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDZ,eAAe,CAACH,sBAAsB,CAACW,KAAK,CAAC,EAAE,UAAU,EAAE,GAAG,CAAC;IAC/DR,eAAe,CAACH,sBAAsB,CAACW,KAAK,CAAC,EAAE,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACxH,OAAOA,KAAK;EACd;EACAZ,YAAY,CAACS,UAAU,EAAE,CAAC;IACxBa,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;MAC9C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,IAAI,EAAE;QAC/C,OAAO;UACLA,IAAI,EAAEA,IAAI;UACVC,cAAc,EAAEJ,KAAK,KAAK;QAC5B,CAAC;MACH,CAAC;MACD,QAAQA,KAAK;QACX,KAAK,GAAG;UACN,OAAOpB,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAEiB,UAAU,CAAC,EAAEG,aAAa,CAAC;QAC7D,KAAK,IAAI;UACP,OAAOtB,QAAQ,CAACqB,KAAK,CAACI,aAAa,CAACN,UAAU,EAAE;YAC9CO,IAAI,EAAE;UACR,CAAC,CAAC,EAAEJ,aAAa,CAAC;QACpB;UACE,OAAOtB,QAAQ,CAACE,YAAY,CAACkB,KAAK,CAACX,MAAM,EAAEU,UAAU,CAAC,EAAEG,aAAa,CAAC;MAC1E;IACF;EACF,CAAC,EAAE;IACDN,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,SAASU,QAAQA,CAACC,KAAK,EAAEX,KAAK,EAAE;MACrC,OAAOA,KAAK,CAACO,cAAc,IAAIP,KAAK,CAACM,IAAI,GAAG,CAAC;IAC/C;EACF,CAAC,EAAE;IACDP,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASY,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEd,KAAK,EAAE;MACtC,IAAIe,WAAW,GAAGF,IAAI,CAACG,cAAc,CAAC,CAAC;MACvC,IAAIhB,KAAK,CAACO,cAAc,EAAE;QACxB,IAAIU,sBAAsB,GAAGjC,qBAAqB,CAACgB,KAAK,CAACM,IAAI,EAAES,WAAW,CAAC;QAC3EF,IAAI,CAACK,cAAc,CAACD,sBAAsB,EAAE,CAAC,EAAE,CAAC,CAAC;QACjDJ,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,OAAON,IAAI;MACb;MACA,IAAIP,IAAI,GAAG,EAAE,KAAK,IAAIQ,KAAK,CAAC,IAAIA,KAAK,CAACM,GAAG,KAAK,CAAC,GAAGpB,KAAK,CAACM,IAAI,GAAG,CAAC,GAAGN,KAAK,CAACM,IAAI;MAC7EO,IAAI,CAACK,cAAc,CAACZ,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;MAC/BO,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5B,OAAON,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAO3B,UAAU;AACnB,CAAC,CAACJ,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}