{"ast": null, "code": "import React from'react';import{BuildingStorefrontIcon,TagIcon,ClockIcon,ExclamationTriangleIcon,CheckCircleIcon}from'@heroicons/react/24/outline';import{formatDistanceToNow}from'date-fns';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ProductCard=_ref=>{let{product,showMarket=true,showCategory=true,onCompare}=_ref;const getMarketColor=marketName=>{const colors={coto:'bg-red-500',carrefour:'bg-blue-600',jumbo:'bg-orange-500',disco:'bg-green-600',vea:'bg-purple-600'};return colors[marketName.toLowerCase()]||'bg-gray-500';};const hasDiscount=product.price.original&&product.price.original>product.price.current;const discountPercentage=hasDiscount?Math.round((product.price.original-product.price.current)/product.price.original*100):product.discount_percentage;return/*#__PURE__*/_jsx(\"div\",{className:\"card hover:shadow-lg transition-shadow duration-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card-body p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200 mb-4\",children:[product.image_url?/*#__PURE__*/_jsx(\"img\",{src:product.image_url,alt:product.name,className:\"h-full w-full object-cover object-center\",onError:e=>{var _target$nextElementSi;const target=e.target;target.style.display='none';(_target$nextElementSi=target.nextElementSibling)===null||_target$nextElementSi===void 0?void 0:_target$nextElementSi.classList.remove('hidden');}}):null,/*#__PURE__*/_jsx(\"div\",{className:\"\".concat(product.image_url?'hidden':'',\" flex items-center justify-center h-full\"),children:/*#__PURE__*/_jsx(BuildingStorefrontIcon,{className:\"h-12 w-12 text-gray-400\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[showMarket&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-3 w-3 rounded-full \".concat(getMarketColor(product.market))}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium text-gray-600 uppercase\",children:product.market})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[product.availability==='in_stock'?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-4 w-4 text-success-500\"}):/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"h-4 w-4 text-warning-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium \".concat(product.availability==='in_stock'?'text-success-600':'text-warning-600'),children:product.availability==='in_stock'?'Available':product.availability==='out_of_stock'?'Out of Stock':product.availability==='limited_stock'?'Limited Stock':'Unknown'})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900 line-clamp-2\",children:product.name}),product.brand&&/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:product.brand})]}),showCategory&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(TagIcon,{className:\"h-3 w-3 text-gray-400\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:product.category}),product.subcategory&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-400\",children:\"\\u2022\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:product.subcategory})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-lg font-bold text-gray-900\",children:[\"$\",product.price.current.toFixed(2)]}),product.unit&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-xs text-gray-500\",children:[\"/ \",product.unit]})]}),discountPercentage&&discountPercentage>0&&/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800\",children:[\"-\",discountPercentage,\"%\"]})]}),hasDiscount&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-gray-500 line-through\",children:[\"$\",product.price.original.toFixed(2)]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-medium text-success-600\",children:[\"Save $\",(product.price.original-product.price.current).toFixed(2)]})]})]}),product.promotion&&/*#__PURE__*/_jsx(\"div\",{className:\"p-2 bg-warning-50 border border-warning-200 rounded-md\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-warning-800 font-medium\",children:[\"\\uD83C\\uDF89 \",product.promotion]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1 text-xs text-gray-500\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"h-3 w-3\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Updated \",formatDistanceToNow(new Date(product.updated_at),{addSuffix:true})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2 pt-2\",children:[product.product_url&&/*#__PURE__*/_jsx(\"a\",{href:product.product_url,target:\"_blank\",rel:\"noopener noreferrer\",className:\"flex-1 btn btn-outline btn-sm\",children:\"View Product\"}),onCompare&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>onCompare(product),className:\"flex-1 btn btn-primary btn-sm\",children:\"Compare\"})]})]})]})});};export default ProductCard;", "map": {"version": 3, "names": ["React", "BuildingStorefrontIcon", "TagIcon", "ClockIcon", "ExclamationTriangleIcon", "CheckCircleIcon", "formatDistanceToNow", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ProductCard", "_ref", "product", "showMarket", "showCategory", "onCompare", "getMarketColor", "marketName", "colors", "coto", "carrefour", "jumbo", "disco", "vea", "toLowerCase", "hasDiscount", "price", "original", "current", "discountPercentage", "Math", "round", "discount_percentage", "className", "children", "image_url", "src", "alt", "name", "onError", "e", "_target$nextElementSi", "target", "style", "display", "nextElement<PERSON><PERSON>ling", "classList", "remove", "concat", "market", "availability", "brand", "category", "subcategory", "toFixed", "unit", "promotion", "Date", "updated_at", "addSuffix", "product_url", "href", "rel", "onClick"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/src/components/Search/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport {\n  BuildingStorefrontIcon,\n  TagIcon,\n  ClockIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n} from '@heroicons/react/24/outline';\nimport { Product } from '../../types';\nimport { formatDistanceToNow } from 'date-fns';\n\ninterface ProductCardProps {\n  product: Product;\n  showMarket?: boolean;\n  showCategory?: boolean;\n  onCompare?: (product: Product) => void;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({\n  product,\n  showMarket = true,\n  showCategory = true,\n  onCompare,\n}) => {\n  const getMarketColor = (marketName: string) => {\n    const colors: Record<string, string> = {\n      coto: 'bg-red-500',\n      carrefour: 'bg-blue-600',\n      jumbo: 'bg-orange-500',\n      disco: 'bg-green-600',\n      vea: 'bg-purple-600',\n    };\n    return colors[marketName.toLowerCase()] || 'bg-gray-500';\n  };\n\n  const hasDiscount = product.price.original && product.price.original > product.price.current;\n  const discountPercentage = hasDiscount\n    ? Math.round(((product.price.original! - product.price.current) / product.price.original!) * 100)\n    : product.discount_percentage;\n\n  return (\n    <div className=\"card hover:shadow-lg transition-shadow duration-200\">\n      <div className=\"card-body p-4\">\n        {/* Product Image */}\n        <div className=\"aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-lg bg-gray-200 mb-4\">\n          {product.image_url ? (\n            <img\n              src={product.image_url}\n              alt={product.name}\n              className=\"h-full w-full object-cover object-center\"\n              onError={(e) => {\n                const target = e.target as HTMLImageElement;\n                target.style.display = 'none';\n                target.nextElementSibling?.classList.remove('hidden');\n              }}\n            />\n          ) : null}\n          <div className={`${product.image_url ? 'hidden' : ''} flex items-center justify-center h-full`}>\n            <BuildingStorefrontIcon className=\"h-12 w-12 text-gray-400\" />\n          </div>\n        </div>\n\n        {/* Product Info */}\n        <div className=\"space-y-3\">\n          {/* Market and Availability */}\n          <div className=\"flex items-center justify-between\">\n            {showMarket && (\n              <div className=\"flex items-center space-x-2\">\n                <div className={`h-3 w-3 rounded-full ${getMarketColor(product.market)}`}></div>\n                <span className=\"text-xs font-medium text-gray-600 uppercase\">\n                  {product.market}\n                </span>\n              </div>\n            )}\n            <div className=\"flex items-center space-x-1\">\n              {product.availability === 'in_stock' ? (\n                <CheckCircleIcon className=\"h-4 w-4 text-success-500\" />\n              ) : (\n                <ExclamationTriangleIcon className=\"h-4 w-4 text-warning-500\" />\n              )}\n              <span className={`text-xs font-medium ${\n                product.availability === 'in_stock' ? 'text-success-600' : 'text-warning-600'\n              }`}>\n                {product.availability === 'in_stock' ? 'Available' :\n                 product.availability === 'out_of_stock' ? 'Out of Stock' :\n                 product.availability === 'limited_stock' ? 'Limited Stock' : 'Unknown'}\n              </span>\n            </div>\n          </div>\n\n          {/* Product Name */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-900 line-clamp-2\">\n              {product.name}\n            </h3>\n            {product.brand && (\n              <p className=\"text-xs text-gray-500 mt-1\">{product.brand}</p>\n            )}\n          </div>\n\n          {/* Category */}\n          {showCategory && (\n            <div className=\"flex items-center space-x-1\">\n              <TagIcon className=\"h-3 w-3 text-gray-400\" />\n              <span className=\"text-xs text-gray-500\">{product.category}</span>\n              {product.subcategory && (\n                <>\n                  <span className=\"text-xs text-gray-400\">•</span>\n                  <span className=\"text-xs text-gray-500\">{product.subcategory}</span>\n                </>\n              )}\n            </div>\n          )}\n\n          {/* Price */}\n          <div className=\"space-y-1\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-lg font-bold text-gray-900\">\n                  ${product.price.current.toFixed(2)}\n                </span>\n                {product.unit && (\n                  <span className=\"text-xs text-gray-500\">/ {product.unit}</span>\n                )}\n              </div>\n              {discountPercentage && discountPercentage > 0 && (\n                <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800\">\n                  -{discountPercentage}%\n                </span>\n              )}\n            </div>\n            \n            {hasDiscount && (\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-gray-500 line-through\">\n                  ${product.price.original!.toFixed(2)}\n                </span>\n                <span className=\"text-sm font-medium text-success-600\">\n                  Save ${(product.price.original! - product.price.current).toFixed(2)}\n                </span>\n              </div>\n            )}\n          </div>\n\n          {/* Promotion */}\n          {product.promotion && (\n            <div className=\"p-2 bg-warning-50 border border-warning-200 rounded-md\">\n              <p className=\"text-xs text-warning-800 font-medium\">\n                🎉 {product.promotion}\n              </p>\n            </div>\n          )}\n\n          {/* Last Updated */}\n          <div className=\"flex items-center space-x-1 text-xs text-gray-500\">\n            <ClockIcon className=\"h-3 w-3\" />\n            <span>\n              Updated {formatDistanceToNow(new Date(product.updated_at), { addSuffix: true })}\n            </span>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex space-x-2 pt-2\">\n            {product.product_url && (\n              <a\n                href={product.product_url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"flex-1 btn btn-outline btn-sm\"\n              >\n                View Product\n              </a>\n            )}\n            {onCompare && (\n              <button\n                onClick={() => onCompare(product)}\n                className=\"flex-1 btn btn-primary btn-sm\"\n              >\n                Compare\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,OACEC,sBAAsB,CACtBC,OAAO,CACPC,SAAS,CACTC,uBAAuB,CACvBC,eAAe,KACV,6BAA6B,CAEpC,OAASC,mBAAmB,KAAQ,UAAU,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAS/C,KAAM,CAAAC,WAAuC,CAAGC,IAAA,EAK1C,IAL2C,CAC/CC,OAAO,CACPC,UAAU,CAAG,IAAI,CACjBC,YAAY,CAAG,IAAI,CACnBC,SACF,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAAK,cAAc,CAAIC,UAAkB,EAAK,CAC7C,KAAM,CAAAC,MAA8B,CAAG,CACrCC,IAAI,CAAE,YAAY,CAClBC,SAAS,CAAE,aAAa,CACxBC,KAAK,CAAE,eAAe,CACtBC,KAAK,CAAE,cAAc,CACrBC,GAAG,CAAE,eACP,CAAC,CACD,MAAO,CAAAL,MAAM,CAACD,UAAU,CAACO,WAAW,CAAC,CAAC,CAAC,EAAI,aAAa,CAC1D,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGb,OAAO,CAACc,KAAK,CAACC,QAAQ,EAAIf,OAAO,CAACc,KAAK,CAACC,QAAQ,CAAGf,OAAO,CAACc,KAAK,CAACE,OAAO,CAC5F,KAAM,CAAAC,kBAAkB,CAAGJ,WAAW,CAClCK,IAAI,CAACC,KAAK,CAAE,CAACnB,OAAO,CAACc,KAAK,CAACC,QAAQ,CAAIf,OAAO,CAACc,KAAK,CAACE,OAAO,EAAIhB,OAAO,CAACc,KAAK,CAACC,QAAS,CAAI,GAAG,CAAC,CAC/Ff,OAAO,CAACoB,mBAAmB,CAE/B,mBACE3B,IAAA,QAAK4B,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClE3B,KAAA,QAAK0B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5B3B,KAAA,QAAK0B,SAAS,CAAC,0EAA0E,CAAAC,QAAA,EACtFtB,OAAO,CAACuB,SAAS,cAChB9B,IAAA,QACE+B,GAAG,CAAExB,OAAO,CAACuB,SAAU,CACvBE,GAAG,CAAEzB,OAAO,CAAC0B,IAAK,CAClBL,SAAS,CAAC,0CAA0C,CACpDM,OAAO,CAAGC,CAAC,EAAK,KAAAC,qBAAA,CACd,KAAM,CAAAC,MAAM,CAAGF,CAAC,CAACE,MAA0B,CAC3CA,MAAM,CAACC,KAAK,CAACC,OAAO,CAAG,MAAM,CAC7B,CAAAH,qBAAA,CAAAC,MAAM,CAACG,kBAAkB,UAAAJ,qBAAA,iBAAzBA,qBAAA,CAA2BK,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC,CACvD,CAAE,CACH,CAAC,CACA,IAAI,cACR1C,IAAA,QAAK4B,SAAS,IAAAe,MAAA,CAAKpC,OAAO,CAACuB,SAAS,CAAG,QAAQ,CAAG,EAAE,4CAA2C,CAAAD,QAAA,cAC7F7B,IAAA,CAACP,sBAAsB,EAACmC,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC3D,CAAC,EACH,CAAC,cAGN1B,KAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAC/CrB,UAAU,eACTN,KAAA,QAAK0B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C7B,IAAA,QAAK4B,SAAS,yBAAAe,MAAA,CAA0BhC,cAAc,CAACJ,OAAO,CAACqC,MAAM,CAAC,CAAG,CAAM,CAAC,cAChF5C,IAAA,SAAM4B,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CAC1DtB,OAAO,CAACqC,MAAM,CACX,CAAC,EACJ,CACN,cACD1C,KAAA,QAAK0B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EACzCtB,OAAO,CAACsC,YAAY,GAAK,UAAU,cAClC7C,IAAA,CAACH,eAAe,EAAC+B,SAAS,CAAC,0BAA0B,CAAE,CAAC,cAExD5B,IAAA,CAACJ,uBAAuB,EAACgC,SAAS,CAAC,0BAA0B,CAAE,CAChE,cACD5B,IAAA,SAAM4B,SAAS,wBAAAe,MAAA,CACbpC,OAAO,CAACsC,YAAY,GAAK,UAAU,CAAG,kBAAkB,CAAG,kBAAkB,CAC5E,CAAAhB,QAAA,CACAtB,OAAO,CAACsC,YAAY,GAAK,UAAU,CAAG,WAAW,CACjDtC,OAAO,CAACsC,YAAY,GAAK,cAAc,CAAG,cAAc,CACxDtC,OAAO,CAACsC,YAAY,GAAK,eAAe,CAAG,eAAe,CAAG,SAAS,CACnE,CAAC,EACJ,CAAC,EACH,CAAC,cAGN3C,KAAA,QAAA2B,QAAA,eACE7B,IAAA,OAAI4B,SAAS,CAAC,gDAAgD,CAAAC,QAAA,CAC3DtB,OAAO,CAAC0B,IAAI,CACX,CAAC,CACJ1B,OAAO,CAACuC,KAAK,eACZ9C,IAAA,MAAG4B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEtB,OAAO,CAACuC,KAAK,CAAI,CAC7D,EACE,CAAC,CAGLrC,YAAY,eACXP,KAAA,QAAK0B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C7B,IAAA,CAACN,OAAO,EAACkC,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC7C5B,IAAA,SAAM4B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEtB,OAAO,CAACwC,QAAQ,CAAO,CAAC,CAChExC,OAAO,CAACyC,WAAW,eAClB9C,KAAA,CAAAE,SAAA,EAAAyB,QAAA,eACE7B,IAAA,SAAM4B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,cAChD7B,IAAA,SAAM4B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEtB,OAAO,CAACyC,WAAW,CAAO,CAAC,EACpE,CACH,EACE,CACN,cAGD9C,KAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD3B,KAAA,QAAK0B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3B,KAAA,SAAM0B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,EAAC,GAC/C,CAACtB,OAAO,CAACc,KAAK,CAACE,OAAO,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAC9B,CAAC,CACN1C,OAAO,CAAC2C,IAAI,eACXhD,KAAA,SAAM0B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,IAAE,CAACtB,OAAO,CAAC2C,IAAI,EAAO,CAC/D,EACE,CAAC,CACL1B,kBAAkB,EAAIA,kBAAkB,CAAG,CAAC,eAC3CtB,KAAA,SAAM0B,SAAS,CAAC,qGAAqG,CAAAC,QAAA,EAAC,GACnH,CAACL,kBAAkB,CAAC,GACvB,EAAM,CACP,EACE,CAAC,CAELJ,WAAW,eACVlB,KAAA,QAAK0B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3B,KAAA,SAAM0B,SAAS,CAAC,oCAAoC,CAAAC,QAAA,EAAC,GAClD,CAACtB,OAAO,CAACc,KAAK,CAACC,QAAQ,CAAE2B,OAAO,CAAC,CAAC,CAAC,EAChC,CAAC,cACP/C,KAAA,SAAM0B,SAAS,CAAC,sCAAsC,CAAAC,QAAA,EAAC,QAC/C,CAAC,CAACtB,OAAO,CAACc,KAAK,CAACC,QAAQ,CAAIf,OAAO,CAACc,KAAK,CAACE,OAAO,EAAE0B,OAAO,CAAC,CAAC,CAAC,EAC/D,CAAC,EACJ,CACN,EACE,CAAC,CAGL1C,OAAO,CAAC4C,SAAS,eAChBnD,IAAA,QAAK4B,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cACrE3B,KAAA,MAAG0B,SAAS,CAAC,sCAAsC,CAAAC,QAAA,EAAC,eAC/C,CAACtB,OAAO,CAAC4C,SAAS,EACpB,CAAC,CACD,CACN,cAGDjD,KAAA,QAAK0B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChE7B,IAAA,CAACL,SAAS,EAACiC,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC1B,KAAA,SAAA2B,QAAA,EAAM,UACI,CAAC/B,mBAAmB,CAAC,GAAI,CAAAsD,IAAI,CAAC7C,OAAO,CAAC8C,UAAU,CAAC,CAAE,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,EAC3E,CAAC,EACJ,CAAC,cAGNpD,KAAA,QAAK0B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EACjCtB,OAAO,CAACgD,WAAW,eAClBvD,IAAA,MACEwD,IAAI,CAAEjD,OAAO,CAACgD,WAAY,CAC1BlB,MAAM,CAAC,QAAQ,CACfoB,GAAG,CAAC,qBAAqB,CACzB7B,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAC1C,cAED,CAAG,CACJ,CACAnB,SAAS,eACRV,IAAA,WACE0D,OAAO,CAAEA,CAAA,GAAMhD,SAAS,CAACH,OAAO,CAAE,CAClCqB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAC1C,SAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}