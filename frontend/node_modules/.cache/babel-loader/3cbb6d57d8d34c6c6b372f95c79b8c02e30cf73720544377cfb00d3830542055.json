{"ast": null, "code": "/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */\nfunction baseUnary(func) {\n  return function (value) {\n    return func(value);\n  };\n}\nmodule.exports = baseUnary;", "map": {"version": 3, "names": ["baseUnary", "func", "value", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_baseUnary.js"], "sourcesContent": ["/**\n * The base implementation of `_.unary` without support for storing metadata.\n *\n * @private\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n */\nfunction baseUnary(func) {\n  return function(value) {\n    return func(value);\n  };\n}\n\nmodule.exports = baseUnary;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,IAAI,EAAE;EACvB,OAAO,UAASC,KAAK,EAAE;IACrB,OAAOD,IAAI,CAACC,KAAK,CAAC;EACpB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}