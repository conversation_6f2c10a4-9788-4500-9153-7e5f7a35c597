{"ast": null, "code": "var nativeCreate = require('./_nativeCreate');\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = nativeCreate && value === undefined ? HASH_UNDEFINED : value;\n  return this;\n}\nmodule.exports = hashSet;", "map": {"version": 3, "names": ["nativeCreate", "require", "HASH_UNDEFINED", "hashSet", "key", "value", "data", "__data__", "size", "has", "undefined", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_hashSet.js"], "sourcesContent": ["var nativeCreate = require('./_nativeCreate');\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/**\n * Sets the hash `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf Hash\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the hash instance.\n */\nfunction hashSet(key, value) {\n  var data = this.__data__;\n  this.size += this.has(key) ? 0 : 1;\n  data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;\n  return this;\n}\n\nmodule.exports = hashSet;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA,IAAIC,cAAc,GAAG,2BAA2B;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC3B,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ;EACxB,IAAI,CAACC,IAAI,IAAI,IAAI,CAACC,GAAG,CAACL,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EAClCE,IAAI,CAACF,GAAG,CAAC,GAAIJ,YAAY,IAAIK,KAAK,KAAKK,SAAS,GAAIR,cAAc,GAAGG,KAAK;EAC1E,OAAO,IAAI;AACb;AAEAM,MAAM,CAACC,OAAO,GAAGT,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}