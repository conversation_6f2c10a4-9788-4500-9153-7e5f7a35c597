{"ast": null, "code": "export default function variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}", "map": {"version": 3, "names": ["variance", "values", "valueof", "count", "delta", "mean", "sum", "undefined", "value", "index"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-array/src/variance.js"], "sourcesContent": ["export default function variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAChD,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK;EACT,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIJ,OAAO,KAAKK,SAAS,EAAE;IACzB,KAAK,IAAIC,KAAK,IAAIP,MAAM,EAAE;MACxB,IAAIO,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAC9CJ,KAAK,GAAGI,KAAK,GAAGH,IAAI;QACpBA,IAAI,IAAID,KAAK,GAAG,EAAED,KAAK;QACvBG,GAAG,IAAIF,KAAK,IAAII,KAAK,GAAGH,IAAI,CAAC;MAC/B;IACF;EACF,CAAC,MAAM;IACL,IAAII,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAID,KAAK,IAAIP,MAAM,EAAE;MACxB,IAAI,CAACO,KAAK,GAAGN,OAAO,CAACM,KAAK,EAAE,EAAEC,KAAK,EAAER,MAAM,CAAC,KAAK,IAAI,IAAI,CAACO,KAAK,GAAG,CAACA,KAAK,KAAKA,KAAK,EAAE;QAClFJ,KAAK,GAAGI,KAAK,GAAGH,IAAI;QACpBA,IAAI,IAAID,KAAK,GAAG,EAAED,KAAK;QACvBG,GAAG,IAAIF,KAAK,IAAII,KAAK,GAAGH,IAAI,CAAC;MAC/B;IACF;EACF;EACA,IAAIF,KAAK,GAAG,CAAC,EAAE,OAAOG,GAAG,IAAIH,KAAK,GAAG,CAAC,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}