{"ast": null, "code": "import { timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeTicks, timeTickInterval } from \"d3-time\";\nimport { timeFormat } from \"d3-time-format\";\nimport continuous, { copy } from \"./continuous.js\";\nimport { initRange } from \"./init.js\";\nimport nice from \"./nice.js\";\nfunction date(t) {\n  return new Date(t);\n}\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\nexport function calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = continuous(),\n    invert = scale.invert,\n    domain = scale.domain;\n  var formatMillisecond = format(\".%L\"),\n    formatSecond = format(\":%S\"),\n    formatMinute = format(\"%I:%M\"),\n    formatHour = format(\"%I %p\"),\n    formatDay = format(\"%a %d\"),\n    formatWeek = format(\"%b %d\"),\n    formatMonth = format(\"%B\"),\n    formatYear = format(\"%Y\");\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond : minute(date) < date ? formatSecond : hour(date) < date ? formatMinute : day(date) < date ? formatHour : month(date) < date ? week(date) < date ? formatDay : formatWeek : year(date) < date ? formatMonth : formatYear)(date);\n  }\n  scale.invert = function (y) {\n    return new Date(invert(y));\n  };\n  scale.domain = function (_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n  scale.ticks = function (interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n  scale.tickFormat = function (count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n  scale.nice = function (interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain(nice(d, interval)) : scale;\n  };\n  scale.copy = function () {\n    return copy(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n  return scale;\n}\nexport default function time() {\n  return initRange.apply(calendar(timeTicks, timeTickInterval, timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}", "map": {"version": 3, "names": ["timeYear", "timeMonth", "timeWeek", "timeDay", "timeHour", "timeMinute", "timeSecond", "timeTicks", "timeTickInterval", "timeFormat", "continuous", "copy", "initRange", "nice", "date", "t", "Date", "number", "calendar", "ticks", "tickInterval", "year", "month", "week", "day", "hour", "minute", "second", "format", "scale", "invert", "domain", "formatMillisecond", "formatSecond", "formatMinute", "formatHour", "formatDay", "formatWeek", "formatMonth", "formatYear", "tickFormat", "y", "_", "arguments", "length", "Array", "from", "map", "interval", "d", "count", "specifier", "range", "time", "apply"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/d3-scale/src/time.js"], "sourcesContent": ["import {timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeTicks, timeTickInterval} from \"d3-time\";\nimport {timeFormat} from \"d3-time-format\";\nimport continuous, {copy} from \"./continuous.js\";\nimport {initRange} from \"./init.js\";\nimport nice from \"./nice.js\";\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nexport function calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = continuous(),\n      invert = scale.invert,\n      domain = scale.domain;\n\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond\n        : minute(date) < date ? formatSecond\n        : hour(date) < date ? formatMinute\n        : day(date) < date ? formatHour\n        : month(date) < date ? (week(date) < date ? formatDay : formatWeek)\n        : year(date) < date ? formatMonth\n        : formatYear)(date);\n  }\n\n  scale.invert = function(y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function(interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function(interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain(nice(d, interval)) : scale;\n  };\n\n  scale.copy = function() {\n    return copy(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n\n  return scale;\n}\n\nexport default function time() {\n  return initRange.apply(calendar(timeTicks, timeTickInterval, timeYear, timeMonth, timeWeek, timeDay, timeHour, timeMinute, timeSecond, timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAEC,gBAAgB,QAAO,SAAS;AAC7H,SAAQC,UAAU,QAAO,gBAAgB;AACzC,OAAOC,UAAU,IAAGC,IAAI,QAAO,iBAAiB;AAChD,SAAQC,SAAS,QAAO,WAAW;AACnC,OAAOC,IAAI,MAAM,WAAW;AAE5B,SAASC,IAAIA,CAACC,CAAC,EAAE;EACf,OAAO,IAAIC,IAAI,CAACD,CAAC,CAAC;AACpB;AAEA,SAASE,MAAMA,CAACF,CAAC,EAAE;EACjB,OAAOA,CAAC,YAAYC,IAAI,GAAG,CAACD,CAAC,GAAG,CAAC,IAAIC,IAAI,CAAC,CAACD,CAAC,CAAC;AAC/C;AAEA,OAAO,SAASG,QAAQA,CAACC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAClG,IAAIC,KAAK,GAAGnB,UAAU,CAAC,CAAC;IACpBoB,MAAM,GAAGD,KAAK,CAACC,MAAM;IACrBC,MAAM,GAAGF,KAAK,CAACE,MAAM;EAEzB,IAAIC,iBAAiB,GAAGJ,MAAM,CAAC,KAAK,CAAC;IACjCK,YAAY,GAAGL,MAAM,CAAC,KAAK,CAAC;IAC5BM,YAAY,GAAGN,MAAM,CAAC,OAAO,CAAC;IAC9BO,UAAU,GAAGP,MAAM,CAAC,OAAO,CAAC;IAC5BQ,SAAS,GAAGR,MAAM,CAAC,OAAO,CAAC;IAC3BS,UAAU,GAAGT,MAAM,CAAC,OAAO,CAAC;IAC5BU,WAAW,GAAGV,MAAM,CAAC,IAAI,CAAC;IAC1BW,UAAU,GAAGX,MAAM,CAAC,IAAI,CAAC;EAE7B,SAASY,UAAUA,CAAC1B,IAAI,EAAE;IACxB,OAAO,CAACa,MAAM,CAACb,IAAI,CAAC,GAAGA,IAAI,GAAGkB,iBAAiB,GACzCN,MAAM,CAACZ,IAAI,CAAC,GAAGA,IAAI,GAAGmB,YAAY,GAClCR,IAAI,CAACX,IAAI,CAAC,GAAGA,IAAI,GAAGoB,YAAY,GAChCV,GAAG,CAACV,IAAI,CAAC,GAAGA,IAAI,GAAGqB,UAAU,GAC7Bb,KAAK,CAACR,IAAI,CAAC,GAAGA,IAAI,GAAIS,IAAI,CAACT,IAAI,CAAC,GAAGA,IAAI,GAAGsB,SAAS,GAAGC,UAAU,GAChEhB,IAAI,CAACP,IAAI,CAAC,GAAGA,IAAI,GAAGwB,WAAW,GAC/BC,UAAU,EAAEzB,IAAI,CAAC;EACzB;EAEAe,KAAK,CAACC,MAAM,GAAG,UAASW,CAAC,EAAE;IACzB,OAAO,IAAIzB,IAAI,CAACc,MAAM,CAACW,CAAC,CAAC,CAAC;EAC5B,CAAC;EAEDZ,KAAK,CAACE,MAAM,GAAG,UAASW,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,GAAGb,MAAM,CAACc,KAAK,CAACC,IAAI,CAACJ,CAAC,EAAEzB,MAAM,CAAC,CAAC,GAAGc,MAAM,CAAC,CAAC,CAACgB,GAAG,CAACjC,IAAI,CAAC;EAC9E,CAAC;EAEDe,KAAK,CAACV,KAAK,GAAG,UAAS6B,QAAQ,EAAE;IAC/B,IAAIC,CAAC,GAAGlB,MAAM,CAAC,CAAC;IAChB,OAAOZ,KAAK,CAAC8B,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACA,CAAC,CAACL,MAAM,GAAG,CAAC,CAAC,EAAEI,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ,CAAC;EACvE,CAAC;EAEDnB,KAAK,CAACW,UAAU,GAAG,UAASU,KAAK,EAAEC,SAAS,EAAE;IAC5C,OAAOA,SAAS,IAAI,IAAI,GAAGX,UAAU,GAAGZ,MAAM,CAACuB,SAAS,CAAC;EAC3D,CAAC;EAEDtB,KAAK,CAAChB,IAAI,GAAG,UAASmC,QAAQ,EAAE;IAC9B,IAAIC,CAAC,GAAGlB,MAAM,CAAC,CAAC;IAChB,IAAI,CAACiB,QAAQ,IAAI,OAAOA,QAAQ,CAACI,KAAK,KAAK,UAAU,EAAEJ,QAAQ,GAAG5B,YAAY,CAAC6B,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACA,CAAC,CAACL,MAAM,GAAG,CAAC,CAAC,EAAEI,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ,CAAC;IACvI,OAAOA,QAAQ,GAAGjB,MAAM,CAAClB,IAAI,CAACoC,CAAC,EAAED,QAAQ,CAAC,CAAC,GAAGnB,KAAK;EACrD,CAAC;EAEDA,KAAK,CAAClB,IAAI,GAAG,YAAW;IACtB,OAAOA,IAAI,CAACkB,KAAK,EAAEX,QAAQ,CAACC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC,CAAC;EACzG,CAAC;EAED,OAAOC,KAAK;AACd;AAEA,eAAe,SAASwB,IAAIA,CAAA,EAAG;EAC7B,OAAOzC,SAAS,CAAC0C,KAAK,CAACpC,QAAQ,CAACX,SAAS,EAAEC,gBAAgB,EAAER,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEG,UAAU,CAAC,CAACsB,MAAM,CAAC,CAAC,IAAIf,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAIA,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE2B,SAAS,CAAC;AACrN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}