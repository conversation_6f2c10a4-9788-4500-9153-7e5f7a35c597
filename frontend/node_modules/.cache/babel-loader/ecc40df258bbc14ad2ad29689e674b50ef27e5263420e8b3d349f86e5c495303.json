{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousSunday\n * @category Weekday Helpers\n * @summary When is the previous Sunday?\n *\n * @description\n * When is the previous Sunday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Sunday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Sunday before Jun, 21, 2021?\n * const result = previousSunday(new Date(2021, 5, 21))\n * //=> Sun June 20 2021 00:00:00\n */\nexport default function previousSunday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 0);\n}", "map": {"version": 3, "names": ["requiredArgs", "previousDay", "previousSunday", "date", "arguments"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/previousSunday/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousSunday\n * @category Weekday Helpers\n * @summary When is the previous Sunday?\n *\n * @description\n * When is the previous Sunday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Sunday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Sunday before Jun, 21, 2021?\n * const result = previousSunday(new Date(2021, 5, 21))\n * //=> Sun June 20 2021 00:00:00\n */\nexport default function previousSunday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 0);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,WAAW,MAAM,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC3CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOH,WAAW,CAACE,IAAI,EAAE,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}