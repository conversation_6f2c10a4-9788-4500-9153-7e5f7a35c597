{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport formatters from \"../_lib/format/lightFormatters/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\"; // This RegExp consists of three parts separated by `|`:\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nvar formattingTokensRegExp = /(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\nvar escapedStringRegExp = /^'([^]*?)'?$/;\nvar doubleQuoteRegExp = /''/g;\nvar unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @name lightFormat\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. Unlike `format`,\n * `lightFormat` doesn't use locales and outputs date using the most popular tokens.\n *\n * > ⚠️ Please note that the `lightFormat` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   |\n * |---------------------------------|---------|-----------------------------------|\n * | AM, PM                          | a..aaa  | AM, PM                            |\n * |                                 | aaaa    | a.m., p.m.                        |\n * |                                 | aaaaa   | a, p                              |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 |\n * |                                 | yy      | 44, 01, 00, 17                    |\n * |                                 | yyy     | 044, 001, 000, 017                |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |\n * |                                 | MM      | 01, 02, ..., 12                   |\n * | Day of month                    | d       | 1, 2, ..., 31                     |\n * |                                 | dd      | 01, 02, ..., 31                   |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |\n * |                                 | hh      | 01, 02, ..., 11, 12               |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |\n * |                                 | HH      | 00, 01, 02, ..., 23               |\n * | Minute                          | m       | 0, 1, ..., 59                     |\n * |                                 | mm      | 00, 01, ..., 59                   |\n * | Second                          | s       | 0, 1, ..., 59                     |\n * |                                 | ss      | 00, 01, ..., 59                   |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |\n * |                                 | SS      | 00, 01, ..., 99                   |\n * |                                 | SSS     | 000, 001, ..., 999                |\n * |                                 | SSSS    | ...                               |\n *\n * @param {Date|Number} date - the original date\n * @param {String} format - the string of tokens\n * @returns {String} the formatted date string\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} format string contains an unescaped latin alphabet character\n *\n * @example\n * const result = lightFormat(new Date(2014, 1, 11), 'yyyy-MM-dd')\n * //=> '2014-02-11'\n */\n\nexport default function lightFormat(dirtyDate, formatStr) {\n  requiredArgs(2, arguments);\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  // Convert the date in system timezone to the same date in UTC+00:00 timezone.\n  // This ensures that when UTC functions will be implemented, locales will be compatible with them.\n  // See an issue about UTC functions: https://github.com/date-fns/date-fns/issues/376\n  var timezoneOffset = getTimezoneOffsetInMilliseconds(originalDate);\n  var utcDate = subMilliseconds(originalDate, timezoneOffset);\n  var tokens = formatStr.match(formattingTokensRegExp);\n\n  // The only case when formattingTokensRegExp doesn't match the string is when it's empty\n  if (!tokens) return '';\n  var result = tokens.map(function (substring) {\n    // Replace two single quote characters with one single quote character\n    if (substring === \"''\") {\n      return \"'\";\n    }\n    var firstCharacter = substring[0];\n    if (firstCharacter === \"'\") {\n      return cleanEscapedString(substring);\n    }\n    var formatter = formatters[firstCharacter];\n    if (formatter) {\n      return formatter(utcDate, substring);\n    }\n    if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n      throw new RangeError('Format string contains an unescaped latin alphabet character `' + firstCharacter + '`');\n    }\n    return substring;\n  }).join('');\n  return result;\n}\nfunction cleanEscapedString(input) {\n  var matches = input.match(escapedStringRegExp);\n  if (!matches) {\n    return input;\n  }\n  return matches[1].replace(doubleQuoteRegExp, \"'\");\n}", "map": {"version": 3, "names": ["toDate", "formatters", "getTimezoneOffsetInMilliseconds", "<PERSON><PERSON><PERSON><PERSON>", "subMilliseconds", "requiredArgs", "formattingTokensRegExp", "escapedStringRegExp", "doubleQuoteRegExp", "unescapedLatinCharacterRegExp", "lightFormat", "dirtyDate", "formatStr", "arguments", "originalDate", "RangeError", "timezoneOffset", "utcDate", "tokens", "match", "result", "map", "substring", "firstCharacter", "cleanEscapedString", "formatter", "join", "input", "matches", "replace"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/lightFormat/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport formatters from \"../_lib/format/lightFormatters/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\"; // This RegExp consists of three parts separated by `|`:\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nvar formattingTokensRegExp = /(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\nvar escapedStringRegExp = /^'([^]*?)'?$/;\nvar doubleQuoteRegExp = /''/g;\nvar unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @name lightFormat\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. Unlike `format`,\n * `lightFormat` doesn't use locales and outputs date using the most popular tokens.\n *\n * > ⚠️ Please note that the `lightFormat` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   |\n * |---------------------------------|---------|-----------------------------------|\n * | AM, PM                          | a..aaa  | AM, PM                            |\n * |                                 | aaaa    | a.m., p.m.                        |\n * |                                 | aaaaa   | a, p                              |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 |\n * |                                 | yy      | 44, 01, 00, 17                    |\n * |                                 | yyy     | 044, 001, 000, 017                |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |\n * |                                 | MM      | 01, 02, ..., 12                   |\n * | Day of month                    | d       | 1, 2, ..., 31                     |\n * |                                 | dd      | 01, 02, ..., 31                   |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |\n * |                                 | hh      | 01, 02, ..., 11, 12               |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |\n * |                                 | HH      | 00, 01, 02, ..., 23               |\n * | Minute                          | m       | 0, 1, ..., 59                     |\n * |                                 | mm      | 00, 01, ..., 59                   |\n * | Second                          | s       | 0, 1, ..., 59                     |\n * |                                 | ss      | 00, 01, ..., 59                   |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |\n * |                                 | SS      | 00, 01, ..., 99                   |\n * |                                 | SSS     | 000, 001, ..., 999                |\n * |                                 | SSSS    | ...                               |\n *\n * @param {Date|Number} date - the original date\n * @param {String} format - the string of tokens\n * @returns {String} the formatted date string\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} format string contains an unescaped latin alphabet character\n *\n * @example\n * const result = lightFormat(new Date(2014, 1, 11), 'yyyy-MM-dd')\n * //=> '2014-02-11'\n */\n\nexport default function lightFormat(dirtyDate, formatStr) {\n  requiredArgs(2, arguments);\n  var originalDate = toDate(dirtyDate);\n  if (!isValid(originalDate)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  // Convert the date in system timezone to the same date in UTC+00:00 timezone.\n  // This ensures that when UTC functions will be implemented, locales will be compatible with them.\n  // See an issue about UTC functions: https://github.com/date-fns/date-fns/issues/376\n  var timezoneOffset = getTimezoneOffsetInMilliseconds(originalDate);\n  var utcDate = subMilliseconds(originalDate, timezoneOffset);\n  var tokens = formatStr.match(formattingTokensRegExp);\n\n  // The only case when formattingTokensRegExp doesn't match the string is when it's empty\n  if (!tokens) return '';\n  var result = tokens.map(function (substring) {\n    // Replace two single quote characters with one single quote character\n    if (substring === \"''\") {\n      return \"'\";\n    }\n    var firstCharacter = substring[0];\n    if (firstCharacter === \"'\") {\n      return cleanEscapedString(substring);\n    }\n    var formatter = formatters[firstCharacter];\n    if (formatter) {\n      return formatter(utcDate, substring);\n    }\n    if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n      throw new RangeError('Format string contains an unescaped latin alphabet character `' + firstCharacter + '`');\n    }\n    return substring;\n  }).join('');\n  return result;\n}\nfunction cleanEscapedString(input) {\n  var matches = input.match(escapedStringRegExp);\n  if (!matches) {\n    return input;\n  }\n  return matches[1].replace(doubleQuoteRegExp, \"'\");\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,UAAU,MAAM,yCAAyC;AAChE,OAAOC,+BAA+B,MAAM,kDAAkD;AAC9F,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,+BAA+B,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,gCAAgC;AAC7D,IAAIC,mBAAmB,GAAG,cAAc;AACxC,IAAIC,iBAAiB,GAAG,KAAK;AAC7B,IAAIC,6BAA6B,GAAG,UAAU;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,WAAWA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACxDP,YAAY,CAAC,CAAC,EAAEQ,SAAS,CAAC;EAC1B,IAAIC,YAAY,GAAGd,MAAM,CAACW,SAAS,CAAC;EACpC,IAAI,CAACR,OAAO,CAACW,YAAY,CAAC,EAAE;IAC1B,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EAC5C;;EAEA;EACA;EACA;EACA,IAAIC,cAAc,GAAGd,+BAA+B,CAACY,YAAY,CAAC;EAClE,IAAIG,OAAO,GAAGb,eAAe,CAACU,YAAY,EAAEE,cAAc,CAAC;EAC3D,IAAIE,MAAM,GAAGN,SAAS,CAACO,KAAK,CAACb,sBAAsB,CAAC;;EAEpD;EACA,IAAI,CAACY,MAAM,EAAE,OAAO,EAAE;EACtB,IAAIE,MAAM,GAAGF,MAAM,CAACG,GAAG,CAAC,UAAUC,SAAS,EAAE;IAC3C;IACA,IAAIA,SAAS,KAAK,IAAI,EAAE;MACtB,OAAO,GAAG;IACZ;IACA,IAAIC,cAAc,GAAGD,SAAS,CAAC,CAAC,CAAC;IACjC,IAAIC,cAAc,KAAK,GAAG,EAAE;MAC1B,OAAOC,kBAAkB,CAACF,SAAS,CAAC;IACtC;IACA,IAAIG,SAAS,GAAGxB,UAAU,CAACsB,cAAc,CAAC;IAC1C,IAAIE,SAAS,EAAE;MACb,OAAOA,SAAS,CAACR,OAAO,EAAEK,SAAS,CAAC;IACtC;IACA,IAAIC,cAAc,CAACJ,KAAK,CAACV,6BAA6B,CAAC,EAAE;MACvD,MAAM,IAAIM,UAAU,CAAC,gEAAgE,GAAGQ,cAAc,GAAG,GAAG,CAAC;IAC/G;IACA,OAAOD,SAAS;EAClB,CAAC,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;EACX,OAAON,MAAM;AACf;AACA,SAASI,kBAAkBA,CAACG,KAAK,EAAE;EACjC,IAAIC,OAAO,GAAGD,KAAK,CAACR,KAAK,CAACZ,mBAAmB,CAAC;EAC9C,IAAI,CAACqB,OAAO,EAAE;IACZ,OAAOD,KAAK;EACd;EACA,OAAOC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACrB,iBAAiB,EAAE,GAAG,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}