{"ast": null, "code": "var ListCache = require('./_ListCache'),\n  stackClear = require('./_stackClear'),\n  stackDelete = require('./_stackDelete'),\n  stackGet = require('./_stackGet'),\n  stackHas = require('./_stackHas'),\n  stackSet = require('./_stackSet');\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\nmodule.exports = Stack;", "map": {"version": 3, "names": ["ListCache", "require", "stackClear", "stackDelete", "stackGet", "stackHas", "stackSet", "<PERSON><PERSON>", "entries", "data", "__data__", "size", "prototype", "clear", "get", "has", "set", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_Stack.js"], "sourcesContent": ["var ListCache = require('./_ListCache'),\n    stackClear = require('./_stackClear'),\n    stackDelete = require('./_stackDelete'),\n    stackGet = require('./_stackGet'),\n    stackHas = require('./_stackHas'),\n    stackSet = require('./_stackSet');\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n\nmodule.exports = Stack;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;EACvCG,QAAQ,GAAGH,OAAO,CAAC,aAAa,CAAC;EACjCI,QAAQ,GAAGJ,OAAO,CAAC,aAAa,CAAC;EACjCK,QAAQ,GAAGL,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,KAAKA,CAACC,OAAO,EAAE;EACtB,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAIV,SAAS,CAACQ,OAAO,CAAC;EACjD,IAAI,CAACG,IAAI,GAAGF,IAAI,CAACE,IAAI;AACvB;;AAEA;AACAJ,KAAK,CAACK,SAAS,CAACC,KAAK,GAAGX,UAAU;AAClCK,KAAK,CAACK,SAAS,CAAC,QAAQ,CAAC,GAAGT,WAAW;AACvCI,KAAK,CAACK,SAAS,CAACE,GAAG,GAAGV,QAAQ;AAC9BG,KAAK,CAACK,SAAS,CAACG,GAAG,GAAGV,QAAQ;AAC9BE,KAAK,CAACK,SAAS,CAACI,GAAG,GAAGV,QAAQ;AAE9BW,MAAM,CAACC,OAAO,GAAGX,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}