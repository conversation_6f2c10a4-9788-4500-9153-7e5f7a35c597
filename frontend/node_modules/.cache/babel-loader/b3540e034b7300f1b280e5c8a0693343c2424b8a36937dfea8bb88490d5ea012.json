{"ast": null, "code": "var _excluded = [\"component\"];\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\n/**\n * @fileOverview Customized\n */\nimport React, { isValidElement, cloneElement, createElement } from 'react';\nimport isFunction from 'lodash/isFunction';\nimport { Layer } from '../container/Layer';\nimport { warn } from '../util/LogUtils';\n/**\n * custom svg elements by rechart instance props and state.\n * @returns {Object}   svg elements\n */\nexport function Customized(_ref) {\n  var component = _ref.component,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var child;\n  if (/*#__PURE__*/isValidElement(component)) {\n    child = /*#__PURE__*/cloneElement(component, props);\n  } else if (isFunction(component)) {\n    child = /*#__PURE__*/createElement(component, props);\n  } else {\n    warn(false, \"Customized's props `component` must be React.element or Function, but got %s.\", _typeof(component));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-customized-wrapper\"\n  }, child);\n}\nCustomized.displayName = 'Customized';", "map": {"version": 3, "names": ["_excluded", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_objectWithoutProperties", "source", "excluded", "target", "_objectWithoutPropertiesLoose", "key", "i", "Object", "getOwnPropertySymbols", "sourceSymbolKeys", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "React", "isValidElement", "cloneElement", "createElement", "isFunction", "Layer", "warn", "Customized", "_ref", "component", "props", "child", "className", "displayName"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/component/Customized.js"], "sourcesContent": ["var _excluded = [\"component\"];\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\n/**\n * @fileOverview Customized\n */\nimport React, { isValidElement, cloneElement, createElement } from 'react';\nimport isFunction from 'lodash/isFunction';\nimport { Layer } from '../container/Layer';\nimport { warn } from '../util/LogUtils';\n/**\n * custom svg elements by rechart instance props and state.\n * @returns {Object}   svg elements\n */\nexport function Customized(_ref) {\n  var component = _ref.component,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var child;\n  if ( /*#__PURE__*/isValidElement(component)) {\n    child = /*#__PURE__*/cloneElement(component, props);\n  } else if (isFunction(component)) {\n    child = /*#__PURE__*/createElement(component, props);\n  } else {\n    warn(false, \"Customized's props `component` must be React.element or Function, but got %s.\", _typeof(component));\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-customized-wrapper\"\n  }, child);\n}\nCustomized.displayName = 'Customized';"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,WAAW,CAAC;AAC7B,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,wBAAwBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAGC,6BAA6B,CAACH,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,GAAG,EAAEC,CAAC;EAAE,IAAIC,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,gBAAgB,GAAGF,MAAM,CAACC,qBAAqB,CAACP,MAAM,CAAC;IAAE,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,gBAAgB,CAACC,MAAM,EAAEJ,CAAC,EAAE,EAAE;MAAED,GAAG,GAAGI,gBAAgB,CAACH,CAAC,CAAC;MAAE,IAAIJ,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAU,IAAI,CAACE,MAAM,CAACR,SAAS,CAACa,oBAAoB,CAACC,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AAC3e,SAASC,6BAA6BA,CAACH,MAAM,EAAEC,QAAQ,EAAE;EAAE,IAAID,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIE,GAAG,IAAIJ,MAAM,EAAE;IAAE,IAAIM,MAAM,CAACR,SAAS,CAACe,cAAc,CAACD,IAAI,CAACZ,MAAM,EAAEI,GAAG,CAAC,EAAE;MAAE,IAAIH,QAAQ,CAACS,OAAO,CAACN,GAAG,CAAC,IAAI,CAAC,EAAE;MAAUF,MAAM,CAACE,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;IAAE;EAAE;EAAE,OAAOF,MAAM;AAAE;AACtR;AACA;AACA;AACA,OAAOY,KAAK,IAAIC,cAAc,EAAEC,YAAY,EAAEC,aAAa,QAAQ,OAAO;AAC1E,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,IAAI,QAAQ,kBAAkB;AACvC;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,KAAK,GAAGzB,wBAAwB,CAACuB,IAAI,EAAE9B,SAAS,CAAC;EACnD,IAAIiC,KAAK;EACT,IAAK,aAAaV,cAAc,CAACQ,SAAS,CAAC,EAAE;IAC3CE,KAAK,GAAG,aAAaT,YAAY,CAACO,SAAS,EAAEC,KAAK,CAAC;EACrD,CAAC,MAAM,IAAIN,UAAU,CAACK,SAAS,CAAC,EAAE;IAChCE,KAAK,GAAG,aAAaR,aAAa,CAACM,SAAS,EAAEC,KAAK,CAAC;EACtD,CAAC,MAAM;IACLJ,IAAI,CAAC,KAAK,EAAE,+EAA+E,EAAE3B,OAAO,CAAC8B,SAAS,CAAC,CAAC;EAClH;EACA,OAAO,aAAaT,KAAK,CAACG,aAAa,CAACE,KAAK,EAAE;IAC7CO,SAAS,EAAE;EACb,CAAC,EAAED,KAAK,CAAC;AACX;AACAJ,UAAU,CAACM,WAAW,GAAG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}