{"ast": null, "code": "import isString from 'lodash/isString';\nimport isNan from 'lodash/isNaN';\nimport get from 'lodash/get';\nimport lodashIsNumber from 'lodash/isNumber';\nimport isNil from 'lodash/isNil';\nexport var mathSign = function mathSign(value) {\n  if (value === 0) {\n    return 0;\n  }\n  if (value > 0) {\n    return 1;\n  }\n  return -1;\n};\nexport var isPercent = function isPercent(value) {\n  return isString(value) && value.indexOf('%') === value.length - 1;\n};\nexport var isNumber = function isNumber(value) {\n  return lodashIsNumber(value) && !isNan(value);\n};\nexport var isNullish = function isNullish(value) {\n  return isNil(value);\n};\nexport var isNumOrStr = function isNumOrStr(value) {\n  return isNumber(value) || isString(value);\n};\nvar idCounter = 0;\nexport var uniqueId = function uniqueId(prefix) {\n  var id = ++idCounter;\n  return \"\".concat(prefix || '').concat(id);\n};\n\n/**\n * Get percent value of a total value\n * @param {number|string} percent A percent\n * @param {number} totalValue     Total value\n * @param {number} defaultValue   The value returned when percent is undefined or invalid\n * @param {boolean} validate      If set to be true, the result will be validated\n * @return {number} value\n */\nexport var getPercentValue = function getPercentValue(percent, totalValue) {\n  var defaultValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  var validate = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (!isNumber(percent) && !isString(percent)) {\n    return defaultValue;\n  }\n  var value;\n  if (isPercent(percent)) {\n    var index = percent.indexOf('%');\n    value = totalValue * parseFloat(percent.slice(0, index)) / 100;\n  } else {\n    value = +percent;\n  }\n  if (isNan(value)) {\n    value = defaultValue;\n  }\n  if (validate && value > totalValue) {\n    value = totalValue;\n  }\n  return value;\n};\nexport var getAnyElementOfObject = function getAnyElementOfObject(obj) {\n  if (!obj) {\n    return null;\n  }\n  var keys = Object.keys(obj);\n  if (keys && keys.length) {\n    return obj[keys[0]];\n  }\n  return null;\n};\nexport var hasDuplicate = function hasDuplicate(ary) {\n  if (!Array.isArray(ary)) {\n    return false;\n  }\n  var len = ary.length;\n  var cache = {};\n  for (var i = 0; i < len; i++) {\n    if (!cache[ary[i]]) {\n      cache[ary[i]] = true;\n    } else {\n      return true;\n    }\n  }\n  return false;\n};\n\n/* @todo consider to rename this function into `getInterpolator` */\nexport var interpolateNumber = function interpolateNumber(numberA, numberB) {\n  if (isNumber(numberA) && isNumber(numberB)) {\n    return function (t) {\n      return numberA + t * (numberB - numberA);\n    };\n  }\n  return function () {\n    return numberB;\n  };\n};\nexport function findEntryInArray(ary, specifiedKey, specifiedValue) {\n  if (!ary || !ary.length) {\n    return null;\n  }\n  return ary.find(function (entry) {\n    return entry && (typeof specifiedKey === 'function' ? specifiedKey(entry) : get(entry, specifiedKey)) === specifiedValue;\n  });\n}\n\n/**\n * The least square linear regression\n * @param {Array} data The array of points\n * @returns {Object} The domain of x, and the parameter of linear function\n */\nexport var getLinearRegression = function getLinearRegression(data) {\n  if (!data || !data.length) {\n    return null;\n  }\n  var len = data.length;\n  var xsum = 0;\n  var ysum = 0;\n  var xysum = 0;\n  var xxsum = 0;\n  var xmin = Infinity;\n  var xmax = -Infinity;\n  var xcurrent = 0;\n  var ycurrent = 0;\n  for (var i = 0; i < len; i++) {\n    xcurrent = data[i].cx || 0;\n    ycurrent = data[i].cy || 0;\n    xsum += xcurrent;\n    ysum += ycurrent;\n    xysum += xcurrent * ycurrent;\n    xxsum += xcurrent * xcurrent;\n    xmin = Math.min(xmin, xcurrent);\n    xmax = Math.max(xmax, xcurrent);\n  }\n  var a = len * xxsum !== xsum * xsum ? (len * xysum - xsum * ysum) / (len * xxsum - xsum * xsum) : 0;\n  return {\n    xmin: xmin,\n    xmax: xmax,\n    a: a,\n    b: (ysum - a * xsum) / len\n  };\n};\n\n/**\n * Compare values.\n *\n * This function is intended to be passed to `Array.prototype.sort()`. It properly compares generic homogeneous arrays that are either `string[]`,\n * `number[]`, or `Date[]`. When comparing heterogeneous arrays or homogeneous arrays of other types, it will attempt to compare items properly but\n * will fall back to string comparison for mismatched or unsupported types.\n *\n * For some background, `Array.prototype.sort()`'s default comparator coerces each of the array's items into a string and compares the strings. This\n * often leads to undesirable behavior, especially with numerical items.\n *\n * @param {unknown} a The first item to compare\n * @param {unknown} b The second item to compare\n * @return {number} A negative number if a < b, a positive number if a > b, 0 if equal\n */\nexport var compareValues = function compareValues(a, b) {\n  if (isNumber(a) && isNumber(b)) {\n    return a - b;\n  }\n  if (isString(a) && isString(b)) {\n    return a.localeCompare(b);\n  }\n  if (a instanceof Date && b instanceof Date) {\n    return a.getTime() - b.getTime();\n  }\n  return String(a).localeCompare(String(b));\n};", "map": {"version": 3, "names": ["isString", "isNan", "get", "lodashIsNumber", "isNil", "mathSign", "value", "isPercent", "indexOf", "length", "isNumber", "<PERSON><PERSON><PERSON><PERSON>", "isNumOrStr", "idCounter", "uniqueId", "prefix", "id", "concat", "getPercentValue", "percent", "totalValue", "defaultValue", "arguments", "undefined", "validate", "index", "parseFloat", "slice", "getAnyElementOfObject", "obj", "keys", "Object", "hasDuplicate", "ary", "Array", "isArray", "len", "cache", "i", "interpolateNumber", "numberA", "numberB", "t", "findEntryInArray", "<PERSON><PERSON><PERSON>", "specifiedValue", "find", "entry", "getLinearRegression", "data", "xsum", "ysum", "xysum", "xxsum", "xmin", "Infinity", "xmax", "xcurrent", "ycurrent", "cx", "cy", "Math", "min", "max", "a", "b", "compareValues", "localeCompare", "Date", "getTime", "String"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/util/DataUtils.js"], "sourcesContent": ["import isString from 'lodash/isString';\nimport isNan from 'lodash/isNaN';\nimport get from 'lodash/get';\nimport lodashIsNumber from 'lodash/isNumber';\nimport isNil from 'lodash/isNil';\nexport var mathSign = function mathSign(value) {\n  if (value === 0) {\n    return 0;\n  }\n  if (value > 0) {\n    return 1;\n  }\n  return -1;\n};\nexport var isPercent = function isPercent(value) {\n  return isString(value) && value.indexOf('%') === value.length - 1;\n};\nexport var isNumber = function isNumber(value) {\n  return lodashIsNumber(value) && !isNan(value);\n};\nexport var isNullish = function isNullish(value) {\n  return isNil(value);\n};\nexport var isNumOrStr = function isNumOrStr(value) {\n  return isNumber(value) || isString(value);\n};\nvar idCounter = 0;\nexport var uniqueId = function uniqueId(prefix) {\n  var id = ++idCounter;\n  return \"\".concat(prefix || '').concat(id);\n};\n\n/**\n * Get percent value of a total value\n * @param {number|string} percent A percent\n * @param {number} totalValue     Total value\n * @param {number} defaultValue   The value returned when percent is undefined or invalid\n * @param {boolean} validate      If set to be true, the result will be validated\n * @return {number} value\n */\nexport var getPercentValue = function getPercentValue(percent, totalValue) {\n  var defaultValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n  var validate = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (!isNumber(percent) && !isString(percent)) {\n    return defaultValue;\n  }\n  var value;\n  if (isPercent(percent)) {\n    var index = percent.indexOf('%');\n    value = totalValue * parseFloat(percent.slice(0, index)) / 100;\n  } else {\n    value = +percent;\n  }\n  if (isNan(value)) {\n    value = defaultValue;\n  }\n  if (validate && value > totalValue) {\n    value = totalValue;\n  }\n  return value;\n};\nexport var getAnyElementOfObject = function getAnyElementOfObject(obj) {\n  if (!obj) {\n    return null;\n  }\n  var keys = Object.keys(obj);\n  if (keys && keys.length) {\n    return obj[keys[0]];\n  }\n  return null;\n};\nexport var hasDuplicate = function hasDuplicate(ary) {\n  if (!Array.isArray(ary)) {\n    return false;\n  }\n  var len = ary.length;\n  var cache = {};\n  for (var i = 0; i < len; i++) {\n    if (!cache[ary[i]]) {\n      cache[ary[i]] = true;\n    } else {\n      return true;\n    }\n  }\n  return false;\n};\n\n/* @todo consider to rename this function into `getInterpolator` */\nexport var interpolateNumber = function interpolateNumber(numberA, numberB) {\n  if (isNumber(numberA) && isNumber(numberB)) {\n    return function (t) {\n      return numberA + t * (numberB - numberA);\n    };\n  }\n  return function () {\n    return numberB;\n  };\n};\nexport function findEntryInArray(ary, specifiedKey, specifiedValue) {\n  if (!ary || !ary.length) {\n    return null;\n  }\n  return ary.find(function (entry) {\n    return entry && (typeof specifiedKey === 'function' ? specifiedKey(entry) : get(entry, specifiedKey)) === specifiedValue;\n  });\n}\n\n/**\n * The least square linear regression\n * @param {Array} data The array of points\n * @returns {Object} The domain of x, and the parameter of linear function\n */\nexport var getLinearRegression = function getLinearRegression(data) {\n  if (!data || !data.length) {\n    return null;\n  }\n  var len = data.length;\n  var xsum = 0;\n  var ysum = 0;\n  var xysum = 0;\n  var xxsum = 0;\n  var xmin = Infinity;\n  var xmax = -Infinity;\n  var xcurrent = 0;\n  var ycurrent = 0;\n  for (var i = 0; i < len; i++) {\n    xcurrent = data[i].cx || 0;\n    ycurrent = data[i].cy || 0;\n    xsum += xcurrent;\n    ysum += ycurrent;\n    xysum += xcurrent * ycurrent;\n    xxsum += xcurrent * xcurrent;\n    xmin = Math.min(xmin, xcurrent);\n    xmax = Math.max(xmax, xcurrent);\n  }\n  var a = len * xxsum !== xsum * xsum ? (len * xysum - xsum * ysum) / (len * xxsum - xsum * xsum) : 0;\n  return {\n    xmin: xmin,\n    xmax: xmax,\n    a: a,\n    b: (ysum - a * xsum) / len\n  };\n};\n\n/**\n * Compare values.\n *\n * This function is intended to be passed to `Array.prototype.sort()`. It properly compares generic homogeneous arrays that are either `string[]`,\n * `number[]`, or `Date[]`. When comparing heterogeneous arrays or homogeneous arrays of other types, it will attempt to compare items properly but\n * will fall back to string comparison for mismatched or unsupported types.\n *\n * For some background, `Array.prototype.sort()`'s default comparator coerces each of the array's items into a string and compares the strings. This\n * often leads to undesirable behavior, especially with numerical items.\n *\n * @param {unknown} a The first item to compare\n * @param {unknown} b The second item to compare\n * @return {number} A negative number if a < b, a positive number if a > b, 0 if equal\n */\nexport var compareValues = function compareValues(a, b) {\n  if (isNumber(a) && isNumber(b)) {\n    return a - b;\n  }\n  if (isString(a) && isString(b)) {\n    return a.localeCompare(b);\n  }\n  if (a instanceof Date && b instanceof Date) {\n    return a.getTime() - b.getTime();\n  }\n  return String(a).localeCompare(String(b));\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAO,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAE;EAC7C,IAAIA,KAAK,KAAK,CAAC,EAAE;IACf,OAAO,CAAC;EACV;EACA,IAAIA,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,CAAC;EACV;EACA,OAAO,CAAC,CAAC;AACX,CAAC;AACD,OAAO,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACD,KAAK,EAAE;EAC/C,OAAON,QAAQ,CAACM,KAAK,CAAC,IAAIA,KAAK,CAACE,OAAO,CAAC,GAAG,CAAC,KAAKF,KAAK,CAACG,MAAM,GAAG,CAAC;AACnE,CAAC;AACD,OAAO,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACJ,KAAK,EAAE;EAC7C,OAAOH,cAAc,CAACG,KAAK,CAAC,IAAI,CAACL,KAAK,CAACK,KAAK,CAAC;AAC/C,CAAC;AACD,OAAO,IAAIK,SAAS,GAAG,SAASA,SAASA,CAACL,KAAK,EAAE;EAC/C,OAAOF,KAAK,CAACE,KAAK,CAAC;AACrB,CAAC;AACD,OAAO,IAAIM,UAAU,GAAG,SAASA,UAAUA,CAACN,KAAK,EAAE;EACjD,OAAOI,QAAQ,CAACJ,KAAK,CAAC,IAAIN,QAAQ,CAACM,KAAK,CAAC;AAC3C,CAAC;AACD,IAAIO,SAAS,GAAG,CAAC;AACjB,OAAO,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,MAAM,EAAE;EAC9C,IAAIC,EAAE,GAAG,EAAEH,SAAS;EACpB,OAAO,EAAE,CAACI,MAAM,CAACF,MAAM,IAAI,EAAE,CAAC,CAACE,MAAM,CAACD,EAAE,CAAC;AAC3C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,eAAe,GAAG,SAASA,eAAeA,CAACC,OAAO,EAAEC,UAAU,EAAE;EACzE,IAAIC,YAAY,GAAGC,SAAS,CAACb,MAAM,GAAG,CAAC,IAAIa,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACxF,IAAIE,QAAQ,GAAGF,SAAS,CAACb,MAAM,GAAG,CAAC,IAAIa,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACxF,IAAI,CAACZ,QAAQ,CAACS,OAAO,CAAC,IAAI,CAACnB,QAAQ,CAACmB,OAAO,CAAC,EAAE;IAC5C,OAAOE,YAAY;EACrB;EACA,IAAIf,KAAK;EACT,IAAIC,SAAS,CAACY,OAAO,CAAC,EAAE;IACtB,IAAIM,KAAK,GAAGN,OAAO,CAACX,OAAO,CAAC,GAAG,CAAC;IAChCF,KAAK,GAAGc,UAAU,GAAGM,UAAU,CAACP,OAAO,CAACQ,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC,GAAG,GAAG;EAChE,CAAC,MAAM;IACLnB,KAAK,GAAG,CAACa,OAAO;EAClB;EACA,IAAIlB,KAAK,CAACK,KAAK,CAAC,EAAE;IAChBA,KAAK,GAAGe,YAAY;EACtB;EACA,IAAIG,QAAQ,IAAIlB,KAAK,GAAGc,UAAU,EAAE;IAClCd,KAAK,GAAGc,UAAU;EACpB;EACA,OAAOd,KAAK;AACd,CAAC;AACD,OAAO,IAAIsB,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,GAAG,EAAE;EACrE,IAAI,CAACA,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EACA,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,GAAG,CAAC;EAC3B,IAAIC,IAAI,IAAIA,IAAI,CAACrB,MAAM,EAAE;IACvB,OAAOoB,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EACrB;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACC,GAAG,EAAE;EACnD,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;IACvB,OAAO,KAAK;EACd;EACA,IAAIG,GAAG,GAAGH,GAAG,CAACxB,MAAM;EACpB,IAAI4B,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAC5B,IAAI,CAACD,KAAK,CAACJ,GAAG,CAACK,CAAC,CAAC,CAAC,EAAE;MAClBD,KAAK,CAACJ,GAAG,CAACK,CAAC,CAAC,CAAC,GAAG,IAAI;IACtB,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA,OAAO,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAC1E,IAAI/B,QAAQ,CAAC8B,OAAO,CAAC,IAAI9B,QAAQ,CAAC+B,OAAO,CAAC,EAAE;IAC1C,OAAO,UAAUC,CAAC,EAAE;MAClB,OAAOF,OAAO,GAAGE,CAAC,IAAID,OAAO,GAAGD,OAAO,CAAC;IAC1C,CAAC;EACH;EACA,OAAO,YAAY;IACjB,OAAOC,OAAO;EAChB,CAAC;AACH,CAAC;AACD,OAAO,SAASE,gBAAgBA,CAACV,GAAG,EAAEW,YAAY,EAAEC,cAAc,EAAE;EAClE,IAAI,CAACZ,GAAG,IAAI,CAACA,GAAG,CAACxB,MAAM,EAAE;IACvB,OAAO,IAAI;EACb;EACA,OAAOwB,GAAG,CAACa,IAAI,CAAC,UAAUC,KAAK,EAAE;IAC/B,OAAOA,KAAK,IAAI,CAAC,OAAOH,YAAY,KAAK,UAAU,GAAGA,YAAY,CAACG,KAAK,CAAC,GAAG7C,GAAG,CAAC6C,KAAK,EAAEH,YAAY,CAAC,MAAMC,cAAc;EAC1H,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EAClE,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACxC,MAAM,EAAE;IACzB,OAAO,IAAI;EACb;EACA,IAAI2B,GAAG,GAAGa,IAAI,CAACxC,MAAM;EACrB,IAAIyC,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI,GAAGC,QAAQ;EACnB,IAAIC,IAAI,GAAG,CAACD,QAAQ;EACpB,IAAIE,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;EAChB,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAC5BmB,QAAQ,GAAGR,IAAI,CAACX,CAAC,CAAC,CAACqB,EAAE,IAAI,CAAC;IAC1BD,QAAQ,GAAGT,IAAI,CAACX,CAAC,CAAC,CAACsB,EAAE,IAAI,CAAC;IAC1BV,IAAI,IAAIO,QAAQ;IAChBN,IAAI,IAAIO,QAAQ;IAChBN,KAAK,IAAIK,QAAQ,GAAGC,QAAQ;IAC5BL,KAAK,IAAII,QAAQ,GAAGA,QAAQ;IAC5BH,IAAI,GAAGO,IAAI,CAACC,GAAG,CAACR,IAAI,EAAEG,QAAQ,CAAC;IAC/BD,IAAI,GAAGK,IAAI,CAACE,GAAG,CAACP,IAAI,EAAEC,QAAQ,CAAC;EACjC;EACA,IAAIO,CAAC,GAAG5B,GAAG,GAAGiB,KAAK,KAAKH,IAAI,GAAGA,IAAI,GAAG,CAACd,GAAG,GAAGgB,KAAK,GAAGF,IAAI,GAAGC,IAAI,KAAKf,GAAG,GAAGiB,KAAK,GAAGH,IAAI,GAAGA,IAAI,CAAC,GAAG,CAAC;EACnG,OAAO;IACLI,IAAI,EAAEA,IAAI;IACVE,IAAI,EAAEA,IAAI;IACVQ,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAE,CAACd,IAAI,GAAGa,CAAC,GAAGd,IAAI,IAAId;EACzB,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI8B,aAAa,GAAG,SAASA,aAAaA,CAACF,CAAC,EAAEC,CAAC,EAAE;EACtD,IAAIvD,QAAQ,CAACsD,CAAC,CAAC,IAAItD,QAAQ,CAACuD,CAAC,CAAC,EAAE;IAC9B,OAAOD,CAAC,GAAGC,CAAC;EACd;EACA,IAAIjE,QAAQ,CAACgE,CAAC,CAAC,IAAIhE,QAAQ,CAACiE,CAAC,CAAC,EAAE;IAC9B,OAAOD,CAAC,CAACG,aAAa,CAACF,CAAC,CAAC;EAC3B;EACA,IAAID,CAAC,YAAYI,IAAI,IAAIH,CAAC,YAAYG,IAAI,EAAE;IAC1C,OAAOJ,CAAC,CAACK,OAAO,CAAC,CAAC,GAAGJ,CAAC,CAACI,OAAO,CAAC,CAAC;EAClC;EACA,OAAOC,MAAM,CAACN,CAAC,CAAC,CAACG,aAAa,CAACG,MAAM,CAACL,CAAC,CAAC,CAAC;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}