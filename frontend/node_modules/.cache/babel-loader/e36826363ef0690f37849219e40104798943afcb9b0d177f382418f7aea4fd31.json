{"ast": null, "code": "/**\n * @fileOverview Cross\n */\n\nexport var Cell = function Cell(_props) {\n  return null;\n};\nCell.displayName = 'Cell';", "map": {"version": 3, "names": ["Cell", "_props", "displayName"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/recharts/es6/component/Cell.js"], "sourcesContent": ["/**\n * @fileOverview Cross\n */\n\nexport var Cell = function Cell(_props) {\n  return null;\n};\nCell.displayName = 'Cell';"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAO,IAAIA,IAAI,GAAG,SAASA,IAAIA,CAACC,MAAM,EAAE;EACtC,OAAO,IAAI;AACb,CAAC;AACDD,IAAI,CAACE,WAAW,GAAG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}