{"ast": null, "code": "import React from'react';import{ChartBarIcon,CpuChipIcon,CircleStackIcon,ClockIcon,ArrowTrendingUpIcon,ArrowTrendingDownIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PerformanceMonitoring=_ref=>{let{metrics}=_ref;const formatNumber=num=>{if(num>=1000000){return(num/1000000).toFixed(1)+'M';}if(num>=1000){return(num/1000).toFixed(1)+'K';}return num.toString();};const getPerformanceColor=(value,thresholds)=>{if(value<=thresholds.good)return'text-green-600';if(value<=thresholds.warning)return'text-yellow-600';return'text-red-600';};const getProgressBarColor=(value,thresholds)=>{if(value<=thresholds.good)return'bg-green-500';if(value<=thresholds.warning)return'bg-yellow-500';return'bg-red-500';};const MetricCard=_ref2=>{let{title,value,unit,icon:Icon,trend,trendValue,color='text-gray-900'}=_ref2;return/*#__PURE__*/_jsx(\"div\",{className:\"card\",children:/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-600\",children:title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-2xl font-bold \".concat(color),children:[value,unit&&/*#__PURE__*/_jsx(\"span\",{className:\"text-lg font-normal text-gray-500 ml-1\",children:unit})]}),trend&&trendValue&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mt-1\",children:[trend==='up'?/*#__PURE__*/_jsx(ArrowTrendingUpIcon,{className:\"h-4 w-4 text-green-500 mr-1\"}):/*#__PURE__*/_jsx(ArrowTrendingDownIcon,{className:\"h-4 w-4 text-red-500 mr-1\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm \".concat(trend==='up'?'text-green-600':'text-red-600'),children:trendValue})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-lg\",children:/*#__PURE__*/_jsx(Icon,{className:\"h-6 w-6 text-gray-400\"})})]})})});};const ProgressBar=_ref3=>{let{label,value,max=100,thresholds={good:50,warning:80}}=_ref3;const percentage=value/max*100;const color=getProgressBarColor(value,thresholds);const textColor=getPerformanceColor(value,thresholds);return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-gray-700\",children:label}),/*#__PURE__*/_jsxs(\"span\",{className:\"font-medium \".concat(textColor),children:[value.toFixed(1),max===100?'%':\" / \".concat(max)]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full bg-gray-200 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-2 rounded-full transition-all duration-300 \".concat(color),style:{width:\"\".concat(Math.min(percentage,100),\"%\")}})})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-medium text-gray-900\",children:\"Performance Monitoring\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"Real-time system metrics and performance indicators\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsx(MetricCard,{title:\"Products Scraped (24h)\",value:formatNumber(metrics.productsScrapedLast24h),icon:ChartBarIcon,trend:\"up\",trendValue:\"+12%\",color:\"text-blue-600\"}),/*#__PURE__*/_jsx(MetricCard,{title:\"Success Rate\",value:metrics.successRate.toFixed(1),unit:\"%\",icon:ArrowTrendingUpIcon,trend:\"up\",trendValue:\"+2.1%\",color:getPerformanceColor(100-metrics.successRate,{good:5,warning:15})}),/*#__PURE__*/_jsx(MetricCard,{title:\"Avg Response Time\",value:metrics.averageResponseTime.toFixed(0),unit:\"ms\",icon:ClockIcon,trend:\"down\",trendValue:\"-50ms\",color:getPerformanceColor(metrics.averageResponseTime,{good:1000,warning:3000})}),/*#__PURE__*/_jsx(MetricCard,{title:\"Database Size\",value:metrics.databaseSize,icon:CircleStackIcon,trend:\"up\",trendValue:\"+1.2GB\",color:\"text-purple-600\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(CpuChipIcon,{className:\"h-5 w-5 text-gray-400 mr-2\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"System Resources\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-body space-y-6\",children:[/*#__PURE__*/_jsx(ProgressBar,{label:\"CPU Usage\",value:metrics.cpuUsage,thresholds:{good:50,warning:80}}),/*#__PURE__*/_jsx(ProgressBar,{label:\"Memory Usage\",value:metrics.memoryUsage,thresholds:{good:60,warning:85}}),/*#__PURE__*/_jsx(ProgressBar,{label:\"Database Storage\",value:75// Mock value\n,thresholds:{good:70,warning:90}})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Historical Performance\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-12\",children:[/*#__PURE__*/_jsx(ChartBarIcon,{className:\"mx-auto h-12 w-12 text-gray-400 mb-4\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:\"Charts Coming Soon\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 mb-6\",children:\"Interactive performance charts will be available in the next version.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2 text-sm text-gray-400\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"Planned charts:\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"list-disc list-inside space-y-1\",children:[/*#__PURE__*/_jsx(\"li\",{children:\"Products scraped over time\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Success rate trends\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Response time distribution\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Error rate by market\"}),/*#__PURE__*/_jsx(\"li\",{children:\"System resource usage\"})]})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900\",children:\"Active Scrapers\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[['Coto','Carrefour','Jumbo'].map((market,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-700\",children:market}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-2 w-2 bg-green-400 rounded-full mr-2 animate-pulse\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-green-600\",children:\"Running\"})]})]},market)),['Disco','Vea'].map(market=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-700\",children:market}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-2 w-2 bg-gray-400 rounded-full mr-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-600\",children:\"Idle\"})]})]},market))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900\",children:\"Recent Alerts\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-2 w-2 bg-yellow-400 rounded-full mt-2\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-700\",children:\"High response time detected\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"Carrefour - 2 minutes ago\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-2 w-2 bg-green-400 rounded-full mt-2\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-700\",children:\"Scraping completed successfully\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"Coto - 15 minutes ago\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-2 w-2 bg-red-400 rounded-full mt-2\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-700\",children:\"Connection timeout\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"Jumbo - 1 hour ago\"})]})]})]})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"h-4 w-4 mr-1\"}),\"Real-time Metrics Coming Soon - v2.0\"]})})]});};export default PerformanceMonitoring;", "map": {"version": 3, "names": ["React", "ChartBarIcon", "CpuChipIcon", "CircleStackIcon", "ClockIcon", "ArrowTrendingUpIcon", "ArrowTrendingDownIcon", "jsx", "_jsx", "jsxs", "_jsxs", "PerformanceMonitoring", "_ref", "metrics", "formatNumber", "num", "toFixed", "toString", "getPerformanceColor", "value", "thresholds", "good", "warning", "getProgressBarColor", "MetricCard", "_ref2", "title", "unit", "icon", "Icon", "trend", "trendValue", "color", "className", "children", "concat", "ProgressBar", "_ref3", "label", "max", "percentage", "textColor", "style", "width", "Math", "min", "productsScrapedLast24h", "successRate", "averageResponseTime", "databaseSize", "cpuUsage", "memoryUsage", "map", "market", "index"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/PerformanceMonitoring.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  ChartBarIcon,\n  CpuChipIcon,\n  CircleStackIcon,\n  ClockIcon,\n  ArrowTrendingUpIcon,\n  ArrowTrendingDownIcon,\n} from '@heroicons/react/24/outline';\nimport { SystemMetrics } from '../../types';\n\ninterface PerformanceMonitoringProps {\n  metrics: SystemMetrics;\n}\n\nconst PerformanceMonitoring: React.FC<PerformanceMonitoringProps> = ({\n  metrics,\n}) => {\n  const formatNumber = (num: number) => {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    }\n    if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  };\n\n  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {\n    if (value <= thresholds.good) return 'text-green-600';\n    if (value <= thresholds.warning) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  const getProgressBarColor = (value: number, thresholds: { good: number; warning: number }) => {\n    if (value <= thresholds.good) return 'bg-green-500';\n    if (value <= thresholds.warning) return 'bg-yellow-500';\n    return 'bg-red-500';\n  };\n\n  const MetricCard = ({ \n    title, \n    value, \n    unit, \n    icon: Icon, \n    trend, \n    trendValue,\n    color = 'text-gray-900' \n  }: {\n    title: string;\n    value: string | number;\n    unit?: string;\n    icon: React.ComponentType<any>;\n    trend?: 'up' | 'down';\n    trendValue?: string;\n    color?: string;\n  }) => (\n    <div className=\"card\">\n      <div className=\"card-body\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n            <p className={`text-2xl font-bold ${color}`}>\n              {value}\n              {unit && <span className=\"text-lg font-normal text-gray-500 ml-1\">{unit}</span>}\n            </p>\n            {trend && trendValue && (\n              <div className=\"flex items-center mt-1\">\n                {trend === 'up' ? (\n                  <ArrowTrendingUpIcon className=\"h-4 w-4 text-green-500 mr-1\" />\n                ) : (\n                  <ArrowTrendingDownIcon className=\"h-4 w-4 text-red-500 mr-1\" />\n                )}\n                <span className={`text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>\n                  {trendValue}\n                </span>\n              </div>\n            )}\n          </div>\n          <div className=\"p-3 bg-gray-50 rounded-lg\">\n            <Icon className=\"h-6 w-6 text-gray-400\" />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const ProgressBar = ({ \n    label, \n    value, \n    max = 100, \n    thresholds = { good: 50, warning: 80 } \n  }: {\n    label: string;\n    value: number;\n    max?: number;\n    thresholds?: { good: number; warning: number };\n  }) => {\n    const percentage = (value / max) * 100;\n    const color = getProgressBarColor(value, thresholds);\n    const textColor = getPerformanceColor(value, thresholds);\n\n    return (\n      <div className=\"space-y-2\">\n        <div className=\"flex justify-between text-sm\">\n          <span className=\"font-medium text-gray-700\">{label}</span>\n          <span className={`font-medium ${textColor}`}>\n            {value.toFixed(1)}{max === 100 ? '%' : ` / ${max}`}\n          </span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className={`h-2 rounded-full transition-all duration-300 ${color}`}\n            style={{ width: `${Math.min(percentage, 100)}%` }}\n          />\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h2 className=\"text-lg font-medium text-gray-900\">Performance Monitoring</h2>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          Real-time system metrics and performance indicators\n        </p>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <MetricCard\n          title=\"Products Scraped (24h)\"\n          value={formatNumber(metrics.productsScrapedLast24h)}\n          icon={ChartBarIcon}\n          trend=\"up\"\n          trendValue=\"+12%\"\n          color=\"text-blue-600\"\n        />\n        <MetricCard\n          title=\"Success Rate\"\n          value={metrics.successRate.toFixed(1)}\n          unit=\"%\"\n          icon={ArrowTrendingUpIcon}\n          trend=\"up\"\n          trendValue=\"+2.1%\"\n          color={getPerformanceColor(100 - metrics.successRate, { good: 5, warning: 15 })}\n        />\n        <MetricCard\n          title=\"Avg Response Time\"\n          value={metrics.averageResponseTime.toFixed(0)}\n          unit=\"ms\"\n          icon={ClockIcon}\n          trend=\"down\"\n          trendValue=\"-50ms\"\n          color={getPerformanceColor(metrics.averageResponseTime, { good: 1000, warning: 3000 })}\n        />\n        <MetricCard\n          title=\"Database Size\"\n          value={metrics.databaseSize}\n          icon={CircleStackIcon}\n          trend=\"up\"\n          trendValue=\"+1.2GB\"\n          color=\"text-purple-600\"\n        />\n      </div>\n\n      {/* System Resources */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div className=\"flex items-center\">\n            <CpuChipIcon className=\"h-5 w-5 text-gray-400 mr-2\" />\n            <h3 className=\"text-lg font-medium text-gray-900\">System Resources</h3>\n          </div>\n        </div>\n        <div className=\"card-body space-y-6\">\n          <ProgressBar\n            label=\"CPU Usage\"\n            value={metrics.cpuUsage}\n            thresholds={{ good: 50, warning: 80 }}\n          />\n          <ProgressBar\n            label=\"Memory Usage\"\n            value={metrics.memoryUsage}\n            thresholds={{ good: 60, warning: 85 }}\n          />\n          <ProgressBar\n            label=\"Database Storage\"\n            value={75} // Mock value\n            thresholds={{ good: 70, warning: 90 }}\n          />\n        </div>\n      </div>\n\n      {/* Historical Charts Placeholder */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Historical Performance</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"text-center py-12\">\n            <ChartBarIcon className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Charts Coming Soon</h3>\n            <p className=\"text-gray-500 mb-6\">\n              Interactive performance charts will be available in the next version.\n            </p>\n            <div className=\"space-y-2 text-sm text-gray-400\">\n              <p>Planned charts:</p>\n              <ul className=\"list-disc list-inside space-y-1\">\n                <li>Products scraped over time</li>\n                <li>Success rate trends</li>\n                <li>Response time distribution</li>\n                <li>Error rate by market</li>\n                <li>System resource usage</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Real-time Status */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-sm font-medium text-gray-900\">Active Scrapers</h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"space-y-3\">\n              {['Coto', 'Carrefour', 'Jumbo'].map((market, index) => (\n                <div key={market} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-700\">{market}</span>\n                  <div className=\"flex items-center\">\n                    <div className=\"h-2 w-2 bg-green-400 rounded-full mr-2 animate-pulse\" />\n                    <span className=\"text-xs text-green-600\">Running</span>\n                  </div>\n                </div>\n              ))}\n              {['Disco', 'Vea'].map((market) => (\n                <div key={market} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-700\">{market}</span>\n                  <div className=\"flex items-center\">\n                    <div className=\"h-2 w-2 bg-gray-400 rounded-full mr-2\" />\n                    <span className=\"text-xs text-gray-600\">Idle</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-sm font-medium text-gray-900\">Recent Alerts</h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"h-2 w-2 bg-yellow-400 rounded-full mt-2\" />\n                <div>\n                  <p className=\"text-sm text-gray-700\">High response time detected</p>\n                  <p className=\"text-xs text-gray-500\">Carrefour - 2 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"h-2 w-2 bg-green-400 rounded-full mt-2\" />\n                <div>\n                  <p className=\"text-sm text-gray-700\">Scraping completed successfully</p>\n                  <p className=\"text-xs text-gray-500\">Coto - 15 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"h-2 w-2 bg-red-400 rounded-full mt-2\" />\n                <div>\n                  <p className=\"text-sm text-gray-700\">Connection timeout</p>\n                  <p className=\"text-xs text-gray-500\">Jumbo - 1 hour ago</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Coming Soon Badge */}\n      <div className=\"text-center\">\n        <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n          <ClockIcon className=\"h-4 w-4 mr-1\" />\n          Real-time Metrics Coming Soon - v2.0\n        </span>\n      </div>\n    </div>\n  );\n};\n\nexport default PerformanceMonitoring;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,YAAY,CACZC,WAAW,CACXC,eAAe,CACfC,SAAS,CACTC,mBAAmB,CACnBC,qBAAqB,KAChB,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOrC,KAAM,CAAAC,qBAA2D,CAAGC,IAAA,EAE9D,IAF+D,CACnEC,OACF,CAAC,CAAAD,IAAA,CACC,KAAM,CAAAE,YAAY,CAAIC,GAAW,EAAK,CACpC,GAAIA,GAAG,EAAI,OAAO,CAAE,CAClB,MAAO,CAACA,GAAG,CAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CACzC,CACA,GAAID,GAAG,EAAI,IAAI,CAAE,CACf,MAAO,CAACA,GAAG,CAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAG,GAAG,CACtC,CACA,MAAO,CAAAD,GAAG,CAACE,QAAQ,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAACC,KAAa,CAAEC,UAA6C,GAAK,CAC5F,GAAID,KAAK,EAAIC,UAAU,CAACC,IAAI,CAAE,MAAO,gBAAgB,CACrD,GAAIF,KAAK,EAAIC,UAAU,CAACE,OAAO,CAAE,MAAO,iBAAiB,CACzD,MAAO,cAAc,CACvB,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAACJ,KAAa,CAAEC,UAA6C,GAAK,CAC5F,GAAID,KAAK,EAAIC,UAAU,CAACC,IAAI,CAAE,MAAO,cAAc,CACnD,GAAIF,KAAK,EAAIC,UAAU,CAACE,OAAO,CAAE,MAAO,eAAe,CACvD,MAAO,YAAY,CACrB,CAAC,CAED,KAAM,CAAAE,UAAU,CAAGC,KAAA,MAAC,CAClBC,KAAK,CACLP,KAAK,CACLQ,IAAI,CACJC,IAAI,CAAEC,IAAI,CACVC,KAAK,CACLC,UAAU,CACVC,KAAK,CAAG,eASV,CAAC,CAAAP,KAAA,oBACCjB,IAAA,QAAKyB,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB1B,IAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBxB,KAAA,QAAKuB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDxB,KAAA,QAAAwB,QAAA,eACE1B,IAAA,MAAGyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAER,KAAK,CAAI,CAAC,cAC5DhB,KAAA,MAAGuB,SAAS,uBAAAE,MAAA,CAAwBH,KAAK,CAAG,CAAAE,QAAA,EACzCf,KAAK,CACLQ,IAAI,eAAInB,IAAA,SAAMyB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAEP,IAAI,CAAO,CAAC,EAC9E,CAAC,CACHG,KAAK,EAAIC,UAAU,eAClBrB,KAAA,QAAKuB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EACpCJ,KAAK,GAAK,IAAI,cACbtB,IAAA,CAACH,mBAAmB,EAAC4B,SAAS,CAAC,6BAA6B,CAAE,CAAC,cAE/DzB,IAAA,CAACF,qBAAqB,EAAC2B,SAAS,CAAC,2BAA2B,CAAE,CAC/D,cACDzB,IAAA,SAAMyB,SAAS,YAAAE,MAAA,CAAaL,KAAK,GAAK,IAAI,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAAI,QAAA,CAC9EH,UAAU,CACP,CAAC,EACJ,CACN,EACE,CAAC,cACNvB,IAAA,QAAKyB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxC1B,IAAA,CAACqB,IAAI,EAACI,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACvC,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,EACP,CAED,KAAM,CAAAG,WAAW,CAAGC,KAAA,EAUd,IAVe,CACnBC,KAAK,CACLnB,KAAK,CACLoB,GAAG,CAAG,GAAG,CACTnB,UAAU,CAAG,CAAEC,IAAI,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAMvC,CAAC,CAAAe,KAAA,CACC,KAAM,CAAAG,UAAU,CAAIrB,KAAK,CAAGoB,GAAG,CAAI,GAAG,CACtC,KAAM,CAAAP,KAAK,CAAGT,mBAAmB,CAACJ,KAAK,CAAEC,UAAU,CAAC,CACpD,KAAM,CAAAqB,SAAS,CAAGvB,mBAAmB,CAACC,KAAK,CAAEC,UAAU,CAAC,CAExD,mBACEV,KAAA,QAAKuB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxB,KAAA,QAAKuB,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3C1B,IAAA,SAAMyB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAEI,KAAK,CAAO,CAAC,cAC1D5B,KAAA,SAAMuB,SAAS,gBAAAE,MAAA,CAAiBM,SAAS,CAAG,CAAAP,QAAA,EACzCf,KAAK,CAACH,OAAO,CAAC,CAAC,CAAC,CAAEuB,GAAG,GAAK,GAAG,CAAG,GAAG,OAAAJ,MAAA,CAASI,GAAG,CAAE,EAC9C,CAAC,EACJ,CAAC,cACN/B,IAAA,QAAKyB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClD1B,IAAA,QACEyB,SAAS,iDAAAE,MAAA,CAAkDH,KAAK,CAAG,CACnEU,KAAK,CAAE,CAAEC,KAAK,IAAAR,MAAA,CAAKS,IAAI,CAACC,GAAG,CAACL,UAAU,CAAE,GAAG,CAAC,KAAI,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,mBACE9B,KAAA,QAAKuB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBxB,KAAA,QAAAwB,QAAA,eACE1B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,cAC7E1B,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,qDAE1C,CAAG,CAAC,EACD,CAAC,cAGNxB,KAAA,QAAKuB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE1B,IAAA,CAACgB,UAAU,EACTE,KAAK,CAAC,wBAAwB,CAC9BP,KAAK,CAAEL,YAAY,CAACD,OAAO,CAACiC,sBAAsB,CAAE,CACpDlB,IAAI,CAAE3B,YAAa,CACnB6B,KAAK,CAAC,IAAI,CACVC,UAAU,CAAC,MAAM,CACjBC,KAAK,CAAC,eAAe,CACtB,CAAC,cACFxB,IAAA,CAACgB,UAAU,EACTE,KAAK,CAAC,cAAc,CACpBP,KAAK,CAAEN,OAAO,CAACkC,WAAW,CAAC/B,OAAO,CAAC,CAAC,CAAE,CACtCW,IAAI,CAAC,GAAG,CACRC,IAAI,CAAEvB,mBAAoB,CAC1ByB,KAAK,CAAC,IAAI,CACVC,UAAU,CAAC,OAAO,CAClBC,KAAK,CAAEd,mBAAmB,CAAC,GAAG,CAAGL,OAAO,CAACkC,WAAW,CAAE,CAAE1B,IAAI,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAE,CACjF,CAAC,cACFd,IAAA,CAACgB,UAAU,EACTE,KAAK,CAAC,mBAAmB,CACzBP,KAAK,CAAEN,OAAO,CAACmC,mBAAmB,CAAChC,OAAO,CAAC,CAAC,CAAE,CAC9CW,IAAI,CAAC,IAAI,CACTC,IAAI,CAAExB,SAAU,CAChB0B,KAAK,CAAC,MAAM,CACZC,UAAU,CAAC,OAAO,CAClBC,KAAK,CAAEd,mBAAmB,CAACL,OAAO,CAACmC,mBAAmB,CAAE,CAAE3B,IAAI,CAAE,IAAI,CAAEC,OAAO,CAAE,IAAK,CAAC,CAAE,CACxF,CAAC,cACFd,IAAA,CAACgB,UAAU,EACTE,KAAK,CAAC,eAAe,CACrBP,KAAK,CAAEN,OAAO,CAACoC,YAAa,CAC5BrB,IAAI,CAAEzB,eAAgB,CACtB2B,KAAK,CAAC,IAAI,CACVC,UAAU,CAAC,QAAQ,CACnBC,KAAK,CAAC,iBAAiB,CACxB,CAAC,EACC,CAAC,cAGNtB,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1B,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BxB,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1B,IAAA,CAACN,WAAW,EAAC+B,SAAS,CAAC,4BAA4B,CAAE,CAAC,cACtDzB,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,EACpE,CAAC,CACH,CAAC,cACNxB,KAAA,QAAKuB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC1B,IAAA,CAAC4B,WAAW,EACVE,KAAK,CAAC,WAAW,CACjBnB,KAAK,CAAEN,OAAO,CAACqC,QAAS,CACxB9B,UAAU,CAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAE,CACvC,CAAC,cACFd,IAAA,CAAC4B,WAAW,EACVE,KAAK,CAAC,cAAc,CACpBnB,KAAK,CAAEN,OAAO,CAACsC,WAAY,CAC3B/B,UAAU,CAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAE,CACvC,CAAC,cACFd,IAAA,CAAC4B,WAAW,EACVE,KAAK,CAAC,kBAAkB,CACxBnB,KAAK,CAAE,EAAI;AAAA,CACXC,UAAU,CAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAE,CACvC,CAAC,EACC,CAAC,EACH,CAAC,cAGNZ,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1B,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B1B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,CAC1E,CAAC,cACN1B,IAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBxB,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1B,IAAA,CAACP,YAAY,EAACgC,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACjEzB,IAAA,OAAIyB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC9E1B,IAAA,MAAGyB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,uEAElC,CAAG,CAAC,cACJxB,KAAA,QAAKuB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C1B,IAAA,MAAA0B,QAAA,CAAG,iBAAe,CAAG,CAAC,cACtBxB,KAAA,OAAIuB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC7C1B,IAAA,OAAA0B,QAAA,CAAI,4BAA0B,CAAI,CAAC,cACnC1B,IAAA,OAAA0B,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5B1B,IAAA,OAAA0B,QAAA,CAAI,4BAA0B,CAAI,CAAC,cACnC1B,IAAA,OAAA0B,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7B1B,IAAA,OAAA0B,QAAA,CAAI,uBAAqB,CAAI,CAAC,EAC5B,CAAC,EACF,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNxB,KAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDxB,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1B,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B1B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,CACnE,CAAC,cACN1B,IAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBxB,KAAA,QAAKuB,SAAS,CAAC,WAAW,CAAAC,QAAA,EACvB,CAAC,MAAM,CAAE,WAAW,CAAE,OAAO,CAAC,CAACkB,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,gBAChD5C,KAAA,QAAkBuB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC7D1B,IAAA,SAAMyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEmB,MAAM,CAAO,CAAC,cACvD3C,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1B,IAAA,QAAKyB,SAAS,CAAC,sDAAsD,CAAE,CAAC,cACxEzB,IAAA,SAAMyB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,EACpD,CAAC,GALEmB,MAML,CACN,CAAC,CACD,CAAC,OAAO,CAAE,KAAK,CAAC,CAACD,GAAG,CAAEC,MAAM,eAC3B3C,KAAA,QAAkBuB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC7D1B,IAAA,SAAMyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEmB,MAAM,CAAO,CAAC,cACvD3C,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1B,IAAA,QAAKyB,SAAS,CAAC,uCAAuC,CAAE,CAAC,cACzDzB,IAAA,SAAMyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,MAAI,CAAM,CAAC,EAChD,CAAC,GALEmB,MAML,CACN,CAAC,EACC,CAAC,CACH,CAAC,EACH,CAAC,cAEN3C,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB1B,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B1B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,CACjE,CAAC,cACN1B,IAAA,QAAKyB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBxB,KAAA,QAAKuB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxB,KAAA,QAAKuB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC1B,IAAA,QAAKyB,SAAS,CAAC,yCAAyC,CAAE,CAAC,cAC3DvB,KAAA,QAAAwB,QAAA,eACE1B,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,6BAA2B,CAAG,CAAC,cACpE1B,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,2BAAyB,CAAG,CAAC,EAC/D,CAAC,EACH,CAAC,cACNxB,KAAA,QAAKuB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC1B,IAAA,QAAKyB,SAAS,CAAC,wCAAwC,CAAE,CAAC,cAC1DvB,KAAA,QAAAwB,QAAA,eACE1B,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iCAA+B,CAAG,CAAC,cACxE1B,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,uBAAqB,CAAG,CAAC,EAC3D,CAAC,EACH,CAAC,cACNxB,KAAA,QAAKuB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC1B,IAAA,QAAKyB,SAAS,CAAC,sCAAsC,CAAE,CAAC,cACxDvB,KAAA,QAAAwB,QAAA,eACE1B,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,cAC3D1B,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,EACxD,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cAGN1B,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BxB,KAAA,SAAMuB,SAAS,CAAC,+FAA+F,CAAAC,QAAA,eAC7G1B,IAAA,CAACJ,SAAS,EAAC6B,SAAS,CAAC,cAAc,CAAE,CAAC,uCAExC,EAAM,CAAC,CACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}