{"ast": null, "code": "export default function buildMatchFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    }) : findKey(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    });\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (object.hasOwnProperty(key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["buildMatchFn", "args", "string", "options", "arguments", "length", "undefined", "width", "matchPattern", "matchPatterns", "defaultMatchWidth", "matchResult", "match", "matchedString", "parsePatterns", "defaultParseWidth", "key", "Array", "isArray", "findIndex", "pattern", "test", "<PERSON><PERSON><PERSON>", "value", "valueCallback", "rest", "slice", "object", "predicate", "hasOwnProperty", "array"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/date-fns/esm/locale/_lib/buildMatchFn/index.js"], "sourcesContent": ["export default function buildMatchFn(args) {\n  return function (string) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    }) : findKey(parsePatterns, function (pattern) {\n      return pattern.test(matchedString);\n    });\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return {\n      value: value,\n      rest: rest\n    };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (object.hasOwnProperty(key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,IAAI,EAAE;EACzC,OAAO,UAAUC,MAAM,EAAE;IACvB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIG,KAAK,GAAGJ,OAAO,CAACI,KAAK;IACzB,IAAIC,YAAY,GAAGD,KAAK,IAAIN,IAAI,CAACQ,aAAa,CAACF,KAAK,CAAC,IAAIN,IAAI,CAACQ,aAAa,CAACR,IAAI,CAACS,iBAAiB,CAAC;IACnG,IAAIC,WAAW,GAAGT,MAAM,CAACU,KAAK,CAACJ,YAAY,CAAC;IAC5C,IAAI,CAACG,WAAW,EAAE;MAChB,OAAO,IAAI;IACb;IACA,IAAIE,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;IAClC,IAAIG,aAAa,GAAGP,KAAK,IAAIN,IAAI,CAACa,aAAa,CAACP,KAAK,CAAC,IAAIN,IAAI,CAACa,aAAa,CAACb,IAAI,CAACc,iBAAiB,CAAC;IACpG,IAAIC,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACJ,aAAa,CAAC,GAAGK,SAAS,CAACL,aAAa,EAAE,UAAUM,OAAO,EAAE;MACnF,OAAOA,OAAO,CAACC,IAAI,CAACR,aAAa,CAAC;IACpC,CAAC,CAAC,GAAGS,OAAO,CAACR,aAAa,EAAE,UAAUM,OAAO,EAAE;MAC7C,OAAOA,OAAO,CAACC,IAAI,CAACR,aAAa,CAAC;IACpC,CAAC,CAAC;IACF,IAAIU,KAAK;IACTA,KAAK,GAAGtB,IAAI,CAACuB,aAAa,GAAGvB,IAAI,CAACuB,aAAa,CAACR,GAAG,CAAC,GAAGA,GAAG;IAC1DO,KAAK,GAAGpB,OAAO,CAACqB,aAAa,GAAGrB,OAAO,CAACqB,aAAa,CAACD,KAAK,CAAC,GAAGA,KAAK;IACpE,IAAIE,IAAI,GAAGvB,MAAM,CAACwB,KAAK,CAACb,aAAa,CAACR,MAAM,CAAC;IAC7C,OAAO;MACLkB,KAAK,EAAEA,KAAK;MACZE,IAAI,EAAEA;IACR,CAAC;EACH,CAAC;AACH;AACA,SAASH,OAAOA,CAACK,MAAM,EAAEC,SAAS,EAAE;EAClC,KAAK,IAAIZ,GAAG,IAAIW,MAAM,EAAE;IACtB,IAAIA,MAAM,CAACE,cAAc,CAACb,GAAG,CAAC,IAAIY,SAAS,CAACD,MAAM,CAACX,GAAG,CAAC,CAAC,EAAE;MACxD,OAAOA,GAAG;IACZ;EACF;EACA,OAAOV,SAAS;AAClB;AACA,SAASa,SAASA,CAACW,KAAK,EAAEF,SAAS,EAAE;EACnC,KAAK,IAAIZ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGc,KAAK,CAACzB,MAAM,EAAEW,GAAG,EAAE,EAAE;IAC3C,IAAIY,SAAS,CAACE,KAAK,CAACd,GAAG,CAAC,CAAC,EAAE;MACzB,OAAOA,GAAG;IACZ;EACF;EACA,OAAOV,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}