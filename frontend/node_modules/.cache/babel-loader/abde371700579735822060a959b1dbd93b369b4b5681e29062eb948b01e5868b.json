{"ast": null, "code": "var compareAscending = require('./_compareAscending');\n\n/**\n * Used by `_.orderBy` to compare multiple properties of a value to another\n * and stable sort them.\n *\n * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,\n * specify an order of \"desc\" for descending or \"asc\" for ascending sort order\n * of corresponding values.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {boolean[]|string[]} orders The order to sort by for each property.\n * @returns {number} Returns the sort order indicator for `object`.\n */\nfunction compareMultiple(object, other, orders) {\n  var index = -1,\n    objCriteria = object.criteria,\n    othCriteria = other.criteria,\n    length = objCriteria.length,\n    ordersLength = orders.length;\n  while (++index < length) {\n    var result = compareAscending(objCriteria[index], othCriteria[index]);\n    if (result) {\n      if (index >= ordersLength) {\n        return result;\n      }\n      var order = orders[index];\n      return result * (order == 'desc' ? -1 : 1);\n    }\n  }\n  // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications\n  // that causes it, under certain circumstances, to provide the same value for\n  // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247\n  // for more details.\n  //\n  // This also ensures a stable sort in V8 and other engines.\n  // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.\n  return object.index - other.index;\n}\nmodule.exports = compareMultiple;", "map": {"version": 3, "names": ["compareAscending", "require", "compareMultiple", "object", "other", "orders", "index", "objCriteria", "criteria", "othCriteria", "length", "ordersLength", "result", "order", "module", "exports"], "sources": ["/Volumes/2tb/github/test-scrap/frontend/node_modules/lodash/_compareMultiple.js"], "sourcesContent": ["var compareAscending = require('./_compareAscending');\n\n/**\n * Used by `_.orderBy` to compare multiple properties of a value to another\n * and stable sort them.\n *\n * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,\n * specify an order of \"desc\" for descending or \"asc\" for ascending sort order\n * of corresponding values.\n *\n * @private\n * @param {Object} object The object to compare.\n * @param {Object} other The other object to compare.\n * @param {boolean[]|string[]} orders The order to sort by for each property.\n * @returns {number} Returns the sort order indicator for `object`.\n */\nfunction compareMultiple(object, other, orders) {\n  var index = -1,\n      objCriteria = object.criteria,\n      othCriteria = other.criteria,\n      length = objCriteria.length,\n      ordersLength = orders.length;\n\n  while (++index < length) {\n    var result = compareAscending(objCriteria[index], othCriteria[index]);\n    if (result) {\n      if (index >= ordersLength) {\n        return result;\n      }\n      var order = orders[index];\n      return result * (order == 'desc' ? -1 : 1);\n    }\n  }\n  // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications\n  // that causes it, under certain circumstances, to provide the same value for\n  // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247\n  // for more details.\n  //\n  // This also ensures a stable sort in V8 and other engines.\n  // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.\n  return object.index - other.index;\n}\n\nmodule.exports = compareMultiple;\n"], "mappings": "AAAA,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,qBAAqB,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAC9C,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,WAAW,GAAGJ,MAAM,CAACK,QAAQ;IAC7BC,WAAW,GAAGL,KAAK,CAACI,QAAQ;IAC5BE,MAAM,GAAGH,WAAW,CAACG,MAAM;IAC3BC,YAAY,GAAGN,MAAM,CAACK,MAAM;EAEhC,OAAO,EAAEJ,KAAK,GAAGI,MAAM,EAAE;IACvB,IAAIE,MAAM,GAAGZ,gBAAgB,CAACO,WAAW,CAACD,KAAK,CAAC,EAAEG,WAAW,CAACH,KAAK,CAAC,CAAC;IACrE,IAAIM,MAAM,EAAE;MACV,IAAIN,KAAK,IAAIK,YAAY,EAAE;QACzB,OAAOC,MAAM;MACf;MACA,IAAIC,KAAK,GAAGR,MAAM,CAACC,KAAK,CAAC;MACzB,OAAOM,MAAM,IAAIC,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOV,MAAM,CAACG,KAAK,GAAGF,KAAK,CAACE,KAAK;AACnC;AAEAQ,MAAM,CAACC,OAAO,GAAGb,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}