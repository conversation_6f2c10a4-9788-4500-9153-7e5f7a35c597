[{"/Volumes/2tb/github/test-scrap/frontend/src/index.tsx": "1", "/Volumes/2tb/github/test-scrap/frontend/src/App.tsx": "2", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Dashboard.tsx": "3", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Compare.tsx": "4", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Search.tsx": "5", "/Volumes/2tb/github/test-scrap/frontend/src/pages/NotFound.tsx": "6", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Admin.tsx": "7", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Markets.tsx": "8", "/Volumes/2tb/github/test-scrap/frontend/src/services/api.ts": "9", "/Volumes/2tb/github/test-scrap/frontend/src/components/Compare/PriceChart.tsx": "10", "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Layout.tsx": "11", "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/HealthStatus.tsx": "12", "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/StatsCards.tsx": "13", "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/SearchFilters.tsx": "14", "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/SearchResults.tsx": "15", "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/MarketOverview.tsx": "16", "/Volumes/2tb/github/test-scrap/frontend/src/components/Compare/ComparisonTable.tsx": "17", "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Header.tsx": "18", "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Footer.tsx": "19", "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/ProductCard.tsx": "20", "/Volumes/2tb/github/test-scrap/frontend/src/components/Common/Pagination.tsx": "21"}, {"size": 274, "mtime": 1755533316942, "results": "22", "hashOfConfig": "23"}, {"size": 897, "mtime": 1755533260394, "results": "24", "hashOfConfig": "23"}, {"size": 6391, "mtime": 1755532822759, "results": "25", "hashOfConfig": "23"}, {"size": 11872, "mtime": 1755533179390, "results": "26", "hashOfConfig": "23"}, {"size": 8958, "mtime": 1755533044738, "results": "27", "hashOfConfig": "23"}, {"size": 1089, "mtime": 1755533271068, "results": "28", "hashOfConfig": "23"}, {"size": 5623, "mtime": 1755533306254, "results": "29", "hashOfConfig": "23"}, {"size": 12786, "mtime": 1755533249209, "results": "30", "hashOfConfig": "23"}, {"size": 6901, "mtime": 1755533651926, "results": "31", "hashOfConfig": "23"}, {"size": 4483, "mtime": 1755533082072, "results": "32", "hashOfConfig": "23"}, {"size": 504, "mtime": 1755532677039, "results": "33", "hashOfConfig": "23"}, {"size": 5820, "mtime": 1755534299757, "results": "34", "hashOfConfig": "23"}, {"size": 4642, "mtime": 1755533609265, "results": "35", "hashOfConfig": "23"}, {"size": 8407, "mtime": 1755532873671, "results": "36", "hashOfConfig": "23"}, {"size": 9676, "mtime": 1755532963253, "results": "37", "hashOfConfig": "23"}, {"size": 5897, "mtime": 1755532754879, "results": "38", "hashOfConfig": "23"}, {"size": 8366, "mtime": 1755533519226, "results": "39", "hashOfConfig": "23"}, {"size": 4130, "mtime": 1755532668213, "results": "40", "hashOfConfig": "23"}, {"size": 1152, "mtime": 1755532687296, "results": "41", "hashOfConfig": "23"}, {"size": 6605, "mtime": 1755532910511, "results": "42", "hashOfConfig": "23"}, {"size": 6560, "mtime": 1755532999532, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ta9tux", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Volumes/2tb/github/test-scrap/frontend/src/index.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/App.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Dashboard.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Compare.tsx", ["107"], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Search.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/NotFound.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Admin.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Markets.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/services/api.ts", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Compare/PriceChart.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Layout.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/HealthStatus.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/StatsCards.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/SearchFilters.tsx", ["108"], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/SearchResults.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/MarketOverview.tsx", ["109"], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Compare/ComparisonTable.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Header.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Footer.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/ProductCard.tsx", ["110"], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Common/Pagination.tsx", [], [], {"ruleId": "111", "severity": 1, "message": "112", "line": 28, "column": 6, "nodeType": "113", "endLine": 28, "endColumn": 20, "suggestions": "114"}, {"ruleId": "115", "severity": 1, "message": "116", "line": 4, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 4, "endColumn": 12}, {"ruleId": "115", "severity": 1, "message": "119", "line": 5, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 5, "endColumn": 15}, {"ruleId": "115", "severity": 1, "message": "120", "line": 2, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 2, "endColumn": 14}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'performComparison'. Either include it or remove the dependency array.", "ArrayExpression", ["121"], "@typescript-eslint/no-unused-vars", "'XMarkIcon' is defined but never used.", "Identifier", "unusedVar", "'ChartBarIcon' is defined but never used.", "'Link' is defined but never used.", {"desc": "122", "fix": "123"}, "Update the dependencies array to be: [performComparison, searchParams]", {"range": "124", "text": "125"}, [1047, 1061], "[performComparison, searchParams]"]