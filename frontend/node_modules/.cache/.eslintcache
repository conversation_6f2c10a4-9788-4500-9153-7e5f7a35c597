[{"/Volumes/2tb/github/test-scrap/frontend/src/index.tsx": "1", "/Volumes/2tb/github/test-scrap/frontend/src/App.tsx": "2", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Dashboard.tsx": "3", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Compare.tsx": "4", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Search.tsx": "5", "/Volumes/2tb/github/test-scrap/frontend/src/pages/NotFound.tsx": "6", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Admin.tsx": "7", "/Volumes/2tb/github/test-scrap/frontend/src/pages/Markets.tsx": "8", "/Volumes/2tb/github/test-scrap/frontend/src/services/api.ts": "9", "/Volumes/2tb/github/test-scrap/frontend/src/components/Compare/PriceChart.tsx": "10", "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Layout.tsx": "11", "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/HealthStatus.tsx": "12", "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/StatsCards.tsx": "13", "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/SearchFilters.tsx": "14", "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/SearchResults.tsx": "15", "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/MarketOverview.tsx": "16", "/Volumes/2tb/github/test-scrap/frontend/src/components/Compare/ComparisonTable.tsx": "17", "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Header.tsx": "18", "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Footer.tsx": "19", "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/ProductCard.tsx": "20", "/Volumes/2tb/github/test-scrap/frontend/src/components/Common/Pagination.tsx": "21", "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/SystemConfiguration.tsx": "22", "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/DataManagement.tsx": "23", "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/ScrapingLogs.tsx": "24", "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/ScraperControls.tsx": "25", "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/PerformanceMonitoring.tsx": "26"}, {"size": 274, "mtime": 1755533316942, "results": "27", "hashOfConfig": "28"}, {"size": 897, "mtime": 1755533260394, "results": "29", "hashOfConfig": "28"}, {"size": 6391, "mtime": 1755532822759, "results": "30", "hashOfConfig": "28"}, {"size": 11872, "mtime": 1755533179390, "results": "31", "hashOfConfig": "28"}, {"size": 8958, "mtime": 1755533044738, "results": "32", "hashOfConfig": "28"}, {"size": 1089, "mtime": 1755533271068, "results": "33", "hashOfConfig": "28"}, {"size": 14138, "mtime": 1755545048736, "results": "34", "hashOfConfig": "28"}, {"size": 12786, "mtime": 1755533249209, "results": "35", "hashOfConfig": "28"}, {"size": 6917, "mtime": 1755543078977, "results": "36", "hashOfConfig": "28"}, {"size": 4483, "mtime": 1755533082072, "results": "37", "hashOfConfig": "28"}, {"size": 504, "mtime": 1755532677039, "results": "38", "hashOfConfig": "28"}, {"size": 5820, "mtime": 1755534299757, "results": "39", "hashOfConfig": "28"}, {"size": 4642, "mtime": 1755533609265, "results": "40", "hashOfConfig": "28"}, {"size": 8407, "mtime": 1755532873671, "results": "41", "hashOfConfig": "28"}, {"size": 9698, "mtime": 1755543064901, "results": "42", "hashOfConfig": "28"}, {"size": 5897, "mtime": 1755532754879, "results": "43", "hashOfConfig": "28"}, {"size": 8366, "mtime": 1755533519226, "results": "44", "hashOfConfig": "28"}, {"size": 4130, "mtime": 1755532668213, "results": "45", "hashOfConfig": "28"}, {"size": 1152, "mtime": 1755532687296, "results": "46", "hashOfConfig": "28"}, {"size": 6605, "mtime": 1755532910511, "results": "47", "hashOfConfig": "28"}, {"size": 6560, "mtime": 1755532999532, "results": "48", "hashOfConfig": "28"}, {"size": 11067, "mtime": 1755545155848, "results": "49", "hashOfConfig": "28"}, {"size": 14419, "mtime": 1755545194947, "results": "50", "hashOfConfig": "28"}, {"size": 8942, "mtime": 1755545181530, "results": "51", "hashOfConfig": "28"}, {"size": 8144, "mtime": 1755545168940, "results": "52", "hashOfConfig": "28"}, {"size": 10213, "mtime": 1755545101809, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ta9tux", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Volumes/2tb/github/test-scrap/frontend/src/index.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/App.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Dashboard.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Compare.tsx", ["132"], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Search.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/NotFound.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Admin.tsx", ["133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147"], [], "/Volumes/2tb/github/test-scrap/frontend/src/pages/Markets.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/services/api.ts", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Compare/PriceChart.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Layout.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/HealthStatus.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/StatsCards.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/SearchFilters.tsx", ["148"], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/SearchResults.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Dashboard/MarketOverview.tsx", ["149"], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Compare/ComparisonTable.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Header.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Layout/Footer.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Search/ProductCard.tsx", ["150"], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Common/Pagination.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/SystemConfiguration.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/DataManagement.tsx", ["151"], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/ScrapingLogs.tsx", ["152"], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/ScraperControls.tsx", [], [], "/Volumes/2tb/github/test-scrap/frontend/src/components/Admin/PerformanceMonitoring.tsx", [], [], {"ruleId": "153", "severity": 1, "message": "154", "line": 28, "column": 6, "nodeType": "155", "endLine": 28, "endColumn": 20, "suggestions": "156"}, {"ruleId": "157", "severity": 1, "message": "158", "line": 1, "column": 27, "nodeType": "159", "messageId": "160", "endLine": 1, "endColumn": 36}, {"ruleId": "157", "severity": 1, "message": "161", "line": 8, "column": 3, "nodeType": "159", "messageId": "160", "endLine": 8, "endColumn": 12}, {"ruleId": "157", "severity": 1, "message": "162", "line": 219, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 219, "endColumn": 35}, {"ruleId": "157", "severity": 1, "message": "163", "line": 226, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 226, "endColumn": 34}, {"ruleId": "157", "severity": 1, "message": "164", "line": 233, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 233, "endColumn": 31}, {"ruleId": "157", "severity": 1, "message": "165", "line": 240, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 240, "endColumn": 30}, {"ruleId": "157", "severity": 1, "message": "166", "line": 247, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 247, "endColumn": 35}, {"ruleId": "157", "severity": 1, "message": "167", "line": 254, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 254, "endColumn": 33}, {"ruleId": "157", "severity": 1, "message": "168", "line": 255, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 255, "endColumn": 32}, {"ruleId": "157", "severity": 1, "message": "169", "line": 256, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 256, "endColumn": 34}, {"ruleId": "157", "severity": 1, "message": "170", "line": 257, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 257, "endColumn": 33}, {"ruleId": "157", "severity": 1, "message": "171", "line": 258, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 258, "endColumn": 33}, {"ruleId": "157", "severity": 1, "message": "172", "line": 259, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 259, "endColumn": 33}, {"ruleId": "157", "severity": 1, "message": "173", "line": 260, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 260, "endColumn": 34}, {"ruleId": "157", "severity": 1, "message": "174", "line": 261, "column": 17, "nodeType": "159", "messageId": "160", "endLine": 261, "endColumn": 39}, {"ruleId": "157", "severity": 1, "message": "175", "line": 4, "column": 3, "nodeType": "159", "messageId": "160", "endLine": 4, "endColumn": 12}, {"ruleId": "157", "severity": 1, "message": "176", "line": 5, "column": 3, "nodeType": "159", "messageId": "160", "endLine": 5, "endColumn": 15}, {"ruleId": "157", "severity": 1, "message": "177", "line": 2, "column": 10, "nodeType": "159", "messageId": "160", "endLine": 2, "endColumn": 14}, {"ruleId": "157", "severity": 1, "message": "178", "line": 56, "column": 9, "nodeType": "159", "messageId": "160", "endLine": 56, "endColumn": 23}, {"ruleId": "157", "severity": 1, "message": "179", "line": 12, "column": 20, "nodeType": "159", "messageId": "160", "endLine": 12, "endColumn": 30}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'performComparison'. Either include it or remove the dependency array.", "ArrayExpression", ["180"], "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'ClockIcon' is defined but never used.", "'handleStartScraper' is assigned a value but never used.", "'handleStopScraper' is assigned a value but never used.", "'handleStartAll' is assigned a value but never used.", "'handleStopAll' is assigned a value but never used.", "'handleConfigChange' is assigned a value but never used.", "'handleExportLogs' is assigned a value but never used.", "'handleClearLogs' is assigned a value but never used.", "'handleRefreshLogs' is assigned a value but never used.", "'handleSaveConfig' is assigned a value but never used.", "'handleExportData' is assigned a value but never used.", "'handleImportData' is assigned a value but never used.", "'handleCleanupData' is assigned a value but never used.", "'handleOptimizeDatabase' is assigned a value but never used.", "'XMarkIcon' is defined but never used.", "'ChartBarIcon' is defined but never used.", "'Link' is defined but never used.", "'formatFileSize' is assigned a value but never used.", "'MarketName' is defined but never used.", {"desc": "181", "fix": "182"}, "Update the dependencies array to be: [performComparison, searchParams]", {"range": "183", "text": "184"}, [1047, 1061], "[performComparison, searchParams]"]