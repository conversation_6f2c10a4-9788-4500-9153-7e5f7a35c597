{"version": 3, "names": ["_semver", "require", "_debug", "_filterItems", "_moduleTransformations", "_normalizeOptions", "_shippedProposals", "_pluginsCompatData", "_babelPluginPolyfillCorejs", "_babel7Plugins", "_helperCompilationTargets", "_availablePlugins", "_helper<PERSON>lugin<PERSON><PERSON>s", "pluginCoreJS3", "_pluginCoreJS3", "default", "isPluginRequired", "targets", "support", "isRequired", "compatData", "filterStageFromList", "list", "stageList", "Object", "keys", "reduce", "result", "item", "has", "pluginsListWithProposals", "assign", "pluginsList", "pluginsBugfixesList", "pluginsListWithuotProposals", "proposalPlugins", "pluginsListNoBugfixesWithProposals", "pluginsListNoBugfixesWithoutProposals", "getPlugin", "pluginName", "plugin", "availablePlugins", "Error", "transformIncludesAndExcludes", "opts", "opt", "target", "test", "add", "all", "plugins", "Set", "builtIns", "exports", "getSpecialModulesPluginNames", "modules", "shouldTransformDynamicImport", "babelVersion", "modulesPluginNames", "push", "moduleTransformations", "console", "warn", "getCoreJSOptions", "useBuiltIns", "corejs", "polyfillTargets", "include", "exclude", "proposals", "shippedProposals", "debug", "method", "version", "toString", "undefined", "noRuntimeName", "getPolyfillPlugins", "regenerator", "polyfillPlugins", "pluginOptions", "major", "babel7", "pluginCoreJS2", "legacyBabelPolyfillPlugin", "usage", "deprecated", "pluginRegenerator", "removeRegeneratorEntryPlugin", "getLocalTargets", "optionsTargets", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "browserslistEnv", "api", "<PERSON><PERSON><PERSON><PERSON>", "browsers", "getTargets", "onBrowserslistConfigFound", "config", "addExternalDependency", "supportsStaticESM", "caller", "supportsDynamicImport", "supportsExportNamespaceFrom", "_default", "declarePreset", "assertVersion", "babelTargets", "optionsExclude", "forceAllTransforms", "optionsInclude", "optionsModules", "normalizeOptions", "loose", "spec", "bugfixes", "semver", "lt", "hasUglifyTarget", "uglify", "transformTargets", "pluginNames", "filterItems", "pluginSyntaxMap", "addProposalSyntaxPlugins", "proposalSyntaxPlugins", "removeUnsupportedItems", "removeUnnecessaryItems", "overlappingPlugins", "pluginUseBuiltIns", "Array", "from", "map", "deprecatedAssertSyntax", "concat", "log", "JSON", "stringify", "prettifyTargets", "for<PERSON>ach", "logPlugin", "getModulesPluginNames", "transformations", "shouldTransformESM", "shouldTransformExportNamespaceFrom"], "sources": ["../src/index.ts"], "sourcesContent": ["import semver, { type SemVer } from \"semver\";\nimport { logPlugin } from \"./debug.ts\";\nimport {\n  addProposalSyntaxPlugins,\n  removeUnnecessaryItems,\n  removeUnsupportedItems,\n} from \"./filter-items.ts\";\nimport moduleTransformations from \"./module-transformations.ts\";\nimport normalizeOptions from \"./normalize-options.ts\";\nimport {\n  pluginSyntaxMap,\n  proposalPlugins,\n  proposalSyntaxPlugins,\n} from \"./shipped-proposals.ts\";\nimport {\n  plugins as pluginsList,\n  pluginsBugfixes as pluginsBugfixesList,\n  overlappingPlugins,\n} from \"./plugins-compat-data.ts\";\n\nimport type { CallerMetadata, PresetAPI } from \"@babel/core\";\n\nimport _pluginCoreJS3 from \"babel-plugin-polyfill-corejs3\";\n// TODO(Babel 8): Just use the default import\nconst pluginCoreJS3 = _pluginCoreJS3.default || _pluginCoreJS3;\n\nimport babel7 from \"./polyfills/babel-7-plugins.cjs\" with { if: \"!process.env.BABEL_8_BREAKING\" };\n\nimport getTargets, {\n  prettifyTargets,\n  filterItems,\n  isRequired,\n} from \"@babel/helper-compilation-targets\";\nimport type { Targets, InputTargets } from \"@babel/helper-compilation-targets\";\nimport availablePlugins from \"./available-plugins.ts\";\nimport { declarePreset } from \"@babel/helper-plugin-utils\";\n\nimport type { BuiltInsOption, ModuleOption, Options } from \"./types.d.ts\";\nexport type { Options };\n\n// TODO: Remove in Babel 8\nexport function isPluginRequired(targets: Targets, support: Targets) {\n  return isRequired(\"fake-name\", targets, {\n    compatData: { \"fake-name\": support },\n  });\n}\n\nfunction filterStageFromList(\n  list: { [feature: string]: Targets },\n  stageList: Set<string>,\n) {\n  return Object.keys(list).reduce((result, item) => {\n    if (!stageList.has(item)) {\n      // @ts-expect-error todo: refine result types\n      result[item] = list[item];\n    }\n\n    return result;\n  }, {});\n}\n\nconst pluginsListWithProposals = Object.assign(\n  {},\n  pluginsList,\n  pluginsBugfixesList,\n);\nconst pluginsListWithuotProposals = filterStageFromList(\n  pluginsListWithProposals,\n  proposalPlugins,\n);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var pluginsListNoBugfixesWithProposals = pluginsList;\n  // eslint-disable-next-line no-var\n  var pluginsListNoBugfixesWithoutProposals = filterStageFromList(\n    pluginsList,\n    proposalPlugins,\n  );\n}\n\nconst getPlugin = (pluginName: string) => {\n  const plugin =\n    // @ts-expect-error plugin name is constructed from available plugin list\n    availablePlugins[pluginName]();\n\n  if (!plugin) {\n    throw new Error(\n      `Could not find plugin \"${pluginName}\". Ensure there is an entry in ./available-plugins.js for it.`,\n    );\n  }\n\n  return plugin;\n};\n\nexport const transformIncludesAndExcludes = (opts: Array<string>): any => {\n  return opts.reduce(\n    (result, opt) => {\n      const target = /^(?:es|es6|es7|esnext|web)\\./.test(opt)\n        ? \"builtIns\"\n        : \"plugins\";\n      result[target].add(opt);\n      return result;\n    },\n    {\n      all: opts,\n      plugins: new Set(),\n      builtIns: new Set(),\n    },\n  );\n};\n\nfunction getSpecialModulesPluginNames(\n  modules: Exclude<ModuleOption, \"auto\">,\n  shouldTransformDynamicImport: boolean,\n  babelVersion: string,\n) {\n  const modulesPluginNames = [];\n  if (modules) {\n    modulesPluginNames.push(moduleTransformations[modules]);\n  }\n\n  if (shouldTransformDynamicImport) {\n    if (modules && modules !== \"umd\") {\n      modulesPluginNames.push(\"transform-dynamic-import\");\n    } else {\n      console.warn(\n        \"Dynamic import can only be transformed when transforming ES\" +\n          \" modules to AMD, CommonJS or SystemJS.\",\n      );\n    }\n  }\n\n  if (!process.env.BABEL_8_BREAKING && babelVersion[0] !== \"8\") {\n    // Enable module-related syntax plugins for older Babel versions\n    if (!shouldTransformDynamicImport) {\n      modulesPluginNames.push(\"syntax-dynamic-import\");\n    }\n    modulesPluginNames.push(\"syntax-top-level-await\");\n    modulesPluginNames.push(\"syntax-import-meta\");\n  }\n\n  return modulesPluginNames;\n}\n\nconst getCoreJSOptions = ({\n  useBuiltIns,\n  corejs,\n  polyfillTargets,\n  include,\n  exclude,\n  proposals,\n  shippedProposals,\n  debug,\n}: {\n  useBuiltIns: BuiltInsOption;\n  corejs: SemVer | null | false;\n  polyfillTargets: Targets;\n  include: Set<string>;\n  exclude: Set<string>;\n  proposals: boolean;\n  shippedProposals: boolean;\n  debug: boolean;\n}) => ({\n  method: `${useBuiltIns}-global`,\n  version: corejs ? corejs.toString() : undefined,\n  targets: polyfillTargets,\n  include,\n  exclude,\n  proposals,\n  shippedProposals,\n  debug,\n  \"#__secret_key__@babel/preset-env__compatibility\": {\n    noRuntimeName: true,\n  },\n});\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var getPolyfillPlugins = ({\n    useBuiltIns,\n    corejs,\n    polyfillTargets,\n    include,\n    exclude,\n    proposals,\n    shippedProposals,\n    regenerator,\n    debug,\n  }: {\n    useBuiltIns: BuiltInsOption;\n    corejs: SemVer | null | false;\n    polyfillTargets: Targets;\n    include: Set<string>;\n    exclude: Set<string>;\n    proposals: boolean;\n    shippedProposals: boolean;\n    regenerator: boolean;\n    debug: boolean;\n  }) => {\n    const polyfillPlugins = [];\n    if (useBuiltIns === \"usage\" || useBuiltIns === \"entry\") {\n      const pluginOptions = getCoreJSOptions({\n        useBuiltIns,\n        corejs,\n        polyfillTargets,\n        include,\n        exclude,\n        proposals,\n        shippedProposals,\n        debug,\n      });\n\n      if (corejs) {\n        if (process.env.BABEL_8_BREAKING) {\n          polyfillPlugins.push([pluginCoreJS3, pluginOptions]);\n        } else {\n          if (useBuiltIns === \"usage\") {\n            if (corejs.major === 2) {\n              polyfillPlugins.push(\n                [babel7.pluginCoreJS2, pluginOptions],\n                [babel7.legacyBabelPolyfillPlugin, { usage: true }],\n              );\n            } else {\n              polyfillPlugins.push(\n                [pluginCoreJS3, pluginOptions],\n                [\n                  babel7.legacyBabelPolyfillPlugin,\n                  { usage: true, deprecated: true },\n                ],\n              );\n            }\n            if (regenerator) {\n              polyfillPlugins.push([\n                babel7.pluginRegenerator,\n                { method: \"usage-global\", debug },\n              ]);\n            }\n          } else {\n            if (corejs.major === 2) {\n              polyfillPlugins.push(\n                [babel7.legacyBabelPolyfillPlugin, { regenerator }],\n                [babel7.pluginCoreJS2, pluginOptions],\n              );\n            } else {\n              polyfillPlugins.push(\n                [pluginCoreJS3, pluginOptions],\n                [babel7.legacyBabelPolyfillPlugin, { deprecated: true }],\n              );\n              if (!regenerator) {\n                polyfillPlugins.push([\n                  babel7.removeRegeneratorEntryPlugin,\n                  pluginOptions,\n                ]);\n              }\n            }\n          }\n        }\n      }\n    }\n    return polyfillPlugins;\n  };\n\n  if (!USE_ESM) {\n    // eslint-disable-next-line no-restricted-globals\n    exports.getPolyfillPlugins = getPolyfillPlugins;\n  }\n}\n\nfunction getLocalTargets(\n  optionsTargets: Options[\"targets\"],\n  ignoreBrowserslistConfig: boolean,\n  configPath: string,\n  browserslistEnv: string,\n  api: PresetAPI,\n) {\n  if (optionsTargets?.esmodules && optionsTargets.browsers) {\n    console.warn(`\n@babel/preset-env: esmodules and browsers targets have been specified together.\n\\`browsers\\` target, \\`${optionsTargets.browsers.toString()}\\` will be ignored.\n`);\n  }\n\n  return getTargets(optionsTargets as InputTargets, {\n    ignoreBrowserslistConfig,\n    configPath,\n    browserslistEnv,\n    onBrowserslistConfigFound(config) {\n      api.addExternalDependency(config);\n    },\n  });\n}\n\nfunction supportsStaticESM(caller: CallerMetadata | undefined) {\n  // TODO(Babel 8): Fallback to true\n  return !!caller?.supportsStaticESM;\n}\n\nfunction supportsDynamicImport(caller: CallerMetadata | undefined) {\n  // TODO(Babel 8): Fallback to true\n  return !!caller?.supportsDynamicImport;\n}\n\nfunction supportsExportNamespaceFrom(caller: CallerMetadata | undefined) {\n  // TODO(Babel 8): Fallback to null\n  return !!caller?.supportsExportNamespaceFrom;\n}\n\nexport default declarePreset((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const babelTargets = api.targets();\n\n  if (process.env.BABEL_8_BREAKING && (\"loose\" in opts || \"spec\" in opts)) {\n    throw new Error(\n      \"@babel/preset-env: The 'loose' and 'spec' options have been removed, \" +\n        \"and you should configure granular compiler assumptions instead. See \" +\n        \"https://babeljs.io/assumptions for more information.\",\n    );\n  }\n\n  const {\n    configPath,\n    debug,\n    exclude: optionsExclude,\n    forceAllTransforms,\n    ignoreBrowserslistConfig,\n    include: optionsInclude,\n    modules: optionsModules,\n    shippedProposals,\n    targets: optionsTargets,\n    useBuiltIns,\n    corejs: { version: corejs, proposals },\n    browserslistEnv,\n  } = normalizeOptions(opts);\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // eslint-disable-next-line no-var\n    var { loose, spec = false, bugfixes = false } = opts;\n  }\n\n  let targets = babelTargets;\n\n  if (\n    // @babel/core < 7.13.0 doesn't load targets (api.targets() always\n    // returns {} thanks to @babel/helper-plugin-utils), so we always want\n    // to fallback to the old targets behavior in this case.\n    semver.lt(api.version, \"7.13.0\") ||\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    opts.targets ||\n    opts.configPath ||\n    opts.browserslistEnv ||\n    opts.ignoreBrowserslistConfig\n  ) {\n    if (!process.env.BABEL_8_BREAKING) {\n      // eslint-disable-next-line no-var\n      var hasUglifyTarget = false;\n\n      if (optionsTargets?.uglify) {\n        hasUglifyTarget = true;\n        delete optionsTargets.uglify;\n\n        console.warn(`\nThe uglify target has been deprecated. Set the top level\noption \\`forceAllTransforms: true\\` instead.\n`);\n      }\n    }\n\n    targets = getLocalTargets(\n      optionsTargets,\n      ignoreBrowserslistConfig,\n      configPath,\n      browserslistEnv,\n      api,\n    );\n  }\n\n  const transformTargets = (\n    process.env.BABEL_8_BREAKING\n      ? forceAllTransforms\n      : forceAllTransforms || hasUglifyTarget\n  )\n    ? ({} as Targets)\n    : targets;\n\n  const include = transformIncludesAndExcludes(optionsInclude);\n  const exclude = transformIncludesAndExcludes(optionsExclude);\n\n  const compatData =\n    process.env.BABEL_8_BREAKING || bugfixes\n      ? shippedProposals\n        ? pluginsListWithProposals\n        : pluginsListWithuotProposals\n      : shippedProposals\n        ? pluginsListNoBugfixesWithProposals\n        : pluginsListNoBugfixesWithoutProposals;\n  const modules =\n    optionsModules === \"auto\"\n      ? api.caller(supportsStaticESM)\n        ? false\n        : \"commonjs\"\n      : optionsModules;\n  const shouldTransformDynamicImport =\n    optionsModules === \"auto\" ? !api.caller(supportsDynamicImport) : !!modules;\n\n  // If the caller does not support export-namespace-from, we forcefully add\n  // the plugin to `includes`.\n  // TODO(Babel 8): stop doing this, similarly to how we don't do this for any\n  // other plugin. We can consider adding bundlers as targets in the future,\n  // but we should not have a one-off special case for this plugin.\n  if (\n    !exclude.plugins.has(\"transform-export-namespace-from\") &&\n    (optionsModules === \"auto\"\n      ? !api.caller(supportsExportNamespaceFrom)\n      : !!modules)\n  ) {\n    include.plugins.add(\"transform-export-namespace-from\");\n  }\n\n  const pluginNames = filterItems(\n    compatData,\n    include.plugins,\n    exclude.plugins,\n    transformTargets,\n    getSpecialModulesPluginNames(\n      modules,\n      shouldTransformDynamicImport,\n      api.version,\n    ),\n    process.env.BABEL_8_BREAKING || !loose\n      ? undefined\n      : [\"transform-typeof-symbol\"],\n    pluginSyntaxMap,\n  );\n  if (shippedProposals) {\n    addProposalSyntaxPlugins(pluginNames, proposalSyntaxPlugins);\n  }\n  removeUnsupportedItems(pluginNames, api.version);\n  removeUnnecessaryItems(pluginNames, overlappingPlugins);\n\n  const polyfillPlugins = process.env.BABEL_8_BREAKING\n    ? useBuiltIns\n      ? [\n          [\n            pluginCoreJS3,\n            getCoreJSOptions({\n              useBuiltIns,\n              corejs,\n              polyfillTargets: targets,\n              include: include.builtIns,\n              exclude: exclude.builtIns,\n              proposals,\n              shippedProposals,\n              debug,\n            }),\n          ],\n        ]\n      : []\n    : getPolyfillPlugins({\n        useBuiltIns,\n        corejs,\n        polyfillTargets: targets,\n        include: include.builtIns,\n        exclude: exclude.builtIns,\n        proposals,\n        shippedProposals,\n        regenerator: pluginNames.has(\"transform-regenerator\"),\n        debug,\n      });\n\n  const pluginUseBuiltIns = useBuiltIns !== false;\n  const plugins = Array.from(pluginNames)\n    .map(pluginName => {\n      if (\n        !process.env.BABEL_8_BREAKING &&\n        (pluginName === \"transform-class-properties\" ||\n          pluginName === \"transform-private-methods\" ||\n          pluginName === \"transform-private-property-in-object\")\n      ) {\n        return [\n          getPlugin(pluginName),\n          {\n            loose: loose\n              ? \"#__internal__@babel/preset-env__prefer-true-but-false-is-ok-if-it-prevents-an-error\"\n              : \"#__internal__@babel/preset-env__prefer-false-but-true-is-ok-if-it-prevents-an-error\",\n          },\n        ];\n      }\n      if (\n        !process.env.BABEL_8_BREAKING &&\n        pluginName === \"syntax-import-attributes\"\n      ) {\n        // For backward compatibility with the import-assertions plugin, we\n        // allow the deprecated `assert` keyword.\n        return [getPlugin(pluginName), { deprecatedAssertSyntax: true }];\n      }\n      return [\n        getPlugin(pluginName),\n        process.env.BABEL_8_BREAKING\n          ? { useBuiltIns: pluginUseBuiltIns }\n          : { spec, loose, useBuiltIns: pluginUseBuiltIns },\n      ];\n    })\n    .concat(polyfillPlugins);\n\n  if (debug) {\n    console.log(\"@babel/preset-env: `DEBUG` option\");\n    console.log(\"\\nUsing targets:\");\n    console.log(JSON.stringify(prettifyTargets(targets), null, 2));\n    console.log(`\\nUsing modules transform: ${optionsModules.toString()}`);\n    console.log(\"\\nUsing plugins:\");\n    pluginNames.forEach(pluginName => {\n      logPlugin(pluginName, targets, compatData);\n    });\n\n    if (!useBuiltIns) {\n      console.log(\n        \"\\nUsing polyfills: No polyfills were added, since the `useBuiltIns` option was not set.\",\n      );\n    }\n  }\n\n  return { plugins };\n});\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.getModulesPluginNames = ({\n    modules,\n    transformations,\n    shouldTransformESM,\n    shouldTransformDynamicImport,\n    shouldTransformExportNamespaceFrom,\n  }: {\n    modules: ModuleOption;\n    transformations: typeof import(\"./module-transformations\").default;\n    shouldTransformESM: boolean;\n    shouldTransformDynamicImport: boolean;\n    shouldTransformExportNamespaceFrom: boolean;\n  }) => {\n    const modulesPluginNames = [];\n    if (modules !== false && transformations[modules]) {\n      if (shouldTransformESM) {\n        modulesPluginNames.push(transformations[modules]);\n      }\n\n      if (shouldTransformDynamicImport) {\n        if (shouldTransformESM && modules !== \"umd\") {\n          modulesPluginNames.push(\"transform-dynamic-import\");\n        } else {\n          console.warn(\n            \"Dynamic import can only be transformed when transforming ES\" +\n              \" modules to AMD, CommonJS or SystemJS.\",\n          );\n        }\n      }\n    }\n\n    if (shouldTransformExportNamespaceFrom) {\n      modulesPluginNames.push(\"transform-export-namespace-from\");\n    }\n    if (!shouldTransformDynamicImport) {\n      modulesPluginNames.push(\"syntax-dynamic-import\");\n    }\n    if (!shouldTransformExportNamespaceFrom) {\n      modulesPluginNames.push(\"syntax-export-namespace-from\");\n    }\n    modulesPluginNames.push(\"syntax-top-level-await\");\n    modulesPluginNames.push(\"syntax-import-meta\");\n\n    return modulesPluginNames;\n  };\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAKA,IAAAG,sBAAA,GAAAH,OAAA;AACA,IAAAI,iBAAA,GAAAJ,OAAA;AACA,IAAAK,iBAAA,GAAAL,OAAA;AAKA,IAAAM,kBAAA,GAAAN,OAAA;AAQA,IAAAO,0BAAA,GAAAP,OAAA;AAIA,IAAAQ,cAAA,GAAAR,OAAA;AAEA,IAAAS,yBAAA,GAAAT,OAAA;AAMA,IAAAU,iBAAA,GAAAV,OAAA;AACA,IAAAW,kBAAA,GAAAX,OAAA;AAXA,MAAMY,aAAa,GAAGC,0BAAc,CAACC,OAAO,IAAID,0BAAc;AAiBvD,SAASE,gBAAgBA,CAACC,OAAgB,EAAEC,OAAgB,EAAE;EACnE,OAAO,IAAAC,oCAAU,EAAC,WAAW,EAAEF,OAAO,EAAE;IACtCG,UAAU,EAAE;MAAE,WAAW,EAAEF;IAAQ;EACrC,CAAC,CAAC;AACJ;AAEA,SAASG,mBAAmBA,CAC1BC,IAAoC,EACpCC,SAAsB,EACtB;EACA,OAAOC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,MAAM,CAAC,CAACC,MAAM,EAAEC,IAAI,KAAK;IAChD,IAAI,CAACL,SAAS,CAACM,GAAG,CAACD,IAAI,CAAC,EAAE;MAExBD,MAAM,CAACC,IAAI,CAAC,GAAGN,IAAI,CAACM,IAAI,CAAC;IAC3B;IAEA,OAAOD,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,MAAMG,wBAAwB,GAAGN,MAAM,CAACO,MAAM,CAC5C,CAAC,CAAC,EACFC,0BAAW,EACXC,kCACF,CAAC;AACD,MAAMC,2BAA2B,GAAGb,mBAAmB,CACrDS,wBAAwB,EACxBK,iCACF,CAAC;AAEkC;EAEjC,IAAIC,kCAAkC,GAAGJ,0BAAW;EAEpD,IAAIK,qCAAqC,GAAGhB,mBAAmB,CAC7DW,0BAAW,EACXG,iCACF,CAAC;AACH;AAEA,MAAMG,SAAS,GAAIC,UAAkB,IAAK;EACxC,MAAMC,MAAM,GAEVC,yBAAgB,CAACF,UAAU,CAAC,CAAC,CAAC;EAEhC,IAAI,CAACC,MAAM,EAAE;IACX,MAAM,IAAIE,KAAK,CACb,0BAA0BH,UAAU,+DACtC,CAAC;EACH;EAEA,OAAOC,MAAM;AACf,CAAC;AAEM,MAAMG,4BAA4B,GAAIC,IAAmB,IAAU;EACxE,OAAOA,IAAI,CAAClB,MAAM,CAChB,CAACC,MAAM,EAAEkB,GAAG,KAAK;IACf,MAAMC,MAAM,GAAG,8BAA8B,CAACC,IAAI,CAACF,GAAG,CAAC,GACnD,UAAU,GACV,SAAS;IACblB,MAAM,CAACmB,MAAM,CAAC,CAACE,GAAG,CAACH,GAAG,CAAC;IACvB,OAAOlB,MAAM;EACf,CAAC,EACD;IACEsB,GAAG,EAAEL,IAAI;IACTM,OAAO,EAAE,IAAIC,GAAG,CAAC,CAAC;IAClBC,QAAQ,EAAE,IAAID,GAAG,CAAC;EACpB,CACF,CAAC;AACH,CAAC;AAACE,OAAA,CAAAV,4BAAA,GAAAA,4BAAA;AAEF,SAASW,4BAA4BA,CACnCC,OAAsC,EACtCC,4BAAqC,EACrCC,YAAoB,EACpB;EACA,MAAMC,kBAAkB,GAAG,EAAE;EAC7B,IAAIH,OAAO,EAAE;IACXG,kBAAkB,CAACC,IAAI,CAACC,8BAAqB,CAACL,OAAO,CAAC,CAAC;EACzD;EAEA,IAAIC,4BAA4B,EAAE;IAChC,IAAID,OAAO,IAAIA,OAAO,KAAK,KAAK,EAAE;MAChCG,kBAAkB,CAACC,IAAI,CAAC,0BAA0B,CAAC;IACrD,CAAC,MAAM;MACLE,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3D,wCACJ,CAAC;IACH;EACF;EAEA,IAAqCL,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAE5D,IAAI,CAACD,4BAA4B,EAAE;MACjCE,kBAAkB,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAClD;IACAD,kBAAkB,CAACC,IAAI,CAAC,wBAAwB,CAAC;IACjDD,kBAAkB,CAACC,IAAI,CAAC,oBAAoB,CAAC;EAC/C;EAEA,OAAOD,kBAAkB;AAC3B;AAEA,MAAMK,gBAAgB,GAAGA,CAAC;EACxBC,WAAW;EACXC,MAAM;EACNC,eAAe;EACfC,OAAO;EACPC,OAAO;EACPC,SAAS;EACTC,gBAAgB;EAChBC;AAUF,CAAC,MAAM;EACLC,MAAM,EAAE,GAAGR,WAAW,SAAS;EAC/BS,OAAO,EAAER,MAAM,GAAGA,MAAM,CAACS,QAAQ,CAAC,CAAC,GAAGC,SAAS;EAC/C1D,OAAO,EAAEiD,eAAe;EACxBC,OAAO;EACPC,OAAO;EACPC,SAAS;EACTC,gBAAgB;EAChBC,KAAK;EACL,iDAAiD,EAAE;IACjDK,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AAEiC;EAEjC,IAAIC,kBAAkB,GAAGA,CAAC;IACxBb,WAAW;IACXC,MAAM;IACNC,eAAe;IACfC,OAAO;IACPC,OAAO;IACPC,SAAS;IACTC,gBAAgB;IAChBQ,WAAW;IACXP;EAWF,CAAC,KAAK;IACJ,MAAMQ,eAAe,GAAG,EAAE;IAC1B,IAAIf,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,OAAO,EAAE;MACtD,MAAMgB,aAAa,GAAGjB,gBAAgB,CAAC;QACrCC,WAAW;QACXC,MAAM;QACNC,eAAe;QACfC,OAAO;QACPC,OAAO;QACPC,SAAS;QACTC,gBAAgB;QAChBC;MACF,CAAC,CAAC;MAEF,IAAIN,MAAM,EAAE;QAGH;UACL,IAAID,WAAW,KAAK,OAAO,EAAE;YAC3B,IAAIC,MAAM,CAACgB,KAAK,KAAK,CAAC,EAAE;cACtBF,eAAe,CAACpB,IAAI,CAClB,CAACuB,cAAM,CAACC,aAAa,EAAEH,aAAa,CAAC,EACrC,CAACE,cAAM,CAACE,yBAAyB,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAC,CACpD,CAAC;YACH,CAAC,MAAM;cACLN,eAAe,CAACpB,IAAI,CAClB,CAAC9C,aAAa,EAAEmE,aAAa,CAAC,EAC9B,CACEE,cAAM,CAACE,yBAAyB,EAChC;gBAAEC,KAAK,EAAE,IAAI;gBAAEC,UAAU,EAAE;cAAK,CAAC,CAErC,CAAC;YACH;YACA,IAAIR,WAAW,EAAE;cACfC,eAAe,CAACpB,IAAI,CAAC,CACnBuB,cAAM,CAACK,iBAAiB,EACxB;gBAAEf,MAAM,EAAE,cAAc;gBAAED;cAAM,CAAC,CAClC,CAAC;YACJ;UACF,CAAC,MAAM;YACL,IAAIN,MAAM,CAACgB,KAAK,KAAK,CAAC,EAAE;cACtBF,eAAe,CAACpB,IAAI,CAClB,CAACuB,cAAM,CAACE,yBAAyB,EAAE;gBAAEN;cAAY,CAAC,CAAC,EACnD,CAACI,cAAM,CAACC,aAAa,EAAEH,aAAa,CACtC,CAAC;YACH,CAAC,MAAM;cACLD,eAAe,CAACpB,IAAI,CAClB,CAAC9C,aAAa,EAAEmE,aAAa,CAAC,EAC9B,CAACE,cAAM,CAACE,yBAAyB,EAAE;gBAAEE,UAAU,EAAE;cAAK,CAAC,CACzD,CAAC;cACD,IAAI,CAACR,WAAW,EAAE;gBAChBC,eAAe,CAACpB,IAAI,CAAC,CACnBuB,cAAM,CAACM,4BAA4B,EACnCR,aAAa,CACd,CAAC;cACJ;YACF;UACF;QACF;MACF;IACF;IACA,OAAOD,eAAe;EACxB,CAAC;EAEa;IAEZ1B,OAAO,CAACwB,kBAAkB,GAAGA,kBAAkB;EACjD;AACF;AAEA,SAASY,eAAeA,CACtBC,cAAkC,EAClCC,wBAAiC,EACjCC,UAAkB,EAClBC,eAAuB,EACvBC,GAAc,EACd;EACA,IAAIJ,cAAc,YAAdA,cAAc,CAAEK,SAAS,IAAIL,cAAc,CAACM,QAAQ,EAAE;IACxDnC,OAAO,CAACC,IAAI,CAAC;AACjB;AACA,yBAAyB4B,cAAc,CAACM,QAAQ,CAACtB,QAAQ,CAAC,CAAC;AAC3D,CAAC,CAAC;EACA;EAEA,OAAO,IAAAuB,iCAAU,EAACP,cAAc,EAAkB;IAChDC,wBAAwB;IACxBC,UAAU;IACVC,eAAe;IACfK,yBAAyBA,CAACC,MAAM,EAAE;MAChCL,GAAG,CAACM,qBAAqB,CAACD,MAAM,CAAC;IACnC;EACF,CAAC,CAAC;AACJ;AAEA,SAASE,iBAAiBA,CAACC,MAAkC,EAAE;EAE7D,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAED,iBAAiB;AACpC;AAEA,SAASE,qBAAqBA,CAACD,MAAkC,EAAE;EAEjE,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAEC,qBAAqB;AACxC;AAEA,SAASC,2BAA2BA,CAACF,MAAkC,EAAE;EAEvE,OAAO,CAAC,EAACA,MAAM,YAANA,MAAM,CAAEE,2BAA2B;AAC9C;AAAC,IAAAC,QAAA,GAAApD,OAAA,CAAAtC,OAAA,GAEc,IAAA2F,gCAAa,EAAC,CAACZ,GAAG,EAAElD,IAAa,KAAK;EACnDkD,GAAG,CAACa,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,YAAY,GAAGd,GAAG,CAAC7E,OAAO,CAAC,CAAC;EAAC;EAUnC,MAAM;IACJ2E,UAAU;IACVrB,KAAK;IACLH,OAAO,EAAEyC,cAAc;IACvBC,kBAAkB;IAClBnB,wBAAwB;IACxBxB,OAAO,EAAE4C,cAAc;IACvBxD,OAAO,EAAEyD,cAAc;IACvB1C,gBAAgB;IAChBrD,OAAO,EAAEyE,cAAc;IACvB1B,WAAW;IACXC,MAAM,EAAE;MAAEQ,OAAO,EAAER,MAAM;MAAEI;IAAU,CAAC;IACtCwB;EACF,CAAC,GAAG,IAAAoB,yBAAgB,EAACrE,IAAI,CAAC;EAES;IAEjC,IAAI;MAAEsE,KAAK;MAAEC,IAAI,GAAG,KAAK;MAAEC,QAAQ,GAAG;IAAM,CAAC,GAAGxE,IAAI;EACtD;EAEA,IAAI3B,OAAO,GAAG2F,YAAY;EAE1B,IAIES,OAAM,CAACC,EAAE,CAACxB,GAAG,CAACrB,OAAO,EAAE,QAAQ,CAAC,IAGhC7B,IAAI,CAAC3B,OAAO,IACZ2B,IAAI,CAACgD,UAAU,IACfhD,IAAI,CAACiD,eAAe,IACpBjD,IAAI,CAAC+C,wBAAwB,EAC7B;IACmC;MAEjC,IAAI4B,eAAe,GAAG,KAAK;MAE3B,IAAI7B,cAAc,YAAdA,cAAc,CAAE8B,MAAM,EAAE;QAC1BD,eAAe,GAAG,IAAI;QACtB,OAAO7B,cAAc,CAAC8B,MAAM;QAE5B3D,OAAO,CAACC,IAAI,CAAC;AACrB;AACA;AACA,CAAC,CAAC;MACI;IACF;IAEA7C,OAAO,GAAGwE,eAAe,CACvBC,cAAc,EACdC,wBAAwB,EACxBC,UAAU,EACVC,eAAe,EACfC,GACF,CAAC;EACH;EAEA,MAAM2B,gBAAgB,GAGhBX,kBAAkB,IAAIS,eAAe,GAEtC,CAAC,CAAC,GACHtG,OAAO;EAEX,MAAMkD,OAAO,GAAGxB,4BAA4B,CAACoE,cAAc,CAAC;EAC5D,MAAM3C,OAAO,GAAGzB,4BAA4B,CAACkE,cAAc,CAAC;EAE5D,MAAMzF,UAAU,GACkBgG,QAAQ,GACpC9C,gBAAgB,GACdxC,wBAAwB,GACxBI,2BAA2B,GAC7BoC,gBAAgB,GACdlC,kCAAkC,GAClCC,qCAAqC;EAC7C,MAAMkB,OAAO,GACXyD,cAAc,KAAK,MAAM,GACrBlB,GAAG,CAACQ,MAAM,CAACD,iBAAiB,CAAC,GAC3B,KAAK,GACL,UAAU,GACZW,cAAc;EACpB,MAAMxD,4BAA4B,GAChCwD,cAAc,KAAK,MAAM,GAAG,CAAClB,GAAG,CAACQ,MAAM,CAACC,qBAAqB,CAAC,GAAG,CAAC,CAAChD,OAAO;EAO5E,IACE,CAACa,OAAO,CAAClB,OAAO,CAACrB,GAAG,CAAC,iCAAiC,CAAC,KACtDmF,cAAc,KAAK,MAAM,GACtB,CAAClB,GAAG,CAACQ,MAAM,CAACE,2BAA2B,CAAC,GACxC,CAAC,CAACjD,OAAO,CAAC,EACd;IACAY,OAAO,CAACjB,OAAO,CAACF,GAAG,CAAC,iCAAiC,CAAC;EACxD;EAEA,MAAM0E,WAAW,GAAG,IAAAC,qCAAW,EAC7BvG,UAAU,EACV+C,OAAO,CAACjB,OAAO,EACfkB,OAAO,CAAClB,OAAO,EACfuE,gBAAgB,EAChBnE,4BAA4B,CAC1BC,OAAO,EACPC,4BAA4B,EAC5BsC,GAAG,CAACrB,OACN,CAAC,EAC+B,CAACyC,KAAK,GAClCvC,SAAS,GACT,CAAC,yBAAyB,CAAC,EAC/BiD,iCACF,CAAC;EACD,IAAItD,gBAAgB,EAAE;IACpB,IAAAuD,qCAAwB,EAACH,WAAW,EAAEI,uCAAqB,CAAC;EAC9D;EACA,IAAAC,mCAAsB,EAACL,WAAW,EAAE5B,GAAG,CAACrB,OAAO,CAAC;EAChD,IAAAuD,mCAAsB,EAACN,WAAW,EAAEO,qCAAkB,CAAC;EAEvD,MAAMlD,eAAe,GAkBjBF,kBAAkB,CAAC;IACjBb,WAAW;IACXC,MAAM;IACNC,eAAe,EAAEjD,OAAO;IACxBkD,OAAO,EAAEA,OAAO,CAACf,QAAQ;IACzBgB,OAAO,EAAEA,OAAO,CAAChB,QAAQ;IACzBiB,SAAS;IACTC,gBAAgB;IAChBQ,WAAW,EAAE4C,WAAW,CAAC7F,GAAG,CAAC,uBAAuB,CAAC;IACrD0C;EACF,CAAC,CAAC;EAEN,MAAM2D,iBAAiB,GAAGlE,WAAW,KAAK,KAAK;EAC/C,MAAMd,OAAO,GAAGiF,KAAK,CAACC,IAAI,CAACV,WAAW,CAAC,CACpCW,GAAG,CAAC9F,UAAU,IAAI;IACjB,IAEGA,UAAU,KAAK,4BAA4B,IAC1CA,UAAU,KAAK,2BAA2B,IAC1CA,UAAU,KAAK,sCAAsC,EACvD;MACA,OAAO,CACLD,SAAS,CAACC,UAAU,CAAC,EACrB;QACE2E,KAAK,EAAEA,KAAK,GACR,qFAAqF,GACrF;MACN,CAAC,CACF;IACH;IACA,IAEE3E,UAAU,KAAK,0BAA0B,EACzC;MAGA,OAAO,CAACD,SAAS,CAACC,UAAU,CAAC,EAAE;QAAE+F,sBAAsB,EAAE;MAAK,CAAC,CAAC;IAClE;IACA,OAAO,CACLhG,SAAS,CAACC,UAAU,CAAC,EAGjB;MAAE4E,IAAI;MAAED,KAAK;MAAElD,WAAW,EAAEkE;IAAkB,CAAC,CACpD;EACH,CAAC,CAAC,CACDK,MAAM,CAACxD,eAAe,CAAC;EAE1B,IAAIR,KAAK,EAAE;IACTV,OAAO,CAAC2E,GAAG,CAAC,mCAAmC,CAAC;IAChD3E,OAAO,CAAC2E,GAAG,CAAC,kBAAkB,CAAC;IAC/B3E,OAAO,CAAC2E,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,IAAAC,yCAAe,EAAC1H,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9D4C,OAAO,CAAC2E,GAAG,CAAC,8BAA8BxB,cAAc,CAACtC,QAAQ,CAAC,CAAC,EAAE,CAAC;IACtEb,OAAO,CAAC2E,GAAG,CAAC,kBAAkB,CAAC;IAC/Bd,WAAW,CAACkB,OAAO,CAACrG,UAAU,IAAI;MAChC,IAAAsG,gBAAS,EAACtG,UAAU,EAAEtB,OAAO,EAAEG,UAAU,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAI,CAAC4C,WAAW,EAAE;MAChBH,OAAO,CAAC2E,GAAG,CACT,yFACF,CAAC;IACH;EACF;EAEA,OAAO;IAAEtF;EAAQ,CAAC;AACpB,CAAC,CAAC;AAE6C;EAE7CG,OAAO,CAACyF,qBAAqB,GAAG,CAAC;IAC/BvF,OAAO;IACPwF,eAAe;IACfC,kBAAkB;IAClBxF,4BAA4B;IAC5ByF;EAOF,CAAC,KAAK;IACJ,MAAMvF,kBAAkB,GAAG,EAAE;IAC7B,IAAIH,OAAO,KAAK,KAAK,IAAIwF,eAAe,CAACxF,OAAO,CAAC,EAAE;MACjD,IAAIyF,kBAAkB,EAAE;QACtBtF,kBAAkB,CAACC,IAAI,CAACoF,eAAe,CAACxF,OAAO,CAAC,CAAC;MACnD;MAEA,IAAIC,4BAA4B,EAAE;QAChC,IAAIwF,kBAAkB,IAAIzF,OAAO,KAAK,KAAK,EAAE;UAC3CG,kBAAkB,CAACC,IAAI,CAAC,0BAA0B,CAAC;QACrD,CAAC,MAAM;UACLE,OAAO,CAACC,IAAI,CACV,6DAA6D,GAC3D,wCACJ,CAAC;QACH;MACF;IACF;IAEA,IAAImF,kCAAkC,EAAE;MACtCvF,kBAAkB,CAACC,IAAI,CAAC,iCAAiC,CAAC;IAC5D;IACA,IAAI,CAACH,4BAA4B,EAAE;MACjCE,kBAAkB,CAACC,IAAI,CAAC,uBAAuB,CAAC;IAClD;IACA,IAAI,CAACsF,kCAAkC,EAAE;MACvCvF,kBAAkB,CAACC,IAAI,CAAC,8BAA8B,CAAC;IACzD;IACAD,kBAAkB,CAACC,IAAI,CAAC,wBAAwB,CAAC;IACjDD,kBAAkB,CAACC,IAAI,CAAC,oBAAoB,CAAC;IAE7C,OAAOD,kBAAkB;EAC3B,CAAC;AACH", "ignoreList": []}