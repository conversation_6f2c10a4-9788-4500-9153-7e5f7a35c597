"""
Database connection and operations for the supermarket scraper.
"""

import os
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from pymongo import IndexModel, ASCENDING, DESCENDING, TEXT
from pymongo.errors import DuplicateKeyError

from .models import Product, ScrapingSession, SearchQuery, ProductStats, MarketEnum


logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self, mongodb_url: str):
        self.mongodb_url = mongodb_url
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.products_collection: Optional[AsyncIOMotorCollection] = None
        self.sessions_collection: Optional[AsyncIOMotorCollection] = None
    
    async def connect(self):
        """Connect to MongoDB and initialize collections."""
        try:
            self.client = AsyncIOMotorClient(self.mongodb_url)
            self.database = self.client.supermarket_data
            self.products_collection = self.database.products
            self.sessions_collection = self.database.scraping_sessions
            
            # Test connection
            await self.client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")
            
            # Create indexes
            await self._create_indexes()
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from MongoDB."""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    async def _create_indexes(self):
        """Create database indexes for optimal performance."""
        try:
            # Products collection indexes
            product_indexes = [
                IndexModel([("market", ASCENDING), ("market_product_id", ASCENDING)], unique=True),
                IndexModel([("name", TEXT), ("brand", TEXT), ("description", TEXT)]),
                IndexModel([("market", ASCENDING)]),
                IndexModel([("category", ASCENDING)]),
                IndexModel([("price.current", ASCENDING)]),
                IndexModel([("availability", ASCENDING)]),
                IndexModel([("scraped_at", DESCENDING)]),
                IndexModel([("last_updated", DESCENDING)]),
                IndexModel([("promotions.type", ASCENDING)]),
            ]
            
            await self.products_collection.create_indexes(product_indexes)
            
            # Sessions collection indexes
            session_indexes = [
                IndexModel([("session_id", ASCENDING)], unique=True),
                IndexModel([("market", ASCENDING)]),
                IndexModel([("started_at", DESCENDING)]),
                IndexModel([("status", ASCENDING)]),
            ]
            
            await self.sessions_collection.create_indexes(session_indexes)
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
    
    async def save_product(self, product: Product) -> bool:
        """Save or update a product in the database."""
        try:
            # Create unique identifier
            filter_query = {
                "market": product.market,
                "market_product_id": product.market_product_id or product.url
            }
            
            # Update timestamp
            product.last_updated = datetime.utcnow()
            
            # Upsert product
            result = await self.products_collection.replace_one(
                filter_query,
                product.dict(),
                upsert=True
            )
            
            return result.acknowledged
            
        except Exception as e:
            logger.error(f"Failed to save product {product.name}: {e}")
            return False
    
    async def save_products_batch(self, products: List[Product]) -> int:
        """Save multiple products in a batch operation."""
        if not products:
            return 0
        
        try:
            operations = []
            for product in products:
                filter_query = {
                    "market": product.market,
                    "market_product_id": product.market_product_id or product.url
                }
                
                product.last_updated = datetime.utcnow()
                
                operations.append({
                    "replaceOne": {
                        "filter": filter_query,
                        "replacement": product.dict(),
                        "upsert": True
                    }
                })
            
            result = await self.products_collection.bulk_write(operations)
            return result.upserted_count + result.modified_count
            
        except Exception as e:
            logger.error(f"Failed to save products batch: {e}")
            return 0
    
    async def search_products(self, search_query: SearchQuery) -> List[Dict[str, Any]]:
        """Search products based on query parameters."""
        try:
            # Build MongoDB query
            query = {}
            
            if search_query.query:
                query["$text"] = {"$search": search_query.query}
            
            if search_query.market:
                query["market"] = search_query.market
            
            if search_query.category:
                query["category"] = {"$regex": search_query.category, "$options": "i"}
            
            if search_query.min_price is not None or search_query.max_price is not None:
                price_query = {}
                if search_query.min_price is not None:
                    price_query["$gte"] = search_query.min_price
                if search_query.max_price is not None:
                    price_query["$lte"] = search_query.max_price
                query["price.current"] = price_query
            
            if search_query.availability:
                query["availability"] = search_query.availability
            
            if search_query.has_promotion is not None:
                if search_query.has_promotion:
                    query["promotions"] = {"$ne": []}
                else:
                    query["promotions"] = {"$eq": []}
            
            # Build sort criteria
            sort_criteria = [(search_query.sort_by, search_query.sort_order)]
            
            # Execute query
            cursor = self.products_collection.find(query).sort(sort_criteria)
            cursor = cursor.skip(search_query.skip).limit(search_query.limit)
            
            products = await cursor.to_list(length=search_query.limit)
            return products
            
        except Exception as e:
            logger.error(f"Failed to search products: {e}")
            return []
    
    async def get_product_stats(self) -> ProductStats:
        """Get statistics about products in the database."""
        try:
            # Total products
            total_products = await self.products_collection.count_documents({})
            
            # Products by market
            market_pipeline = [
                {"$group": {"_id": "$market", "count": {"$sum": 1}}}
            ]
            market_results = await self.products_collection.aggregate(market_pipeline).to_list(None)
            products_by_market = {item["_id"]: item["count"] for item in market_results}
            
            # Products by category
            category_pipeline = [
                {"$match": {"category": {"$ne": None}}},
                {"$group": {"_id": "$category", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}},
                {"$limit": 20}
            ]
            category_results = await self.products_collection.aggregate(category_pipeline).to_list(None)
            products_by_category = {item["_id"]: item["count"] for item in category_results}
            
            # Average price by market
            price_pipeline = [
                {"$group": {
                    "_id": "$market",
                    "avg_price": {"$avg": "$price.current"}
                }}
            ]
            price_results = await self.products_collection.aggregate(price_pipeline).to_list(None)
            average_price_by_market = {item["_id"]: round(item["avg_price"], 2) for item in price_results}
            
            # Products with promotions
            products_with_promotions = await self.products_collection.count_documents({
                "promotions": {"$ne": []}
            })
            
            # Out of stock products
            out_of_stock_products = await self.products_collection.count_documents({
                "availability": "out_of_stock"
            })
            
            # Last update
            last_product = await self.products_collection.find_one(
                {},
                sort=[("last_updated", DESCENDING)]
            )
            last_update = last_product["last_updated"] if last_product else datetime.utcnow()
            
            return ProductStats(
                total_products=total_products,
                products_by_market=products_by_market,
                products_by_category=products_by_category,
                average_price_by_market=average_price_by_market,
                last_update=last_update,
                products_with_promotions=products_with_promotions,
                out_of_stock_products=out_of_stock_products
            )
            
        except Exception as e:
            logger.error(f"Failed to get product stats: {e}")
            return ProductStats(
                total_products=0,
                products_by_market={},
                products_by_category={},
                average_price_by_market={},
                last_update=datetime.utcnow(),
                products_with_promotions=0,
                out_of_stock_products=0
            )
    
    async def save_scraping_session(self, session: ScrapingSession) -> bool:
        """Save a scraping session record."""
        try:
            await self.sessions_collection.replace_one(
                {"session_id": session.session_id},
                session.dict(),
                upsert=True
            )
            return True
        except Exception as e:
            logger.error(f"Failed to save scraping session: {e}")
            return False
    
    async def cleanup_old_data(self, retention_days: int = 30):
        """Remove old product data beyond retention period."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            # Remove old products
            result = await self.products_collection.delete_many({
                "last_updated": {"$lt": cutoff_date}
            })
            
            # Remove old sessions
            session_result = await self.sessions_collection.delete_many({
                "started_at": {"$lt": cutoff_date}
            })
            
            logger.info(f"Cleaned up {result.deleted_count} old products and {session_result.deleted_count} old sessions")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")


# Global database manager instance
db_manager = DatabaseManager(os.getenv("MONGODB_URL", "mongodb://localhost:27017/supermarket_data"))
