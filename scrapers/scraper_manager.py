"""
Scraper manager to coordinate all supermarket scrapers.
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from data.models import ScrapingConfig, MarketEnum, Product
from data.database import db_manager
from .coto_scraper import CotoScraper
from .carrefour_scraper import CarrefourScraper
from .jumbo_scraper import Jumbo<PERSON>craper
from .disco_scraper import Disco<PERSON><PERSON>raper
from .vea_scraper import VeaScraper

logger = logging.getLogger(__name__)


class ScraperManager:
    """Manages and coordinates all supermarket scrapers."""
    
    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.scrapers = {}
        self._initialize_scrapers()
    
    def _initialize_scrapers(self):
        """Initialize all available scrapers."""
        scraper_classes = {
            MarketEnum.COTO: CotoScraper,
            MarketEnum.CARREFOUR: CarrefourScraper,
            MarketEnum.JUMBO: JumboScraper,
            MarketEnum.DISCO: DiscoScraper,
            MarketEnum.VEA: VeaScraper
        }
        
        for market, scraper_class in scraper_classes.items():
            if market in self.config.markets_enabled:
                try:
                    self.scrapers[market] = scraper_class(self.config)
                    logger.info(f"Initialized scraper for {market}")
                except Exception as e:
                    logger.error(f"Failed to initialize scraper for {market}: {e}")
    
    async def scrape_all_markets(self, categories: Optional[List[str]] = None) -> Dict[str, int]:
        """Scrape all enabled markets."""
        results = {}
        
        # Connect to database
        await db_manager.connect()
        
        try:
            # Run scrapers concurrently (with some delay to avoid overwhelming servers)
            tasks = []
            for market, scraper in self.scrapers.items():
                task = asyncio.create_task(
                    self._scrape_market_with_delay(scraper, categories, len(tasks) * 30)
                )
                tasks.append((market, task))
            
            # Wait for all tasks to complete
            for market, task in tasks:
                try:
                    product_count = await task
                    results[market.value] = product_count
                    logger.info(f"Completed scraping {market}: {product_count} products")
                except Exception as e:
                    logger.error(f"Error scraping {market}: {e}")
                    results[market.value] = 0
        
        finally:
            await db_manager.disconnect()
        
        return results
    
    async def _scrape_market_with_delay(self, scraper, categories: Optional[List[str]], delay: int) -> int:
        """Scrape a single market with initial delay."""
        if delay > 0:
            logger.info(f"Waiting {delay} seconds before starting {scraper.market}")
            await asyncio.sleep(delay)
        
        return await scraper.run_scraping(categories)
    
    async def scrape_single_market(self, market: MarketEnum, categories: Optional[List[str]] = None) -> int:
        """Scrape a single market."""
        if market not in self.scrapers:
            logger.error(f"Scraper for {market} not available")
            return 0
        
        await db_manager.connect()
        
        try:
            scraper = self.scrapers[market]
            return await scraper.run_scraping(categories)
        finally:
            await db_manager.disconnect()
    
    async def get_scraping_status(self) -> Dict[str, Any]:
        """Get status of all scrapers."""
        status = {
            "scrapers_available": len(self.scrapers),
            "markets_enabled": [market.value for market in self.config.markets_enabled],
            "last_run": None,
            "total_products": 0
        }
        
        try:
            await db_manager.connect()
            
            # Get latest scraping sessions
            latest_sessions = {}
            for market in self.scrapers.keys():
                # This would query the latest session for each market
                # For now, we'll just indicate the scrapers are ready
                latest_sessions[market.value] = {
                    "status": "ready",
                    "last_run": None,
                    "products_found": 0
                }
            
            status["latest_sessions"] = latest_sessions
            
            # Get total products in database
            stats = await db_manager.get_product_stats()
            status["total_products"] = stats.total_products
            status["last_update"] = stats.last_update
            
        except Exception as e:
            logger.error(f"Error getting scraping status: {e}")
        finally:
            await db_manager.disconnect()
        
        return status
    
    def cleanup_all_scrapers(self):
        """Clean up all scraper resources."""
        for scraper in self.scrapers.values():
            try:
                scraper.cleanup()
            except Exception as e:
                logger.warning(f"Error cleaning up scraper: {e}")


async def run_scheduled_scraping(config: ScrapingConfig, categories: Optional[List[str]] = None):
    """Run scheduled scraping of all markets."""
    logger.info("Starting scheduled scraping run")
    
    manager = ScraperManager(config)
    
    try:
        results = await manager.scrape_all_markets(categories)
        
        total_products = sum(results.values())
        logger.info(f"Scheduled scraping completed. Total products scraped: {total_products}")
        
        # Log results for each market
        for market, count in results.items():
            logger.info(f"{market}: {count} products")
        
        return results
        
    except Exception as e:
        logger.error(f"Error during scheduled scraping: {e}")
        return {}
    
    finally:
        manager.cleanup_all_scrapers()


if __name__ == "__main__":
    # Example usage
    import os
    from dotenv import load_dotenv
    
    load_dotenv()
    
    # Create configuration
    config = ScrapingConfig(
        rate_limit_delay=float(os.getenv("RATE_LIMIT_DELAY", 2.0)),
        max_retries=int(os.getenv("MAX_RETRIES", 3)),
        timeout=int(os.getenv("TIMEOUT", 30)),
        headless=os.getenv("HEADLESS_BROWSER", "true").lower() == "true",
        user_agent_rotation=os.getenv("USER_AGENT_ROTATION", "true").lower() == "true",
        markets_enabled=[
            market for market in MarketEnum 
            if os.getenv(f"{market.value.upper()}_ENABLED", "true").lower() == "true"
        ]
    )
    
    # Run scraping
    asyncio.run(run_scheduled_scraping(config))
