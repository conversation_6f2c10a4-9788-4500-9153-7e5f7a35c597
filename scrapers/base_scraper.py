"""
Base scraper class with common functionality for all supermarket scrapers.
"""

import asyncio
import logging
import random
import time
import sys
import os
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Set
from urllib.parse import urljoin, urlparse
from urllib.robotparser import RobotFileParser

import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from fake_useragent import UserAgent

# Add project root to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.models import Product, MarketEnum, ScrapingSession, ScrapingConfig
from data.database import db_manager


logger = logging.getLogger(__name__)


class BaseScraper(ABC):
    """Base class for all supermarket scrapers."""
    
    def __init__(self, market: MarketEnum, config: ScrapingConfig):
        self.market = market
        self.config = config
        self.session = requests.Session()
        self.driver: Optional[webdriver.Chrome] = None
        self.user_agent = UserAgent()
        self.scraped_urls: Set[str] = set()
        self.session_id = f"{market}_{int(time.time())}"
        self.scraping_session: Optional[ScrapingSession] = None
        
        # Rate limiting
        self.last_request_time = 0
        
        # Setup session headers
        self._setup_session()
    
    def _setup_session(self):
        """Setup HTTP session with appropriate headers."""
        headers = {
            'User-Agent': self.user_agent.random if self.config.user_agent_rotation else self.user_agent.chrome,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'es-AR,es;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(headers)
    
    def _setup_driver(self) -> webdriver.Chrome:
        """Setup Selenium Chrome driver with appropriate options."""
        chrome_options = Options()

        if self.config.headless:
            chrome_options.add_argument('--headless')

        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # User agent
        if self.config.user_agent_rotation:
            chrome_options.add_argument(f'--user-agent={self.user_agent.random}')

        # Additional stealth options
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins-discovery')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')

        try:
            # Try to use ChromeDriverManager first
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e:
            logger.warning(f"ChromeDriverManager failed: {e}, trying system chromedriver")
            try:
                # Fallback to system chromedriver
                service = Service()  # Uses system PATH
                driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e2:
                logger.error(f"Failed to initialize Chrome driver: {e2}")
                raise

        # Execute script to remove webdriver property
        try:
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        except Exception as e:
            logger.warning(f"Could not execute stealth script: {e}")

        return driver
    
    def _rate_limit(self):
        """Implement rate limiting between requests."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.config.rate_limit_delay:
            sleep_time = self.config.rate_limit_delay - time_since_last_request
            # Add some randomness to avoid detection
            sleep_time += random.uniform(0, 0.5)
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _check_robots_txt(self, url: str) -> bool:
        """Check if URL is allowed by robots.txt."""
        try:
            parsed_url = urlparse(url)
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            
            rp = RobotFileParser()
            rp.set_url(robots_url)
            rp.read()
            
            user_agent = self.session.headers.get('User-Agent', '*')
            return rp.can_fetch(user_agent, url)
            
        except Exception as e:
            logger.warning(f"Could not check robots.txt for {url}: {e}")
            return True  # Allow if we can't check
    
    def _make_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """Make HTTP request with rate limiting and error handling."""
        if not self._check_robots_txt(url):
            logger.warning(f"URL blocked by robots.txt: {url}")
            return None
        
        self._rate_limit()
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.get(url, timeout=self.config.timeout, **kwargs)
                response.raise_for_status()
                return response
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request failed (attempt {attempt + 1}/{self.config.max_retries}): {e}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    logger.error(f"All retry attempts failed for {url}")
        
        return None
    
    def _navigate_with_driver(self, url: str) -> bool:
        """Navigate to URL using Selenium driver."""
        if not self._check_robots_txt(url):
            logger.warning(f"URL blocked by robots.txt: {url}")
            return False
        
        self._rate_limit()
        
        try:
            if not self.driver:
                self.driver = self._setup_driver()
            
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            return True
            
        except (TimeoutException, WebDriverException) as e:
            logger.error(f"Failed to navigate to {url}: {e}")
            return False
    
    def _extract_price(self, price_text: str) -> Optional[float]:
        """Extract numeric price from text."""
        if not price_text:
            return None
        
        try:
            # Remove currency symbols and common text
            price_clean = price_text.replace('$', '').replace(',', '').replace('.', '')
            price_clean = ''.join(filter(str.isdigit, price_clean))
            
            if price_clean:
                # Assume last two digits are cents
                if len(price_clean) > 2:
                    return float(price_clean[:-2] + '.' + price_clean[-2:])
                else:
                    return float(price_clean)
            
        except (ValueError, AttributeError):
            pass
        
        return None
    
    def _normalize_product_name(self, name: str) -> str:
        """Normalize product name for consistency."""
        if not name:
            return ""
        
        # Remove extra whitespace and normalize
        name = ' '.join(name.split())
        
        # Remove common prefixes/suffixes that don't add value
        prefixes_to_remove = ['producto:', 'item:', 'art:']
        for prefix in prefixes_to_remove:
            if name.lower().startswith(prefix):
                name = name[len(prefix):].strip()
        
        return name.title()
    
    async def _start_scraping_session(self):
        """Initialize scraping session tracking."""
        self.scraping_session = ScrapingSession(
            session_id=self.session_id,
            market=self.market,
            scraper_config=self.config.dict()
        )
        
        await db_manager.save_scraping_session(self.scraping_session)
        logger.info(f"Started scraping session {self.session_id} for {self.market}")
    
    async def _update_scraping_session(self, **kwargs):
        """Update scraping session with new data."""
        if self.scraping_session:
            for key, value in kwargs.items():
                if hasattr(self.scraping_session, key):
                    setattr(self.scraping_session, key, value)
            
            await db_manager.save_scraping_session(self.scraping_session)
    
    async def _finish_scraping_session(self, status: str = "completed"):
        """Finalize scraping session."""
        if self.scraping_session:
            self.scraping_session.status = status
            self.scraping_session.completed_at = time.time()
            
            await db_manager.save_scraping_session(self.scraping_session)
            logger.info(f"Finished scraping session {self.session_id} with status: {status}")
    
    def cleanup(self):
        """Clean up resources."""
        if self.driver:
            try:
                self.driver.quit()
            except Exception as e:
                logger.warning(f"Error closing driver: {e}")
        
        if self.session:
            self.session.close()
    
    @abstractmethod
    async def scrape_products(self, categories: Optional[List[str]] = None) -> List[Product]:
        """Scrape products from the supermarket website."""
        pass
    
    @abstractmethod
    def get_product_urls(self, category: Optional[str] = None) -> List[str]:
        """Get list of product URLs to scrape."""
        pass
    
    @abstractmethod
    def extract_product_data(self, url: str) -> Optional[Product]:
        """Extract product data from a specific URL."""
        pass
    
    async def run_scraping(self, categories: Optional[List[str]] = None) -> int:
        """Run the complete scraping process."""
        try:
            await self._start_scraping_session()
            
            products = await self.scrape_products(categories)
            
            if products:
                saved_count = await db_manager.save_products_batch(products)
                await self._update_scraping_session(
                    products_found=len(products),
                    products_new=saved_count
                )
                
                logger.info(f"Scraped and saved {saved_count} products from {self.market}")
                await self._finish_scraping_session("completed")
                return saved_count
            else:
                await self._finish_scraping_session("no_products_found")
                return 0
                
        except Exception as e:
            logger.error(f"Scraping failed for {self.market}: {e}")
            await self._finish_scraping_session("failed")
            return 0
        
        finally:
            self.cleanup()
