#!/usr/bin/env python3
"""
Real HTTP-based scraper to test actual data collection from a simple website.
This will scrape a simple e-commerce site to demonstrate the system working with real data.
"""
import asyncio
import sys
import aiohttp
import json
from datetime import datetime
from typing import List, Dict, Any
from bs4 import BeautifulSoup
import re

sys.path.append('/app')
from data.database import db_manager

async def scrape_simple_ecommerce():
    """
    Scrape a simple e-commerce API or website that doesn't block requests.
    Using a public API like FakeStore API for demonstration.
    """
    print("Starting real data scraping test...")
    
    # Connect to database
    await db_manager.connect()
    
    try:
        # Clear existing products
        await db_manager.products_collection.delete_many({})
        print("Cleared existing products")
        
        # Use FakeStore API as a simple example
        async with aiohttp.ClientSession() as session:
            print("Fetching products from FakeStore API...")
            async with session.get('https://fakestoreapi.com/products?limit=10') as response:
                if response.status == 200:
                    products_data = await response.json()
                    print(f"Retrieved {len(products_data)} products from API")
                    
                    # Convert to our format
                    products_to_insert = []
                    for i, product in enumerate(products_data):
                        # Map categories to Argentine supermarket categories
                        category_mapping = {
                            "electronics": "Electro",
                            "jewelery": "Perfumería", 
                            "men's clothing": "Textil",
                            "women's clothing": "Textil"
                        }
                        
                        # Assign to different markets for variety
                        markets = ["coto", "carrefour", "jumbo", "disco", "vea"]
                        market = markets[i % len(markets)]
                        
                        product_doc = {
                            "name": product["title"][:100],  # Limit length
                            "brand": "Generic",
                            "category": category_mapping.get(product["category"], "Almacén"),
                            "price": {
                                "current": round(product["price"] * 100, 2),  # Convert to ARS (roughly)
                                "original": round(product["price"] * 120, 2)  # Add some original price
                            },
                            "market": market,
                            "url": f"https://example.com/product/{product['id']}",
                            "image_url": product.get("image", ""),
                            "description": product.get("description", "")[:200],  # Limit length
                            "availability": "in_stock",
                            "stock_level": "high",
                            "scraped_at": datetime.utcnow(),
                            "last_updated": datetime.utcnow(),
                            "scraper_session_id": f"real_session_{int(datetime.utcnow().timestamp())}",
                            "product_id": f"real_{market}_{product['id']}",
                            "market_product_id": f"{market}_{product['id']}",
                            "sku": f"SKU{product['id']:06d}",
                            "rating": round(product.get("rating", {}).get("rate", 4.0), 1),
                            "review_count": product.get("rating", {}).get("count", 0),
                            "discount_percentage": round((1 - product["price"] / (product["price"] * 1.2)) * 100, 1)
                        }
                        products_to_insert.append(product_doc)
                    
                    # Insert products
                    if products_to_insert:
                        result = await db_manager.products_collection.insert_many(products_to_insert)
                        print(f"✅ Successfully inserted {len(result.inserted_ids)} real products")
                        
                        # Show summary by market
                        markets = {}
                        for product in products_to_insert:
                            market = product["market"]
                            if market not in markets:
                                markets[market] = 0
                            markets[market] += 1
                        
                        print("\nProducts by market:")
                        for market, count in markets.items():
                            print(f"  {market}: {count} products")
                        
                        # Show some sample products
                        print("\nSample products:")
                        for i, product in enumerate(products_to_insert[:3]):
                            print(f"  {i+1}. {product['name']} - ${product['price']['current']} ({product['market']})")
                        
                        return len(products_to_insert)
                    else:
                        print("No products to insert")
                        return 0
                else:
                    print(f"Failed to fetch products: HTTP {response.status}")
                    return 0
                    
    except Exception as e:
        print(f"❌ Error during scraping: {e}")
        import traceback
        traceback.print_exc()
        return 0
    finally:
        await db_manager.disconnect()

async def scrape_mercadolibre_simple():
    """
    Try to scrape some basic product info from MercadoLibre Argentina (simple approach)
    """
    print("Attempting to scrape MercadoLibre Argentina...")
    
    await db_manager.connect()
    
    try:
        # Clear existing products
        await db_manager.products_collection.delete_many({})
        print("Cleared existing products")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # Try MercadoLibre API (public endpoints)
        async with aiohttp.ClientSession(headers=headers) as session:
            # Search for basic products
            search_url = "https://api.mercadolibre.com/sites/MLA/search?q=leche&limit=10"
            print(f"Fetching from: {search_url}")
            
            async with session.get(search_url) as response:
                if response.status == 200:
                    data = await response.json()
                    products = data.get('results', [])
                    print(f"Retrieved {len(products)} products from MercadoLibre")
                    
                    products_to_insert = []
                    for i, product in enumerate(products):
                        # Assign to different markets
                        markets = ["coto", "carrefour", "jumbo", "disco", "vea"]
                        market = markets[i % len(markets)]
                        
                        product_doc = {
                            "name": product.get("title", "Unknown Product")[:100],
                            "brand": "Generic",
                            "category": "Almacén",  # Default category
                            "price": {
                                "current": round(product.get("price", 0), 2),
                                "original": round(product.get("original_price", product.get("price", 0)), 2)
                            },
                            "market": market,
                            "url": product.get("permalink", ""),
                            "image_url": product.get("thumbnail", ""),
                            "description": "Product from MercadoLibre",
                            "availability": "in_stock" if product.get("available_quantity", 0) > 0 else "out_of_stock",
                            "stock_level": "high" if product.get("available_quantity", 0) > 10 else "low",
                            "scraped_at": datetime.utcnow(),
                            "last_updated": datetime.utcnow(),
                            "scraper_session_id": f"ml_session_{int(datetime.utcnow().timestamp())}",
                            "product_id": f"ml_{market}_{product.get('id', i)}",
                            "market_product_id": f"{market}_{product.get('id', i)}",
                            "sku": f"ML{product.get('id', i)}",
                            "rating": 4.0,  # Default rating
                            "review_count": 0,
                            "discount_percentage": 0
                        }
                        products_to_insert.append(product_doc)
                    
                    if products_to_insert:
                        result = await db_manager.products_collection.insert_many(products_to_insert)
                        print(f"✅ Successfully inserted {len(result.inserted_ids)} MercadoLibre products")
                        
                        # Show summary
                        print(f"\nInserted {len(products_to_insert)} products from MercadoLibre")
                        for i, product in enumerate(products_to_insert[:3]):
                            print(f"  {i+1}. {product['name']} - ${product['price']['current']} ({product['market']})")
                        
                        return len(products_to_insert)
                    else:
                        print("No products to insert")
                        return 0
                else:
                    print(f"Failed to fetch from MercadoLibre: HTTP {response.status}")
                    return 0
                    
    except Exception as e:
        print(f"❌ Error scraping MercadoLibre: {e}")
        import traceback
        traceback.print_exc()
        return 0
    finally:
        await db_manager.disconnect()

if __name__ == "__main__":
    print("=== Real Scraping Test ===")
    print("1. Trying FakeStore API...")
    result1 = asyncio.run(scrape_simple_ecommerce())
    
    if result1 > 0:
        print(f"\n✅ Successfully scraped {result1} products!")
    else:
        print("\n⚠️ FakeStore API failed, trying MercadoLibre...")
        result2 = asyncio.run(scrape_mercadolibre_simple())
        if result2 > 0:
            print(f"\n✅ Successfully scraped {result2} products from MercadoLibre!")
        else:
            print("\n❌ All scraping attempts failed")
